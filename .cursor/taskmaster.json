{"projectName": "SafeScan", "projectDescription": "A Flutter app for scanning product ingredients and analyzing safety", "defaultModel": "claude-3.5-sonnet", "taskFile": ".vscode/tasks.md", "includePatterns": ["**/*.dart", "**/*.yaml", "**/*.yml", "**/*.md", "**/*.kt", "**/*.swift", "**/*.gradle", "**/*.gradle.kts", "**/*.sql"], "excludePatterns": ["**/build/**", "**/.dart_tool/**", "**/node_modules/**", "**/.gradle/**", "**/ios/Pods/**", "**/android/.gradle/**"], "contextFiles": ["pubspec.yaml", "lib/main.dart", "README.md", "lib/services/supabase_service.dart", "lib/services/gemini_service.dart", "lib/services/safety_analysis_service.dart"], "taskCategories": ["High Priority", "Medium Priority", "Low Priority", "Bugs to Fix", "Completed"]}