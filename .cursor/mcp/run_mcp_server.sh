#!/bin/bash

# Run Cursor MCP Server for SafeScan
# This script starts the MCP server for Supabase integration

echo "🚀 Starting SafeScan MCP Server..."

# Create logs directory if it doesn't exist
mkdir -p .cursor/mcp/logs

# Check if the http package is installed
if ! dart pub deps | grep -q "http:"; then
  echo "📦 Installing required packages..."
  dart pub add http
fi

# Run the server
echo "🔄 Running MCP server..."
dart .cursor/mcp/server.dart 