# SafeScan MCP Server for Cursor

This directory contains the Multi-Channel Protocol (MCP) server configuration and scripts for the SafeScan project.

## Overview

The MCP server manages the connection between the Cursor IDE and the Supabase backend. It verifies database tables, schemas, and handles connection status for the SafeScan application.

## Files

- `config.json` - Configuration for the MCP server
- `server.dart` - Main MCP server implementation
- `schema_verifier.dart` - Tool to verify database schemas
- `run_server.dart` - <PERSON><PERSON> script to run the server
- `run_mcp_server.sh` - Shell script to run the server

## Running the Server

You can run the server using one of the following methods:

### Using the Shell Script

```bash
./.cursor/mcp/run_mcp_server.sh
```

### Using Dart Directly

```bash
dart .cursor/mcp/run_server.dart
```

## Verifying the Database

To verify the database schema without starting the server:

```bash
dart .cursor/mcp/schema_verifier.dart
```

This will generate a verification report in `.cursor/mcp/verification_report.json`.

## Required Tables

The MCP server checks for the following required tables:

- `products`
- `ingredients`
- `product_ingredients`
- `saved_products`
- `user_preferences`

## Optional Tables

The server also checks for these optional tables:

- `allergens`
- `dietary_preferences`
- `avoided_ingredients`
- `database_version`

## Troubleshooting

If you encounter issues with the MCP server:

1. Check the logs in `.cursor/mcp/logs/mcp_server.log`
2. Verify your Supabase credentials in `config.json`
3. Run the schema verifier to check for database issues
4. Ensure the required database tables exist

## Status Indicator

The server creates a status file at `.cursor/mcp/status.json` that indicates whether the server is running and any error messages. 