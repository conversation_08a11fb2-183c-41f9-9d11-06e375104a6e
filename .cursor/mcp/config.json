{"mcp_version": "1.0.0", "project_name": "SafeScan", "supabase": {"url": "https://lwnfzmvqahraputvfkos.supabase.co", "anon_key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao", "auto_connect": true, "debug_mode": true}, "database": {"required_tables": ["products", "ingredients", "product_ingredients", "saved_products", "user_preferences"], "optional_tables": ["allergens", "dietary_preferences", "avoided_ingredients", "database_version"], "auto_create_missing": false}, "verification": {"on_startup": true, "verify_schemas": true, "auto_repair": false}, "logging": {"enabled": true, "level": "info", "file_output": ".cursor/mcp/logs/mcp_server.log"}, "cursor_integration": {"enable_commands": true, "status_bar_indicator": true}, "schemas": {"products": {"required_fields": ["id", "name", "safety_score", "safety_rating", "user_id", "created_at", "updated_at"], "optional_fields": ["brand", "barcode", "image", "ingredients_json", "alternatives_json", "scanned_at"]}, "ingredients": {"required_fields": ["id", "name", "safety_level", "created_at", "updated_at"], "optional_fields": ["category", "description", "scientific_name", "common_aliases", "health_risks"]}, "user_preferences": {"required_fields": ["id", "user_id", "created_at", "updated_at"], "optional_fields": ["allergens", "avoided_ingredients", "is_premium", "last_login"]}}}