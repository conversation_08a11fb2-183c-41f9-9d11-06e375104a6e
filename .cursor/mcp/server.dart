import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Cursor MCP Server for SafeScan Project
/// This server manages the connection between Cursor IDE and the Supabase backend

class CursorMCPServer {
  final String configPath;
  late Map<String, dynamic> config;
  String? supabaseUrl;
  String? supabaseKey;
  
  final _statusStreamController = StreamController<String>.broadcast();
  Stream<String> get statusStream => _statusStreamController.stream;
  
  bool _isRunning = false;
  bool get isRunning => _isRunning;
  
  Directory? _logDirectory;
  File? _logFile;
  
  CursorMCPServer(this.configPath);
  
  Future<void> initialize() async {
    try {
      final configFile = File(configPath);
      if (await configFile.exists()) {
        final configContent = await configFile.readAsString();
        config = json.decode(configContent);
        
        supabaseUrl = config['supabase']['url'];
        supabaseKey = config['supabase']['anon_key'];
        
        // Initialize logging
        if (config['logging']['enabled'] == true) {
          final logPath = config['logging']['file_output'];
          if (logPath != null) {
            _logDirectory = Directory(logPath.substring(0, logPath.lastIndexOf('/')));
            if (!await _logDirectory!.exists()) {
              await _logDirectory!.create(recursive: true);
            }
            _logFile = File(logPath);
          }
        }
        
        _log('MCP Server initialized with configuration from $configPath');
        _statusStreamController.add('initialized');
      } else {
        throw Exception('Config file not found at $configPath');
      }
    } catch (e) {
      _log('Error initializing MCP Server: $e', isError: true);
      _statusStreamController.add('error');
      rethrow;
    }
  }
  
  Future<void> start() async {
    if (_isRunning) {
      _log('MCP Server is already running');
      return;
    }
    
    try {
      _isRunning = true;
      _statusStreamController.add('starting');
      
      // Test connection to Supabase
      final isConnected = await _testConnection();
      if (!isConnected) {
        throw Exception('Failed to connect to Supabase');
      }
      
      _log('Successfully connected to Supabase');
      
      // Verify database if configured
      if (config['verification']['on_startup'] == true) {
        await _verifyDatabase();
      }
      
      // Create status indicator file for Cursor
      await _createStatusFile(true);
      
      _log('MCP Server started successfully');
      _statusStreamController.add('running');
    } catch (e) {
      _isRunning = false;
      _log('Error starting MCP Server: $e', isError: true);
      _statusStreamController.add('error');
      
      // Create status indicator file for Cursor with error
      await _createStatusFile(false, errorMessage: e.toString());
      rethrow;
    }
  }
  
  Future<void> stop() async {
    if (!_isRunning) {
      _log('MCP Server is not running');
      return;
    }
    
    try {
      _isRunning = false;
      _statusStreamController.add('stopping');
      
      // Cleanup status file
      await _createStatusFile(false);
      
      _log('MCP Server stopped successfully');
      _statusStreamController.add('stopped');
    } catch (e) {
      _log('Error stopping MCP Server: $e', isError: true);
      _statusStreamController.add('error');
      rethrow;
    }
  }
  
  Future<bool> _testConnection() async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/');
      final response = await http.get(
        url,
        headers: {
          'apikey': supabaseKey!,
          'Authorization': 'Bearer $supabaseKey',
        },
      );
      
      return response.statusCode == 200;
    } catch (e) {
      _log('Error testing connection: $e', isError: true);
      return false;
    }
  }
  
  Future<void> _verifyDatabase() async {
    _log('Verifying database tables and schemas...');
    
    try {
      // Verify required tables
      final requiredTables = config['database']['required_tables'] as List<dynamic>;
      final missingTables = <String>[];
      
      for (final tableName in requiredTables) {
        final exists = await _checkTableExists(tableName.toString());
        if (!exists) {
          missingTables.add(tableName.toString());
        }
      }
      
      if (missingTables.isNotEmpty) {
        _log('Missing required tables: ${missingTables.join(', ')}', isError: true);
        
        if (config['database']['auto_create_missing'] == true) {
          _log('Auto-creating missing tables is enabled but not implemented yet');
        }
      } else {
        _log('All required tables exist');
      }
      
      // Verify schemas if configured
      if (config['verification']['verify_schemas'] == true) {
        final schemas = config['schemas'] as Map<String, dynamic>;
        final schemaIssues = <String>[];
        
        for (final entry in schemas.entries) {
          final tableName = entry.key;
          final schemaConfig = entry.value as Map<String, dynamic>;
          final requiredFields = schemaConfig['required_fields'] as List<dynamic>;
          
          final tableExists = await _checkTableExists(tableName);
          if (tableExists) {
            final tableInfo = await _getTableColumns(tableName);
            
            for (final field in requiredFields) {
              final fieldExists = tableInfo.containsKey(field.toString());
              if (!fieldExists) {
                schemaIssues.add('$tableName.${field.toString()}');
              }
            }
          }
        }
        
        if (schemaIssues.isNotEmpty) {
          _log('Schema issues found: ${schemaIssues.join(', ')}', isError: true);
        } else {
          _log('All schemas valid');
        }
      }
    } catch (e) {
      _log('Error verifying database: $e', isError: true);
    }
  }
  
  Future<bool> _checkTableExists(String tableName) async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?select=id&limit=0');
      final response = await http.get(
        url,
        headers: {
          'apikey': supabaseKey!,
          'Authorization': 'Bearer $supabaseKey',
        },
      );
      
      return response.statusCode == 200;
    } catch (e) {
      _log('Error checking if table $tableName exists: $e', isError: true);
      return false;
    }
  }
  
  Future<Map<String, String>> _getTableColumns(String tableName) async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?limit=1');
      final response = await http.get(
        url,
        headers: {
          'apikey': supabaseKey!,
          'Authorization': 'Bearer $supabaseKey',
        },
      );
      
      if (response.statusCode == 200 && response.body.isNotEmpty) {
        final List<dynamic> data = json.decode(response.body);
        
        if (data.isNotEmpty) {
          final Map<String, dynamic> firstRow = data.first;
          final Map<String, String> columns = {};
          
          firstRow.forEach((key, value) {
            columns[key] = value?.runtimeType.toString() ?? 'null';
          });
          
          return columns;
        }
      }
      
      return {};
    } catch (e) {
      _log('Error getting columns for table $tableName: $e', isError: true);
      return {};
    }
  }
  
  Future<void> _createStatusFile(bool isRunning, {String? errorMessage}) async {
    try {
      final statusFile = File('.cursor/mcp/status.json');
      final status = {
        'running': isRunning,
        'timestamp': DateTime.now().toIso8601String(),
        'project': config['project_name'],
        'error': errorMessage,
      };
      
      await statusFile.writeAsString(const JsonEncoder.withIndent('  ').convert(status));
    } catch (e) {
      _log('Error creating status file: $e', isError: true);
    }
  }
  
  void _log(String message, {bool isError = false}) {
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] ${isError ? 'ERROR' : 'INFO'}: $message';
    
    print(logMessage);
    
    if (_logFile != null) {
      _logFile!.writeAsStringSync('$logMessage\n', mode: FileMode.append);
    }
  }
  
  void dispose() {
    _statusStreamController.close();
  }
}

void main() async {
  final server = CursorMCPServer('.cursor/mcp/config.json');
  
  try {
    await server.initialize();
    await server.start();
    
    print('\n✅ Cursor MCP Server is running');
    print('Press Ctrl+C to stop the server');
    
    // Keep the server running until interrupted
    await Future.delayed(const Duration(days: 1));
  } catch (e) {
    print('\n❌ Failed to start Cursor MCP Server: $e');
  } finally {
    await server.stop();
    server.dispose();
  }
} 