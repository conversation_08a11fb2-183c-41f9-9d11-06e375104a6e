import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// SafeScan Database Verification for MCP
/// This script verifies the Supabase database structure for the SafeScan project

Future<void> main() async {
  print('🔍 SafeScan MCP Database Verification');
  print('====================================\n');
  
  try {
    // Load config
    final configFile = File('.cursor/mcp/config.json');
    if (!await configFile.exists()) {
      print('❌ Configuration file not found. Aborting.');
      exit(1);
    }
    
    final config = json.decode(await configFile.readAsString());
    final supabaseUrl = config['supabase']['url'];
    final supabaseKey = config['supabase']['anon_key'];
    
    print('🌐 Supabase URL: $supabaseUrl');
    
    // Test connection
    print('\n🔄 Testing connection to Supabase...');
    final connectionResult = await _testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful');
    
    // Check required tables
    final requiredTables = config['database']['required_tables'] as List;
    final optionalTables = config['database']['optional_tables'] as List;
    
    print('\n📋 Checking required tables:');
    final requiredResults = <String, bool>{};
    for (final table in requiredTables) {
      final exists = await _checkTableExists(supabaseUrl, supabaseKey, table.toString());
      requiredResults[table.toString()] = exists;
      print('  ${exists ? '✅' : '❌'} $table');
    }
    
    print('\n📋 Checking optional tables:');
    final optionalResults = <String, bool>{};
    for (final table in optionalTables) {
      final exists = await _checkTableExists(supabaseUrl, supabaseKey, table.toString());
      optionalResults[table.toString()] = exists;
      print('  ${exists ? '✅' : '❌'} $table');
    }
    
    // Check schemas
    print('\n🔍 Checking table schemas:');
    final schemaResults = <String, Map<String, bool>>{};
    final schemas = config['schemas'] as Map<String, dynamic>;
    
    for (final entry in schemas.entries) {
      final tableName = entry.key;
      final schemaConfig = entry.value as Map<String, dynamic>;
      final requiredFields = schemaConfig['required_fields'] as List;
      
      print('\n  Table: $tableName');
      final fieldResults = <String, bool>{};
      
      final exists = await _checkTableExists(supabaseUrl, supabaseKey, tableName);
      if (exists) {
        final columns = await _getTableColumns(supabaseUrl, supabaseKey, tableName);
        
        for (final field in requiredFields) {
          final fieldExists = columns.contains(field.toString());
          fieldResults[field.toString()] = fieldExists;
          print('    ${fieldExists ? '✅' : '❌'} ${field.toString()}');
        }
      } else {
        for (final field in requiredFields) {
          fieldResults[field.toString()] = false;
          print('    ❌ ${field.toString()} (table missing)');
        }
      }
      
      schemaResults[tableName] = fieldResults;
    }
    
    // Generate verification report
    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'connection': connectionResult,
      'required_tables': requiredResults,
      'optional_tables': optionalResults,
      'schemas': schemaResults,
      'summary': {
        'all_required_present': requiredResults.values.every((v) => v),
        'optional_tables_present': optionalResults.values.where((v) => v).length,
        'optional_tables_total': optionalResults.length,
        'all_schemas_valid': schemaResults.entries.every((entry) => 
          entry.value.values.every((fieldExists) => fieldExists)
        )
      }
    };
    
    // Save report
    final reportFile = File('.cursor/mcp/verification_report.json');
    await reportFile.writeAsString(const JsonEncoder.withIndent('  ').convert(report));
    print('\n📝 Verification report saved to .cursor/mcp/verification_report.json');
    
    // Generate recommendations
    print('\n🔧 Recommendations:');
    final missingRequired = requiredResults.entries.where((e) => !e.value).map((e) => e.key).toList();
    if (missingRequired.isNotEmpty) {
      print('  ❗ Create missing required tables: ${missingRequired.join(', ')}');
      print('  👉 Run the SQL migrations in Supabase/migrations/ to create these tables');
    }
    
    final missingOptional = optionalResults.entries.where((e) => !e.value).map((e) => e.key).toList();
    if (missingOptional.isNotEmpty) {
      print('  ⚠️ Consider creating optional tables: ${missingOptional.join(', ')}');
      print('  👉 These tables provide additional functionality but are not critical');
    }
    
    final schemaIssues = <String>[];
    for (final entry in schemaResults.entries) {
      final tableName = entry.key;
      final fieldResults = entry.value;
      
      for (final fieldEntry in fieldResults.entries) {
        if (!fieldEntry.value) {
          schemaIssues.add('$tableName.${fieldEntry.key}');
        }
      }
    }
    
    if (schemaIssues.isNotEmpty) {
      print('  ❗ Fix schema issues: ${schemaIssues.join(', ')}');
      print('  👉 These fields are missing from their respective tables');
    }
    
    // Final assessment
    final allGood = missingRequired.isEmpty && schemaIssues.isEmpty;
    print('\n${allGood ? '✅ Database is properly configured for SafeScan MCP!' : '❌ Database requires fixes before it can be used with SafeScan MCP'}');
    
  } catch (e) {
    print('❌ Error during verification: $e');
    exit(1);
  }
}

Future<bool> _testConnection(String url, String key) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 10));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error testing connection: $e');
    return false;
  }
}

Future<bool> _checkTableExists(String url, String key, String tableName) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/$tableName?select=id&limit=0'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 5));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error checking table $tableName: $e');
    return false;
  }
}

Future<List<String>> _getTableColumns(String url, String key, String tableName) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/$tableName?limit=1'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 5));
    
    if (response.statusCode == 200 && response.body.isNotEmpty) {
      final List<dynamic> data = json.decode(response.body);
      
      if (data.isNotEmpty) {
        final Map<String, dynamic> firstRow = data.first;
        return firstRow.keys.toList();
      }
    }
    
    return [];
  } catch (e) {
    print('  Error getting columns for $tableName: $e');
    return [];
  }
} 