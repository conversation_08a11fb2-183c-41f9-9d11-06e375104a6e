import 'dart:io';

/// Simple script to run the Cursor MCP Server
/// Usage: dart .cursor/mcp/run_server.dart

void main() async {
  print('🚀 Starting Cursor MCP Server for SafeScan...');
  
  // Make sure the MCP directory exists
  final mcpDir = Directory('.cursor/mcp');
  if (!await mcpDir.exists()) {
    print('❌ MCP directory does not exist. Creating it...');
    await mcpDir.create(recursive: true);
  }
  
  // Create logs directory if it doesn't exist
  final logsDir = Directory('.cursor/mcp/logs');
  if (!await logsDir.exists()) {
    await logsDir.create(recursive: true);
  }
  
  // Check if the config file exists
  final configFile = File('.cursor/mcp/config.json');
  if (!await configFile.exists()) {
    print('❌ Configuration file not found at .cursor/mcp/config.json');
    exit(1);
  }
  
  // Check if server.dart exists
  final serverFile = File('.cursor/mcp/server.dart');
  if (!await serverFile.exists()) {
    print('❌ Server file not found at .cursor/mcp/server.dart');
    exit(1);
  }
  
  // Run the server
  print('🔄 Running MCP server...');
  
  final process = await Process.start(
    'dart',
    ['.cursor/mcp/server.dart'],
    workingDirectory: Directory.current.path,
    mode: ProcessStartMode.inheritStdio,
  );
  
  // Handle server exit
  final exitCode = await process.exitCode;
  
  if (exitCode == 0) {
    print('✅ MCP server exited successfully');
  } else {
    print('❌ MCP server exited with code $exitCode');
    exit(exitCode);
  }
} 