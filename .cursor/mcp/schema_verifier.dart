import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

// MCP Schema Verifier for Cursor
// This script validates the Supabase database schemas against the project requirements

class MCPSchemaVerifier {
  final String configPath;
  late Map<String, dynamic> config;
  String? supabaseUrl;
  String? supabaseKey;
  
  MCPSchemaVerifier(this.configPath);
  
  Future<void> initialize() async {
    try {
      final configFile = File(configPath);
      if (await configFile.exists()) {
        final configContent = await configFile.readAsString();
        config = json.decode(configContent);
        
        supabaseUrl = config['supabase']['url'];
        supabaseKey = config['supabase']['anon_key'];
        
        print('MCP Schema Verifier initialized with configuration from $configPath');
      } else {
        throw Exception('Config file not found at $configPath');
      }
    } catch (e) {
      print('Error initializing MCP Schema Verifier: $e');
      rethrow;
    }
  }
  
  Future<Map<String, bool>> verifyRequiredTables() async {
    final results = <String, bool>{};
    final requiredTables = config['database']['required_tables'] as List<dynamic>;
    
    print('\n🔍 Verifying Required Tables...');
    
    for (final tableName in requiredTables) {
      final exists = await _checkTableExists(tableName.toString());
      results[tableName.toString()] = exists;
      print('  ${exists ? '✅' : '❌'} $tableName');
    }
    
    return results;
  }
  
  Future<Map<String, bool>> verifyOptionalTables() async {
    final results = <String, bool>{};
    final optionalTables = config['database']['optional_tables'] as List<dynamic>;
    
    print('\n🔍 Verifying Optional Tables...');
    
    for (final tableName in optionalTables) {
      final exists = await _checkTableExists(tableName.toString());
      results[tableName.toString()] = exists;
      print('  ${exists ? '✅' : '❌'} $tableName');
    }
    
    return results;
  }
  
  Future<Map<String, Map<String, bool>>> verifyTableSchemas() async {
    final results = <String, Map<String, bool>>{};
    final schemas = config['schemas'] as Map<String, dynamic>;
    
    print('\n🔍 Verifying Table Schemas...');
    
    for (final entry in schemas.entries) {
      final tableName = entry.key;
      final schemaConfig = entry.value as Map<String, dynamic>;
      final requiredFields = schemaConfig['required_fields'] as List<dynamic>;
      
      print('\n  Table: $tableName');
      
      final fieldResults = <String, bool>{};
      final tableExists = await _checkTableExists(tableName);
      
      if (tableExists) {
        final tableInfo = await _getTableColumns(tableName);
        
        for (final field in requiredFields) {
          final fieldExists = tableInfo.containsKey(field.toString());
          fieldResults[field.toString()] = fieldExists;
          print('    ${fieldExists ? '✅' : '❌'} ${field.toString()}');
        }
      } else {
        for (final field in requiredFields) {
          fieldResults[field.toString()] = false;
          print('    ❌ ${field.toString()} (table missing)');
        }
      }
      
      results[tableName] = fieldResults;
    }
    
    return results;
  }
  
  Future<bool> _checkTableExists(String tableName) async {
    try {
      final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?select=id&limit=0');
      final response = await http.get(
        url,
        headers: {
          'apikey': supabaseKey!,
          'Authorization': 'Bearer $supabaseKey',
        },
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('Error checking if table $tableName exists: $e');
      return false;
    }
  }
  
  Future<Map<String, String>> _getTableColumns(String tableName) async {
    try {
      // First, get a single row to infer the schema
      final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?limit=1');
      final response = await http.get(
        url,
        headers: {
          'apikey': supabaseKey!,
          'Authorization': 'Bearer $supabaseKey',
        },
      );
      
      if (response.statusCode == 200 && response.body.isNotEmpty) {
        final List<dynamic> data = json.decode(response.body);
        
        if (data.isNotEmpty) {
          final Map<String, dynamic> firstRow = data.first;
          final Map<String, String> columns = {};
          
          firstRow.forEach((key, value) {
            columns[key] = value?.runtimeType.toString() ?? 'null';
          });
          
          return columns;
        }
      }
      
      // If we can't get a row, try to get the definition (requires rpc function)
      // This is a fallback and may not work on all Supabase instances
      return {};
    } catch (e) {
      print('Error getting columns for table $tableName: $e');
      return {};
    }
  }
  
  void generateReport(Map<String, bool> requiredTables, Map<String, bool> optionalTables, Map<String, Map<String, bool>> schemas) {
    final reportPath = '.cursor/mcp/verification_report.json';
    final report = {
      'timestamp': DateTime.now().toIso8601String(),
      'required_tables': requiredTables,
      'optional_tables': optionalTables,
      'schemas': schemas,
      'summary': {
        'required_tables_present': requiredTables.values.every((v) => v),
        'optional_tables_present': optionalTables.values.where((v) => v).length,
        'optional_tables_total': optionalTables.length,
        'schemas_valid': schemas.entries.every((e) => e.value.values.every((v) => v)),
      }
    };
    
    try {
      final reportFile = File(reportPath);
      reportFile.writeAsStringSync(const JsonEncoder.withIndent('  ').convert(report));
      print('\n📊 Verification report generated at $reportPath');
    } catch (e) {
      print('Error generating report: $e');
    }
    
    // Print summary
    print('\n📋 Verification Summary:');
    print('  Required Tables: ${requiredTables.values.every((v) => v) ? '✅ All Present' : '❌ Some Missing'}');
    print('  Optional Tables: ${optionalTables.values.where((v) => v).length}/${optionalTables.length} Present');
    print('  Schemas: ${schemas.entries.every((e) => e.value.values.every((v) => v)) ? '✅ All Valid' : '❌ Some Invalid'}');
    
    // Check if the database is ready for use
    final isReady = requiredTables.values.every((v) => v) && 
                    schemas.entries.every((e) => e.value.values.every((v) => v));
    
    print('\n${isReady ? '✅ MCP Database is READY for use!' : '❌ MCP Database is NOT READY - fixes required!'}');
  }
}

void main() async {
  final verifier = MCPSchemaVerifier('.cursor/mcp/config.json');
  
  try {
    await verifier.initialize();
    
    final requiredTables = await verifier.verifyRequiredTables();
    final optionalTables = await verifier.verifyOptionalTables();
    final schemas = await verifier.verifyTableSchemas();
    
    verifier.generateReport(requiredTables, optionalTables, schemas);
  } catch (e) {
    print('Error in schema verification: $e');
  }
} 