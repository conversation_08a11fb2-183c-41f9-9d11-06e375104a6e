import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Direct SQL execution script
/// This applies SQL changes directly to fix the database schema

Future<void> main() async {
  print('🔄 Direct SQL Migration for SafeScan');
  print('================================\n');
  
  try {
    // Load config
    final configFile = File('.cursor/mcp/config.json');
    if (!await configFile.exists()) {
      print('❌ Configuration file not found. Aborting.');
      exit(1);
    }
    
    final config = json.decode(await configFile.readAsString());
    final supabaseUrl = config['supabase']['url'];
    final supabaseKey = config['supabase']['anon_key'];
    
    // Test connection
    print('🔄 Testing connection to Supabase...');
    final connectionResult = await testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful\n');
    
    // Since we can't execute SQL directly through the REST API without an RPC function,
    // we need to provide alternative guidance to the user.
    
    print('📝 To fix the database schema, please follow these instructions:');
    print('\n1. Log in to your Supabase dashboard at https://app.supabase.com');
    print('2. Navigate to your project: SafeScan');
    print('3. Go to the SQL Editor (on the left sidebar)');
    print('4. Create a new query');
    print('5. Copy and paste the SQL from Supabase/migrations/fix_schema_issues.sql');
    print('6. Run the SQL query');
    print('\nAfter running the SQL, come back and verify the changes with:');
    print('dart .cursor/mcp/verify_database.dart');
    
    // Print the SQL file path
    print('\nSQL file path: ${Directory.current.path}/Supabase/migrations/fix_schema_issues.sql');
    
    // Get the current SQL file contents
    final sqlFile = File('Supabase/migrations/fix_schema_issues.sql');
    if (await sqlFile.exists()) {
      print('\n🔍 SQL file is ready to be executed');
    } else {
      print('\n❌ SQL file not found. Please check the path.');
    }
    
  } catch (e) {
    print('❌ Error during migration preparation: $e');
    exit(1);
  }
}

Future<bool> testConnection(String url, String key) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 10));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error testing connection: $e');
    return false;
  }
} 