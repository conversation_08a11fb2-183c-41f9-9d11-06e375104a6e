#!/bin/bash

# MCP Commands for SafeScan
# This script provides various MCP commands for Cursor integration

function print_help() {
  echo "SafeScan MCP Commands"
  echo "====================="
  echo ""
  echo "Usage: .cursor/mcp/mcp_commands.sh [command]"
  echo ""
  echo "Available commands:"
  echo "  start      - Start the MCP server"
  echo "  verify     - Verify database schema"
  echo "  fix        - Show instructions to fix database issues"
  echo "  status     - Check MCP server status"
  echo "  help       - Show this help message"
  echo ""
}

function start_server() {
  echo "🚀 Starting MCP server..."
  dart .cursor/mcp/run_server.dart
}

function verify_database() {
  echo "🔍 Verifying database schema..."
  dart .cursor/mcp/verify_database.dart
}

function fix_database() {
  echo "🔧 Database Fix Instructions"
  echo "==========================="
  echo ""
  echo "To fix the database schema, follow these steps:"
  echo ""
  echo "1. Log in to your Supabase dashboard at https://app.supabase.com"
  echo "2. Navigate to your project: SafeScan"
  echo "3. Go to the SQL Editor (on the left sidebar)"
  echo "4. Create a new query"
  echo "5. Copy and paste the SQL from this file: md-files/database_migration_guide.md"
  echo "6. Run the SQL query"
  echo ""
  echo "For detailed instructions, refer to: md-files/database_migration_guide.md"
  echo ""
  echo "After running the SQL, come back and verify the changes with:"
  echo "  .cursor/mcp/mcp_commands.sh verify"
}

function check_status() {
  if [ -f ".cursor/mcp/status.json" ]; then
    echo "MCP Server Status:"
    cat .cursor/mcp/status.json
  else
    echo "❌ MCP server is not running or status file not found"
  fi
}

# Process command
case "$1" in
  "start")
    start_server
    ;;
  "verify")
    verify_database
    ;;
  "fix")
    fix_database
    ;;
  "status")
    check_status
    ;;
  "help" | *)
    print_help
    ;;
esac 