import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// A simplified script to apply SQL migration to Supabase
/// This applies the necessary database schema changes for SafeScan MCP

Future<void> main() async {
  print('🔄 Applying SQL Migration to Supabase');
  print('==================================\n');
  
  try {
    // Load config
    final configFile = File('.cursor/mcp/config.json');
    if (!await configFile.exists()) {
      print('❌ Configuration file not found. Aborting.');
      exit(1);
    }
    
    final config = json.decode(await configFile.readAsString());
    final supabaseUrl = config['supabase']['url'];
    final supabaseKey = config['supabase']['anon_key'];
    
    // Test connection
    print('🔄 Testing connection to Supabase...');
    final connectionResult = await testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful\n');
    
    // Apply products table fixes
    print('🔄 Fixing products table...');
    final productsResult = await applyProductsFixes(supabaseUrl, supabaseKey);
    print(productsResult ? '✅ Products table fixed' : '❌ Failed to fix products table');
    
    // Apply user_preferences table fixes
    print('\n🔄 Fixing user_preferences table...');
    final preferencesResult = await applyPreferencesFixes(supabaseUrl, supabaseKey);
    print(preferencesResult ? '✅ User preferences table fixed' : '❌ Failed to fix user preferences table');
    
    // Create optional tables
    print('\n🔄 Creating optional tables...');
    final optionalResult = await createOptionalTables(supabaseUrl, supabaseKey);
    print(optionalResult ? '✅ Optional tables created' : '❌ Failed to create optional tables');
    
    print('\n🔄 Verifying changes...');
    print('Please run dart .cursor/mcp/verify_database.dart to verify the changes');
    
  } catch (e) {
    print('❌ Error applying migration: $e');
    exit(1);
  }
}

Future<bool> testConnection(String url, String key) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 10));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error testing connection: $e');
    return false;
  }
}

Future<bool> applyProductsFixes(String url, String key) async {
  try {
    // Check if products table exists
    final checkResult = await http.get(
      Uri.parse('$url/rest/v1/products?limit=0'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    );
    
    if (checkResult.statusCode != 200) {
      print('  Creating products table...');
      // Create products table
      final createResult = await http.post(
        Uri.parse('$url/rest/v1/rpc/execute_sql'),
        headers: {
          'apikey': key,
          'Authorization': 'Bearer $key',
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'sql': '''
            CREATE TABLE IF NOT EXISTS public.products (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              name VARCHAR(255) NOT NULL,
              safety_score INTEGER DEFAULT 0,
              safety_rating VARCHAR(50) DEFAULT 'unknown',
              user_id UUID REFERENCES auth.users(id),
              brand VARCHAR(255),
              barcode VARCHAR(100),
              image TEXT,
              ingredients_json JSONB,
              alternatives_json JSONB,
              scanned_at TIMESTAMP WITH TIME ZONE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
          '''
        }),
      );
      
      if (createResult.statusCode != 200) {
        print('  Failed to create products table: ${createResult.body}');
        return false;
      }
    } else {
      print('  Products table exists, adding missing columns...');
      // Add missing columns
      final columnsResult = await http.post(
        Uri.parse('$url/rest/v1/rpc/execute_sql'),
        headers: {
          'apikey': key,
          'Authorization': 'Bearer $key',
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'sql': '''
            ALTER TABLE public.products 
              ADD COLUMN IF NOT EXISTS id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              ADD COLUMN IF NOT EXISTS name VARCHAR(255),
              ADD COLUMN IF NOT EXISTS safety_score INTEGER DEFAULT 0,
              ADD COLUMN IF NOT EXISTS safety_rating VARCHAR(50) DEFAULT 'unknown',
              ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id),
              ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
          '''
        }),
      );
      
      if (columnsResult.statusCode != 200) {
        print('  Failed to add columns to products table: ${columnsResult.body}');
        return false;
      }
    }
    
    return true;
  } catch (e) {
    print('  Error fixing products table: $e');
    return false;
  }
}

Future<bool> applyPreferencesFixes(String url, String key) async {
  try {
    // Check if user_preferences table exists
    final checkResult = await http.get(
      Uri.parse('$url/rest/v1/user_preferences?limit=0'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    );
    
    if (checkResult.statusCode != 200) {
      print('  Creating user_preferences table...');
      // Create user_preferences table
      final createResult = await http.post(
        Uri.parse('$url/rest/v1/rpc/execute_sql'),
        headers: {
          'apikey': key,
          'Authorization': 'Bearer $key',
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'sql': '''
            CREATE TABLE IF NOT EXISTS public.user_preferences (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              user_id UUID REFERENCES auth.users(id),
              allergens JSONB,
              avoided_ingredients JSONB,
              is_premium BOOLEAN DEFAULT FALSE,
              last_login TIMESTAMP WITH TIME ZONE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
          '''
        }),
      );
      
      if (createResult.statusCode != 200) {
        print('  Failed to create user_preferences table: ${createResult.body}');
        return false;
      }
    } else {
      print('  User preferences table exists, adding missing columns...');
      // Add missing columns
      final columnsResult = await http.post(
        Uri.parse('$url/rest/v1/rpc/execute_sql'),
        headers: {
          'apikey': key,
          'Authorization': 'Bearer $key',
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal',
        },
        body: json.encode({
          'sql': '''
            ALTER TABLE public.user_preferences 
              ADD COLUMN IF NOT EXISTS id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id),
              ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
          '''
        }),
      );
      
      if (columnsResult.statusCode != 200) {
        print('  Failed to add columns to user_preferences table: ${columnsResult.body}');
        return false;
      }
    }
    
    return true;
  } catch (e) {
    print('  Error fixing user_preferences table: $e');
    return false;
  }
}

Future<bool> createOptionalTables(String url, String key) async {
  try {
    // Create allergens table
    print('  Creating allergens table...');
    final allergensResult = await http.post(
      Uri.parse('$url/rest/v1/rpc/execute_sql'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal',
      },
      body: json.encode({
        'sql': '''
          CREATE TABLE IF NOT EXISTS public.allergens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            severity VARCHAR(50) DEFAULT 'medium',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
          );
          
          INSERT INTO public.allergens (name, description, severity)
          VALUES 
            ('Peanuts', 'Legume commonly causing severe allergic reactions', 'high'),
            ('Tree Nuts', 'Includes almonds, walnuts, cashews, etc.', 'high'),
            ('Milk', 'Dairy products and milk derivatives', 'medium'),
            ('Eggs', 'Whole eggs and egg derivatives', 'medium'),
            ('Fish', 'Various fish species and products', 'high'),
            ('Shellfish', 'Includes shrimp, crab, lobster, etc.', 'high'),
            ('Soy', 'Soybeans and soy derivatives', 'medium'),
            ('Wheat', 'Wheat and wheat derivatives', 'medium'),
            ('Sesame', 'Sesame seeds and sesame oil', 'medium'),
            ('Gluten', 'Protein found in wheat, barley, rye', 'medium')
          ON CONFLICT (name) DO NOTHING;
        '''
      }),
    );
    
    if (allergensResult.statusCode != 200) {
      print('  Failed to create allergens table: ${allergensResult.body}');
      return false;
    }
    
    // Create dietary_preferences table
    print('  Creating dietary_preferences table...');
    final dietaryResult = await http.post(
      Uri.parse('$url/rest/v1/rpc/execute_sql'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal',
      },
      body: json.encode({
        'sql': '''
          CREATE TABLE IF NOT EXISTS public.dietary_preferences (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
          );
          
          INSERT INTO public.dietary_preferences (name, description)
          VALUES 
            ('Vegetarian', 'Avoids meat including beef, poultry, and seafood'),
            ('Vegan', 'Avoids all animal products including dairy, eggs, and honey'),
            ('Gluten-Free', 'Avoids wheat, barley, rye, and other gluten-containing ingredients'),
            ('Dairy-Free', 'Avoids milk and dairy products'),
            ('Keto', 'Low carbohydrate, high fat diet'),
            ('Paleo', 'Focuses on whole foods, avoids processed foods, grains, legumes, and dairy')
          ON CONFLICT (name) DO NOTHING;
        '''
      }),
    );
    
    if (dietaryResult.statusCode != 200) {
      print('  Failed to create dietary_preferences table: ${dietaryResult.body}');
      return false;
    }
    
    // Create avoided_ingredients table
    print('  Creating avoided_ingredients table...');
    final avoidedResult = await http.post(
      Uri.parse('$url/rest/v1/rpc/execute_sql'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal',
      },
      body: json.encode({
        'sql': '''
          CREATE TABLE IF NOT EXISTS public.avoided_ingredients (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            name VARCHAR(255) NOT NULL,
            reason TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(user_id, name)
          );
        '''
      }),
    );
    
    if (avoidedResult.statusCode != 200) {
      print('  Failed to create avoided_ingredients table: ${avoidedResult.body}');
      return false;
    }
    
    // Create database_version table
    print('  Creating database_version table...');
    final versionResult = await http.post(
      Uri.parse('$url/rest/v1/rpc/execute_sql'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal',
      },
      body: json.encode({
        'sql': '''
          CREATE TABLE IF NOT EXISTS public.database_version (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            version VARCHAR(50) NOT NULL,
            description TEXT,
            last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
          );
          
          INSERT INTO public.database_version (version, description)
          VALUES ('1.1.0', 'Fixed schema issues for MCP protocol support')
          ON CONFLICT DO NOTHING;
        '''
      }),
    );
    
    if (versionResult.statusCode != 200) {
      print('  Failed to create database_version table: ${versionResult.body}');
      return false;
    }
    
    return true;
  } catch (e) {
    print('  Error creating optional tables: $e');
    return false;
  }
} 