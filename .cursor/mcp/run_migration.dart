import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Script to run SQL migrations against Supabase
/// This applies the database fixes identified by the MCP verification

Future<void> main() async {
  print('🔄 Running SafeScan SQL Migration');
  print('===============================\n');
  
  try {
    // Load config
    final configFile = File('.cursor/mcp/config.json');
    if (!await configFile.exists()) {
      print('❌ Configuration file not found. Aborting.');
      exit(1);
    }
    
    final config = json.decode(await configFile.readAsString());
    final supabaseUrl = config['supabase']['url'];
    final supabaseKey = config['supabase']['anon_key'];
    
    // Load SQL file
    final sqlFile = File('Supabase/migrations/fix_schema_issues.sql');
    if (!await sqlFile.exists()) {
      print('❌ SQL migration file not found. Aborting.');
      exit(1);
    }
    
    final sqlContent = await sqlFile.readAsString();
    
    // Split SQL content into individual statements
    final statements = _splitSqlStatements(sqlContent);
    
    print('Found ${statements.length} SQL statements to execute');
    
    // Test connection
    print('\n🔄 Testing connection to Supabase...');
    final connectionResult = await _testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful');
    
    // Execute statements
    print('\n🔄 Executing SQL migration...\n');
    
    int successful = 0;
    int failed = 0;
    
    for (int i = 0; i < statements.length; i++) {
      final statement = statements[i];
      if (statement.trim().isEmpty) continue;
      
      print('  Statement ${i+1}/${statements.length}...');
      
      try {
        final result = await _executeSql(supabaseUrl, supabaseKey, statement);
        if (result) {
          successful++;
          print('  ✅ Success');
        } else {
          failed++;
          print('  ❌ Failed');
        }
      } catch (e) {
        failed++;
        print('  ❌ Error: $e');
      }
    }
    
    // Print summary
    print('\n📊 Migration Summary:');
    print('  ✅ Successful statements: $successful');
    print('  ❌ Failed statements: $failed');
    
    if (failed == 0) {
      print('\n✅ SQL migration completed successfully!');
      print('Run the verification script to confirm the changes.');
    } else {
      print('\n⚠️ SQL migration completed with some errors.');
      print('Please check the Supabase dashboard for more details.');
    }
    
  } catch (e) {
    print('❌ Error during migration: $e');
    exit(1);
  }
}

/// Test connection to Supabase
Future<bool> _testConnection(String url, String key) async {
  try {
    final response = await http.get(
      Uri.parse('$url/rest/v1/'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
      },
    ).timeout(const Duration(seconds: 10));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error testing connection: $e');
    return false;
  }
}

/// Execute a SQL statement against Supabase
Future<bool> _executeSql(String url, String key, String sql) async {
  try {
    final response = await http.post(
      Uri.parse('$url/rest/sql'),
      headers: {
        'apikey': key,
        'Authorization': 'Bearer $key',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'query': sql,
      }),
    ).timeout(const Duration(seconds: 30));
    
    return response.statusCode == 200;
  } catch (e) {
    print('  Error executing SQL: $e');
    return false;
  }
}

/// Split SQL file into individual statements
List<String> _splitSqlStatements(String sqlContent) {
  // Remove comments
  final noComments = sqlContent.replaceAll(RegExp(r'--.*$', multiLine: true), '');
  
  // Split by semicolons, but keep statements like 'DO $$...$$;' together
  final statements = <String>[];
  
  bool inDollarQuote = false;
  String currentStatement = '';
  
  for (final line in noComments.split('\n')) {
    final trimmedLine = line.trim();
    
    if (trimmedLine.isEmpty) {
      currentStatement += '\n';
      continue;
    }
    
    // Check for dollar quotes ($$)
    if (trimmedLine.contains(r'$$')) {
      inDollarQuote = !inDollarQuote;
      currentStatement += line + '\n';
      continue;
    }
    
    // If we're inside a dollar quote, just add the line
    if (inDollarQuote) {
      currentStatement += line + '\n';
      continue;
    }
    
    // Check for statement ending with semicolon
    if (trimmedLine.endsWith(';')) {
      currentStatement += line + '\n';
      statements.add(currentStatement.trim());
      currentStatement = '';
    } else {
      currentStatement += line + '\n';
    }
  }
  
  // Add the last statement if there's any
  if (currentStatement.trim().isNotEmpty) {
    statements.add(currentStatement.trim());
  }
  
  return statements;
} 