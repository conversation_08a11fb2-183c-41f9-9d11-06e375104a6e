-- Function to analyze tables and update statistics
CREATE OR REPLACE FUNCTION maintenance_analyze_tables()
R<PERSON><PERSON>NS void AS $$
BEGIN
    ANALYZE VERBOSE products;
    ANALYZE VERBOSE ingredients;
    ANALYZE VERBOSE product_ingredients;
    ANALYZE VERBOSE saved_products;
    ANALYZE VERBOSE nutrition_facts;
    RAISE NOTICE 'Database tables analyzed successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to reindex tables
CREATE OR REPLACE FUNCTION maintenance_reindex_tables()
RETURNS void AS $$
BEGIN
    REINDEX TABLE products;
    REINDEX TABLE ingredients;
    REINDEX TABLE product_ingredients;
    R<PERSON><PERSON><PERSON> TABLE saved_products;
    REI<PERSON>EX TABLE nutrition_facts;
    RAISE NOTICE 'Database tables reindexed successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to clean up orphaned records
CREATE OR REPLACE FUNCTION cleanup_orphaned_records()
RETURNS TABLE(table_name text, records_removed bigint) AS $$
DECLARE
    removed_count bigint;
BEGIN
    -- Clean up product_ingredients with no valid product
    DELETE FROM product_ingredients 
    WHERE product_id NOT IN (SELECT id FROM products);
    GET DIAGNOSTICS removed_count = ROW_COUNT;
    
    table_name := 'product_ingredients';
    records_removed := removed_count;
    RETURN NEXT;
    
    -- Clean up saved_products with no valid product
    DELETE FROM saved_products 
    WHERE product_id NOT IN (SELECT id FROM products);
    GET DIAGNOSTICS removed_count = ROW_COUNT;
    
    table_name := 'saved_products';
    records_removed := removed_count;
    RETURN NEXT;
    
    -- Clean up nutrition_facts with no valid product
    DELETE FROM nutrition_facts 
    WHERE product_id NOT IN (SELECT id FROM products);
    GET DIAGNOSTICS removed_count = ROW_COUNT;
    
    table_name := 'nutrition_facts';
    records_removed := removed_count;
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;
