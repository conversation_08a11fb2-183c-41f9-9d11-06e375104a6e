-- Schema debugging script
-- This will output the actual schema of the tables to diagnose issues

-- Check products table schema
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM 
  information_schema.columns
WHERE 
  table_schema = 'public' 
  AND table_name = 'products'
ORDER BY 
  ordinal_position;

-- Check user_preferences table schema
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM 
  information_schema.columns
WHERE 
  table_schema = 'public' 
  AND table_name = 'user_preferences'
ORDER BY 
  ordinal_position;

-- Check for primary keys
SELECT
  c.table_name,
  c.column_name
FROM
  information_schema.table_constraints tc
JOIN
  information_schema.constraint_column_usage AS ccu 
  USING (constraint_schema, constraint_name)
JOIN
  information_schema.columns AS c 
  ON c.table_schema = tc.constraint_schema
  AND c.table_name = tc.table_name
  AND c.column_name = ccu.column_name
WHERE
  tc.constraint_type = 'PRIMARY KEY'
  AND tc.table_schema = 'public'
  AND tc.table_name IN ('products', 'user_preferences');

-- Check for any data in the tables
SELECT COUNT(*) FROM public.products;
SELECT COUNT(*) FROM public.user_preferences; 