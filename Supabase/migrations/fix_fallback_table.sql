-- Fix product_analysis_fallback table schema issues
-- Add missing columns that the SafeScan app expects

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Check if product_analysis_fallback table exists, if not create it
CREATE TABLE IF NOT EXISTS public.product_analysis_fallback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR NOT NULL,
    product_name VARCHAR,
    analysis_data JSONB NOT NULL,
    source VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to product_analysis_fallback if they don't exist
DO $$
BEGIN
    -- Add safety_score column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'product_analysis_fallback' 
        AND column_name = 'safety_score'
    ) THEN
        ALTER TABLE public.product_analysis_fallback 
        ADD COLUMN safety_score INTEGER DEFAULT 0;
    END IF;
    
    -- Add safety_rating column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'product_analysis_fallback' 
        AND column_name = 'safety_rating'
    ) THEN
        ALTER TABLE public.product_analysis_fallback 
        ADD COLUMN safety_rating VARCHAR(50) DEFAULT 'unknown';
    END IF;
    
    -- Add user_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'product_analysis_fallback' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.product_analysis_fallback 
        ADD COLUMN user_id UUID;
    END IF;
    
    -- Add product_data column as alias for analysis_data for backward compatibility
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'product_analysis_fallback' 
        AND column_name = 'product_data'
    ) THEN
        ALTER TABLE public.product_analysis_fallback 
        ADD COLUMN product_data JSONB;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_barcode 
ON public.product_analysis_fallback(barcode);

CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_user_id 
ON public.product_analysis_fallback(user_id);

CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_created_at 
ON public.product_analysis_fallback(created_at);

-- Enable Row Level Security
ALTER TABLE public.product_analysis_fallback ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for fallback table
-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own fallback products" ON public.product_analysis_fallback;
DROP POLICY IF EXISTS "Users can insert fallback products" ON public.product_analysis_fallback;
DROP POLICY IF EXISTS "Anonymous users can insert fallback products" ON public.product_analysis_fallback;

-- Allow users to view their own fallback products
CREATE POLICY "Users can view their own fallback products" 
ON public.product_analysis_fallback
FOR SELECT USING (
    auth.uid() = user_id OR 
    user_id IS NULL OR 
    user_id = ''
);

-- Allow users to insert their own fallback products
CREATE POLICY "Users can insert fallback products" 
ON public.product_analysis_fallback
FOR INSERT WITH CHECK (
    auth.uid() = user_id OR 
    user_id IS NULL OR 
    user_id = ''
);

-- Allow anonymous users to insert fallback products (for error recovery)
CREATE POLICY "Anonymous users can insert fallback products" 
ON public.product_analysis_fallback
FOR INSERT WITH CHECK (true);

-- Update the database version
INSERT INTO public.database_version (version, description)
VALUES ('1.2.0', 'Fixed product_analysis_fallback table schema and added missing columns')
ON CONFLICT DO NOTHING; 