-- Targeted column fixes for products and user_preferences tables
-- This script focuses only on adding the missing columns

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Fix products table with individual column adds to better handle errors
DO $$
BEGIN
  -- Add id column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN id UUID DEFAULT uuid_generate_v4();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, update its properties
      ALTER TABLE public.products ALTER COLUMN id SET DEFAULT uuid_generate_v4();
  END;
  
  -- Add name column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN name VARCHAR(255);
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add safety_score column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN safety_score INTEGER DEFAULT 0;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add safety_rating column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN safety_rating VARCHAR(50) DEFAULT 'unknown';
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add user_id column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN user_id UUID;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add created_at column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add updated_at column if not exists
  BEGIN
    ALTER TABLE public.products ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Try to add primary key
  BEGIN
    ALTER TABLE public.products ADD PRIMARY KEY (id);
  EXCEPTION
    WHEN unique_violation THEN
      -- PK already exists, nothing to do
      NULL;
    WHEN others THEN
      -- Other errors like null value in column violation
      NULL;
  END;
END $$;

-- Fix user_preferences table with individual column adds
DO $$
BEGIN
  -- Add id column if not exists
  BEGIN
    ALTER TABLE public.user_preferences ADD COLUMN id UUID DEFAULT uuid_generate_v4();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, update its properties
      ALTER TABLE public.user_preferences ALTER COLUMN id SET DEFAULT uuid_generate_v4();
  END;
  
  -- Add user_id column if not exists
  BEGIN
    ALTER TABLE public.user_preferences ADD COLUMN user_id UUID;
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add created_at column if not exists
  BEGIN
    ALTER TABLE public.user_preferences ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Add updated_at column if not exists
  BEGIN
    ALTER TABLE public.user_preferences ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  EXCEPTION
    WHEN duplicate_column THEN
      -- Column already exists, nothing to do
      NULL;
  END;
  
  -- Try to add primary key
  BEGIN
    ALTER TABLE public.user_preferences ADD PRIMARY KEY (id);
  EXCEPTION
    WHEN unique_violation THEN
      -- PK already exists, nothing to do
      NULL;
    WHEN others THEN
      -- Other errors like null value in column violation
      NULL;
  END;
END $$;

-- Update database version
INSERT INTO public.database_version (version, description)
VALUES ('1.1.1', 'Fixed column issues in products and user_preferences tables')
ON CONFLICT DO NOTHING; 