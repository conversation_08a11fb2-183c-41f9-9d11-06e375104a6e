-- Fix schema issues for SafeScan MCP protocol
-- This migration adds missing columns and tables identified by the MCP verification

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Fix products table
DO $$
BEGIN
  -- Drop existing products table and its dependencies
  DROP TABLE IF EXISTS public.product_ingredients CASCADE;
  DROP TABLE IF EXISTS public.saved_products CASCADE;
  DROP TABLE IF EXISTS public.scan_results CASCADE;
  DROP TABLE IF EXISTS public.products CASCADE;
  
  -- Create products table with correct schema
  CREATE TABLE public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255),
    safety_score INTEGER DEFAULT 0,
    safety_rating VARCHAR(50) DEFAULT 'unknown',
    user_id UUID REFERENCES auth.users(id),
    brand VARCHAR(255),
    barcode VARCHAR(255),
    image TEXT,
    edited_image TEXT,
    product_url TEXT,
    ingredients_json JSONB,
    alternatives_json JSONB,
    nutrition_score JSONB,
    nutrition_concerns JSONB,
    concerns_count INTEGER DEFAULT 0,
    match_stats_json JSONB,
    scanned_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  
  -- Create indexes
  CREATE INDEX IF NOT EXISTS idx_products_barcode ON public.products(barcode);
  CREATE INDEX IF NOT EXISTS idx_products_user_id ON public.products(user_id);
  CREATE INDEX IF NOT EXISTS idx_products_safety_score ON public.products(safety_score);
  CREATE INDEX IF NOT EXISTS idx_products_brand ON public.products(brand);
  
  -- Enable RLS
  ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
  
  -- Create RLS policies
  CREATE POLICY "Users can view their own products" ON public.products
    FOR SELECT USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can insert their own products" ON public.products
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
  CREATE POLICY "Users can update their own products" ON public.products
    FOR UPDATE USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can delete their own products" ON public.products
    FOR DELETE USING (auth.uid() = user_id);
    
  -- Recreate product_ingredients table
  CREATE TABLE public.product_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    ingredient_id UUID REFERENCES public.ingredients(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  
  -- Create indexes for product_ingredients
  CREATE INDEX IF NOT EXISTS idx_product_ingredients_product_id ON public.product_ingredients(product_id);
  CREATE INDEX IF NOT EXISTS idx_product_ingredients_ingredient_id ON public.product_ingredients(ingredient_id);
  
  -- Recreate saved_products table
  CREATE TABLE public.saved_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  
  -- Create indexes for saved_products
  CREATE INDEX IF NOT EXISTS idx_saved_products_user_id ON public.saved_products(user_id);
  CREATE INDEX IF NOT EXISTS idx_saved_products_product_id ON public.saved_products(product_id);
  
  -- Enable RLS on saved_products
  ALTER TABLE public.saved_products ENABLE ROW LEVEL SECURITY;
  
  -- Create RLS policies for saved_products
  CREATE POLICY "Users can view their own saved products" ON public.saved_products
    FOR SELECT USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can insert their own saved products" ON public.saved_products
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
  CREATE POLICY "Users can delete their own saved products" ON public.saved_products
    FOR DELETE USING (auth.uid() = user_id);
END $$;

-- Fix user_preferences table issues
DO $$
BEGIN
  -- Check if user_preferences table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_preferences') THEN
    -- Check if columns exist and add them if they don't
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'id') THEN
      ALTER TABLE public.user_preferences ADD COLUMN id UUID PRIMARY KEY DEFAULT uuid_generate_v4();
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'user_id') THEN
      ALTER TABLE public.user_preferences ADD COLUMN user_id UUID REFERENCES auth.users(id);
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'created_at') THEN
      ALTER TABLE public.user_preferences ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'user_preferences' AND column_name = 'updated_at') THEN
      ALTER TABLE public.user_preferences ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
  ELSE
    -- Create user_preferences table if it doesn't exist
    CREATE TABLE public.user_preferences (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES auth.users(id),
      allergens JSONB,
      avoided_ingredients JSONB,
      is_premium BOOLEAN DEFAULT FALSE,
      last_login TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  END IF;
END $$;

-- Create missing optional tables
-- Allergens table
CREATE TABLE IF NOT EXISTS public.allergens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  severity VARCHAR(50) DEFAULT 'medium',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common allergens
INSERT INTO public.allergens (name, description, severity)
VALUES 
  ('Peanuts', 'Legume commonly causing severe allergic reactions', 'high'),
  ('Tree Nuts', 'Includes almonds, walnuts, cashews, etc.', 'high'),
  ('Milk', 'Dairy products and milk derivatives', 'medium'),
  ('Eggs', 'Whole eggs and egg derivatives', 'medium'),
  ('Fish', 'Various fish species and products', 'high'),
  ('Shellfish', 'Includes shrimp, crab, lobster, etc.', 'high'),
  ('Soy', 'Soybeans and soy derivatives', 'medium'),
  ('Wheat', 'Wheat and wheat derivatives', 'medium'),
  ('Sesame', 'Sesame seeds and sesame oil', 'medium'),
  ('Gluten', 'Protein found in wheat, barley, rye', 'medium'),
  ('Sulfites', 'Used as preservatives in many foods', 'medium'),
  ('Mustard', 'Mustard seeds and derivatives', 'medium')
ON CONFLICT (name) DO NOTHING;

-- Dietary preferences table
CREATE TABLE IF NOT EXISTS public.dietary_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common dietary preferences
INSERT INTO public.dietary_preferences (name, description)
VALUES 
  ('Vegetarian', 'Avoids meat including beef, poultry, and seafood'),
  ('Vegan', 'Avoids all animal products including dairy, eggs, and honey'),
  ('Gluten-Free', 'Avoids wheat, barley, rye, and other gluten-containing ingredients'),
  ('Dairy-Free', 'Avoids milk and dairy products'),
  ('Keto', 'Low carbohydrate, high fat diet'),
  ('Paleo', 'Focuses on whole foods, avoids processed foods, grains, legumes, and dairy'),
  ('Kosher', 'Follows Jewish dietary laws'),
  ('Halal', 'Follows Islamic dietary laws'),
  ('Low FODMAP', 'Avoids certain carbohydrates that can trigger digestive issues'),
  ('Low Sodium', 'Limits salt and sodium intake')
ON CONFLICT (name) DO NOTHING;

-- Avoided ingredients table
CREATE TABLE IF NOT EXISTS public.avoided_ingredients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Database version table
CREATE TABLE IF NOT EXISTS public.database_version (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  version VARCHAR(50) NOT NULL,
  description TEXT,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert current version
INSERT INTO public.database_version (version, description)
VALUES ('1.1.0', 'Fixed schema issues for MCP protocol support')
ON CONFLICT DO NOTHING;

-- Enable Row Level Security on tables
ALTER TABLE public.allergens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dietary_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.avoided_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.database_version ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allergens table policies
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Anyone can view allergens" ON public.allergens;
  
  -- Create new policies
  CREATE POLICY "Anyone can view allergens" ON public.allergens
    FOR SELECT USING (true);
END $$;

-- Dietary preferences table policies
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Anyone can view dietary preferences" ON public.dietary_preferences;
  
  -- Create new policies
  CREATE POLICY "Anyone can view dietary preferences" ON public.dietary_preferences
    FOR SELECT USING (true);
END $$;

-- Create unknown_barcodes table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.unknown_barcodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR(255) NOT NULL UNIQUE,
    count INTEGER DEFAULT 1,
    first_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id),
    status VARCHAR(50) DEFAULT 'pending'
);

-- Create scan_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.scan_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR(255) NOT NULL,
    request_id VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_unknown_barcodes_barcode ON public.unknown_barcodes(barcode);
CREATE INDEX IF NOT EXISTS idx_unknown_barcodes_user_id ON public.unknown_barcodes(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_logs_barcode ON public.scan_logs(barcode);
CREATE INDEX IF NOT EXISTS idx_scan_logs_user_id ON public.scan_logs(user_id);

-- Enable Row Level Security
ALTER TABLE public.unknown_barcodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scan_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Products table policies
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can see their own scan logs" ON public.scan_logs;
  DROP POLICY IF EXISTS "Users can insert their own scan logs" ON public.scan_logs;
  DROP POLICY IF EXISTS "Users can see unknown barcodes" ON public.unknown_barcodes;
  DROP POLICY IF EXISTS "Users can insert unknown barcodes" ON public.unknown_barcodes;
  
  -- Create new policies
  CREATE POLICY "Users can see their own scan logs" ON public.scan_logs
    FOR SELECT USING (auth.uid() = user_id);

  CREATE POLICY "Users can insert their own scan logs" ON public.scan_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

  CREATE POLICY "Users can see unknown barcodes" ON public.unknown_barcodes
    FOR SELECT USING (true);

  CREATE POLICY "Users can insert unknown barcodes" ON public.unknown_barcodes
    FOR INSERT WITH CHECK (auth.uid() = user_id);
END $$;

-- Update database version
INSERT INTO public.database_version (version, description)
VALUES ('1.2.0', 'Added missing columns and tables for MCP protocol support')
ON CONFLICT DO NOTHING;

-- Drop and recreate product_analysis_fallback table
DROP TABLE IF EXISTS public.product_analysis_fallback CASCADE;

CREATE TABLE public.product_analysis_fallback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR(255) NOT NULL,
    product_name VARCHAR(255),
    analysis_data JSONB NOT NULL,
    source VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for product_analysis_fallback
CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_barcode ON public.product_analysis_fallback(barcode);
CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_user_id ON public.product_analysis_fallback(user_id);

-- Enable RLS on product_analysis_fallback
ALTER TABLE public.product_analysis_fallback ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for product_analysis_fallback
CREATE POLICY "Users can view their own fallback products" ON public.product_analysis_fallback
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own fallback products" ON public.product_analysis_fallback
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own fallback products" ON public.product_analysis_fallback
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own fallback products" ON public.product_analysis_fallback
    FOR DELETE USING (auth.uid() = user_id);

-- Print completion message
SELECT 'Schema fixes applied successfully. Database now compatible with SafeScan MCP.' as result;

-- Fix scan_results table
DO $$
BEGIN
  -- Drop existing scan_results table if it exists
  DROP TABLE IF EXISTS public.scan_results;
  
  -- Create scan_results table with correct schema
  CREATE TABLE public.scan_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES public.products(id),
    barcode VARCHAR(255) NOT NULL,
    product_name VARCHAR(255),
    brand VARCHAR(255),
    scan_data_json JSONB NOT NULL,
    scan_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    source VARCHAR(100),
    confidence_score FLOAT,
    user_id UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );
  
  -- Create indexes
  CREATE INDEX IF NOT EXISTS idx_scan_results_barcode ON public.scan_results(barcode);
  CREATE INDEX IF NOT EXISTS idx_scan_results_product_id ON public.scan_results(product_id);
  CREATE INDEX IF NOT EXISTS idx_scan_results_user_id ON public.scan_results(user_id);
  
  -- Enable RLS
  ALTER TABLE public.scan_results ENABLE ROW LEVEL SECURITY;
  
  -- Create RLS policies
  CREATE POLICY "Users can view their own scan results" ON public.scan_results
    FOR SELECT USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can insert their own scan results" ON public.scan_results
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
  CREATE POLICY "Users can update their own scan results" ON public.scan_results
    FOR UPDATE USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can delete their own scan results" ON public.scan_results
    FOR DELETE USING (auth.uid() = user_id);
END $$; 