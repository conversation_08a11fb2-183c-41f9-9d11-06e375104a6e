-- Fixed schema script for SafeScan database
-- Corrected version without IF NOT EXISTS for policies

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Fix products table
ALTER TABLE IF EXISTS public.products 
  ADD COLUMN IF NOT EXISTS id UUID DEFAULT uuid_generate_v4(),
  ADD COLUMN IF NOT EXISTS name VARCHAR(255),
  ADD COLUMN IF NOT EXISTS safety_score INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS safety_rating VARCHAR(50) DEFAULT 'unknown',
  ADD COLUMN IF NOT EXISTS user_id UUID,
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add primary key if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'products_pkey'
  ) THEN
    ALTER TABLE public.products ADD PRIMARY KEY (id);
  END IF;
EXCEPTION
  WHEN others THEN
    -- Do nothing, primary key already exists or can't be created
END $$;

-- 2. Fix user_preferences table
ALTER TABLE IF EXISTS public.user_preferences 
  ADD COLUMN IF NOT EXISTS id UUID DEFAULT uuid_generate_v4(),
  ADD COLUMN IF NOT EXISTS user_id UUID,
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Add primary key if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'user_preferences_pkey'
  ) THEN
    ALTER TABLE public.user_preferences ADD PRIMARY KEY (id);
  END IF;
EXCEPTION
  WHEN others THEN
    -- Do nothing, primary key already exists or can't be created
END $$;

-- 3. Create optional tables
-- Allergens table
CREATE TABLE IF NOT EXISTS public.allergens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  severity VARCHAR(50) DEFAULT 'medium',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Dietary preferences table
CREATE TABLE IF NOT EXISTS public.dietary_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Avoided ingredients table
CREATE TABLE IF NOT EXISTS public.avoided_ingredients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  name VARCHAR(255) NOT NULL,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Database version table
CREATE TABLE IF NOT EXISTS public.database_version (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  version VARCHAR(50) NOT NULL,
  description TEXT,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Record migration
INSERT INTO public.database_version (version, description)
VALUES ('1.1.0', 'Applied simple fixes for MCP compatibility')
ON CONFLICT DO NOTHING;

-- Enable Row Level Security on tables
ALTER TABLE IF EXISTS public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.allergens ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.dietary_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.avoided_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.database_version ENABLE ROW LEVEL SECURITY;

-- Create RLS policies with safe DROP/CREATE pattern
-- Products table policies
DO $$
BEGIN
  -- Drop existing policies if they exist
  BEGIN
    DROP POLICY IF EXISTS "Users can view their own products" ON public.products;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Users can insert their own products" ON public.products;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Users can update their own products" ON public.products;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Users can delete their own products" ON public.products;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;

  -- Create policies
  CREATE POLICY "Users can view their own products" ON public.products
    FOR SELECT USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can insert their own products" ON public.products
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
  CREATE POLICY "Users can update their own products" ON public.products
    FOR UPDATE USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can delete their own products" ON public.products
    FOR DELETE USING (auth.uid() = user_id);
END $$;

-- User preferences policies
DO $$
BEGIN
  -- Drop existing policies if they exist
  BEGIN
    DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Users can insert their own preferences" ON public.user_preferences;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;

  -- Create policies
  CREATE POLICY "Users can view their own preferences" ON public.user_preferences
    FOR SELECT USING (auth.uid() = user_id);
    
  CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);
    
  CREATE POLICY "Users can update their own preferences" ON public.user_preferences
    FOR UPDATE USING (auth.uid() = user_id);
END $$;

-- Allow anyone to view allergens and dietary preferences
DO $$
BEGIN
  -- Drop existing policies if they exist
  BEGIN
    DROP POLICY IF EXISTS "Anyone can view allergens" ON public.allergens;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;
  
  BEGIN
    DROP POLICY IF EXISTS "Anyone can view dietary preferences" ON public.dietary_preferences;
  EXCEPTION WHEN undefined_object THEN
    -- Policy doesn't exist, ignore error
  END;

  -- Create policies
  CREATE POLICY "Anyone can view allergens" ON public.allergens
    FOR SELECT USING (true);
    
  CREATE POLICY "Anyone can view dietary preferences" ON public.dietary_preferences
    FOR SELECT USING (true);
END $$; 