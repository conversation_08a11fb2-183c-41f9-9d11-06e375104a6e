-- Add missing tables for SafeScan MCP protocol

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Allergens table
CREATE TABLE IF NOT EXISTS public.allergens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    severity VARCHAR(50) DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common allergens
INSERT INTO public.allergens (name, description, severity)
VALUES 
  ('Peanuts', 'Legume commonly causing severe allergic reactions', 'high'),
  ('Tree Nuts', 'Includes almonds, walnuts, cashews, etc.', 'high'),
  ('Milk', 'Dairy products and milk derivatives', 'medium'),
  ('Eggs', 'Whole eggs and egg derivatives', 'medium'),
  ('Fish', 'Various fish species and products', 'high'),
  ('Shellfish', 'Includes shrimp, crab, lobster, etc.', 'high'),
  ('Soy', 'Soybeans and soy derivatives', 'medium'),
  ('Wheat', 'Wheat and wheat derivatives', 'medium'),
  ('Sesame', 'Sesame seeds and sesame oil', 'medium'),
  ('Gluten', 'Protein found in wheat, barley, rye', 'medium'),
  ('Sulfites', 'Used as preservatives in many foods', 'medium'),
  ('Mustard', 'Mustard seeds and derivatives', 'medium')
ON CONFLICT (name) DO NOTHING;

-- Dietary preferences table
CREATE TABLE IF NOT EXISTS public.dietary_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common dietary preferences
INSERT INTO public.dietary_preferences (name, description)
VALUES 
  ('Vegetarian', 'Avoids meat including beef, poultry, and seafood'),
  ('Vegan', 'Avoids all animal products including dairy, eggs, and honey'),
  ('Gluten-Free', 'Avoids wheat, barley, rye, and other gluten-containing ingredients'),
  ('Dairy-Free', 'Avoids milk and dairy products'),
  ('Keto', 'Low carbohydrate, high fat diet'),
  ('Paleo', 'Focuses on whole foods, avoids processed foods, grains, legumes, and dairy'),
  ('Kosher', 'Follows Jewish dietary laws'),
  ('Halal', 'Follows Islamic dietary laws'),
  ('Low FODMAP', 'Avoids certain carbohydrates that can trigger digestive issues'),
  ('Low Sodium', 'Limits salt and sodium intake')
ON CONFLICT (name) DO NOTHING;

-- Avoided ingredients table
CREATE TABLE IF NOT EXISTS public.avoided_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Enable Row Level Security on new tables
ALTER TABLE public.allergens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dietary_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.avoided_ingredients ENABLE ROW LEVEL SECURITY;

-- Allow anyone to view allergens and dietary preferences
CREATE POLICY "Anyone can view allergens" ON public.allergens
  FOR SELECT USING (true);
  
CREATE POLICY "Anyone can view dietary preferences" ON public.dietary_preferences
  FOR SELECT USING (true);

-- Users can only manage their own avoided ingredients
CREATE POLICY "Users can view their avoided ingredients" ON public.avoided_ingredients
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can add avoided ingredients" ON public.avoided_ingredients
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can update their avoided ingredients" ON public.avoided_ingredients
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY "Users can delete their avoided ingredients" ON public.avoided_ingredients
  FOR DELETE USING (auth.uid() = user_id);

-- Add table for database version tracking
CREATE TABLE IF NOT EXISTS public.database_version (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version VARCHAR(50) NOT NULL,
    description TEXT,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert current version
INSERT INTO public.database_version (version, description)
VALUES ('1.0.0', 'Added missing tables for MCP protocol support');

-- Acknowledge migration completion
SELECT 'Missing tables migration completed successfully!'; 