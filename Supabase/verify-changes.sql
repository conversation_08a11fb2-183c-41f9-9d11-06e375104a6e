-- Verify that the nutrition columns were added
SELECT 
    column_name, 
    data_type 
FROM 
    information_schema.columns 
WHERE 
    table_name = 'products' 
    AND column_name IN ('nutrition_facts_json', 'nutrition_concerns_json');

-- Verify that the nutrition_facts table was created
SELECT 
    table_name 
FROM 
    information_schema.tables 
WHERE 
    table_name = 'nutrition_facts' 
    AND table_schema = 'public';

-- Verify that the indexes were created
SELECT 
    indexname, 
    indexdef 
FROM 
    pg_indexes 
WHERE 
    tablename = 'products' 
    AND indexname IN (
        'idx_products_nutrition_facts', 
        'idx_products_nutrition_concerns',
        'idx_products_ingredients_json',
        'idx_products_concerns_json'
    );

-- Verify that the views were created
SELECT 
    table_name 
FROM 
    information_schema.views 
WHERE 
    table_name IN ('product_safety_summary', 'product_nutrition_summary') 
    AND table_schema = 'public';

-- Verify that the functions were created
SELECT 
    proname, 
    prosrc 
FROM 
    pg_proc 
WHERE 
    proname IN (
        'update_modified_column', 
        'maintenance_analyze_tables', 
        'maintenance_reindex_tables',
        'cleanup_orphaned_records',
        'validate_products_json',
        'validate_nutrition_facts'
    ) 
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- Verify that the triggers were created
SELECT 
    trigger_name, 
    event_manipulation, 
    action_statement 
FROM 
    information_schema.triggers 
WHERE 
    trigger_name IN (
        'update_products_modtime', 
        'update_nutrition_facts_modtime',
        'validate_nutrition_facts_trigger'
    ) 
    AND event_object_table IN ('products', 'nutrition_facts');
