/// <reference lib="deno.ns" />

import { serve } from "std/http/server.ts";
import { createClient } from "@supabase/supabase-js";
import { Parser } from "json2csv";
import sgMail from "@sendgrid/mail";

// Types
interface PendingIngredient {
  id: string;
  name: string;
  reviewed: boolean;
  created_at: string;
  // Add other fields as needed
}

interface PendingProduct {
  id: string;
  name: string;
  reviewed: boolean;
  created_at: string;
  // Add other fields as needed
}

interface RequestEvent {
  request: Request;
  method: string;
}

// Environment variables validation
const requiredEnvVars = {
  SUPABASE_URL: Deno.env.get('SUPABASE_URL'),
  SUPABASE_SERVICE_ROLE_KEY: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
  SENDGRID_API_KEY: Deno.env.get('SENDGRID_API_KEY'),
  REPORT_RECIPIENT_EMAIL: Deno.env.get('REPORT_RECIPIENT_EMAIL'),
  SENDER_EMAIL: Deno.env.get('SENDER_EMAIL') || Deno.env.get('REPORT_RECIPIENT_EMAIL'),
};

// Validate environment variables
for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (!value) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
}

const {
  SUPABASE_URL: supabaseUrl,
  SUPABASE_SERVICE_ROLE_KEY: supabaseServiceKey,
  SENDGRID_API_KEY: sendgridApiKey,
  REPORT_RECIPIENT_EMAIL: reportRecipient,
  SENDER_EMAIL: senderEmail,
} = requiredEnvVars;

// Initialize clients
sgMail.setApiKey(sendgridApiKey);
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Helper function to create error response
const createErrorResponse = (message: string, status: number = 500) => {
  return new Response(JSON.stringify({ error: message }), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    },
  });
};

// Helper function to create success response
const createSuccessResponse = (message: string, data?: unknown) => {
  return new Response(JSON.stringify({ message, data }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    },
  });
};

serve(async (req: RequestEvent) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  // Verify request method
  if (req.method !== 'POST') {
    return createErrorResponse('Method not allowed', 405);
  }

  try {
    // 1. Fetch pending_ingredients
    const { data: pendingIngredients, error: ingError } = await supabase
      .from('pending_ingredients')
      .select('*')
      .eq('reviewed', false);

    if (ingError) {
      console.error('Error fetching pending ingredients:', ingError);
      return createErrorResponse('Error fetching pending ingredients');
    }

    // 2. Fetch pending_products
    const { data: pendingProducts, error: prodError } = await supabase
      .from('pending_products')
      .select('*')
      .eq('reviewed', false);

    if (prodError) {
      console.error('Error fetching pending products:', prodError);
      return createErrorResponse('Error fetching pending products');
    }

    // 3. Convert to CSV with error handling
    let ingCsv = '';
    let prodCsv = '';

    try {
      const parser = new Parser();
      ingCsv = pendingIngredients?.length ? parser.parse(pendingIngredients) : 'No pending ingredients';
      prodCsv = pendingProducts?.length ? parser.parse(pendingProducts) : 'No pending products';
    } catch (csvError) {
      console.error('Error generating CSV:', csvError);
      return createErrorResponse('Error generating CSV files');
    }

    // 4. Send email with attachments
    const msg = {
      to: reportRecipient,
      from: senderEmail,
      subject: 'Weekly SafeScan Pending Review Report',
      text: 'Attached are the pending ingredients and products for review.',
      attachments: [
        {
          content: btoa(ingCsv),
          filename: 'pending_ingredients.csv',
          type: 'text/csv',
          disposition: 'attachment',
        },
        {
          content: btoa(prodCsv),
          filename: 'pending_products.csv',
          type: 'text/csv',
          disposition: 'attachment',
        },
      ],
    };

    await sgMail.send(msg);
    return createSuccessResponse('Report sent successfully', {
      ingredientsCount: pendingIngredients?.length || 0,
      productsCount: pendingProducts?.length || 0,
    });
  } catch (error: unknown) {
    console.error('Unexpected error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return createErrorResponse(`Failed to process request: ${errorMessage}`);
  }
});