-- Create user_scan_history table
CREATE TABLE IF NOT EXISTS public.user_scan_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    scanned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id, scanned_at)
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_user_scan_history_user_id ON public.user_scan_history(user_id);

-- Enable Row Level Security
ALTER TABLE public.user_scan_history ENABLE ROW LEVEL SECURITY;

-- User scan history policies
CREATE POLICY "Users can view their own scan history" ON public.user_scan_history
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can insert their own scan history" ON public.user_scan_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can delete their own scan history" ON public.user_scan_history
  FOR DELETE USING (auth.uid() = user_id);
