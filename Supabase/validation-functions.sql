-- Function to validate JSON data in products table
CREATE OR REPLACE FUNCTION validate_products_json()
RETURNS TABLE(id uuid, field_name text, is_valid boolean) AS $$
BEGIN
    -- Check ingredients_json
    FOR id, is_valid IN
        SELECT p.id, jsonb_typeof(p.ingredients_json) = 'array'
        FROM products p
        WHERE p.ingredients_json IS NOT NULL
    LOOP
        field_name := 'ingredients_json';
        RETURN NEXT;
    END LOOP;
    
    -- Check nutrition_facts_json
    FOR id, is_valid IN
        SELECT p.id, jsonb_typeof(p.nutrition_facts_json) = 'object'
        FROM products p
        WHERE p.nutrition_facts_json IS NOT NULL
    LOOP
        field_name := 'nutrition_facts_json';
        RETURN NEXT;
    END LOOP;
    
    -- Check nutrition_concerns_json
    FOR id, is_valid IN
        SELECT p.id, jsonb_typeof(p.nutrition_concerns_json) = 'array'
        FROM products p
        WHERE p.nutrition_concerns_json IS NOT NULL
    LOOP
        field_name := 'nutrition_concerns_json';
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to validate nutrition facts data
CREATE OR REPLACE FUNCTION validate_nutrition_facts()
RETURNS TRIGGER AS $$
BEGIN
    -- Basic validation logic
    IF NEW.nutrition_facts_json IS NOT NULL AND 
       (NOT jsonb_typeof(NEW.nutrition_facts_json) = 'object') THEN
        RAISE EXCEPTION 'nutrition_facts_json must be a valid JSON object';
    END IF;
    
    IF NEW.nutrition_concerns_json IS NOT NULL AND 
       (NOT jsonb_typeof(NEW.nutrition_concerns_json) = 'array') THEN
        RAISE EXCEPTION 'nutrition_concerns_json must be a valid JSON array';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for validation
CREATE TRIGGER validate_nutrition_facts_trigger
BEFORE INSERT OR UPDATE ON products
FOR EACH ROW EXECUTE FUNCTION validate_nutrition_facts();
