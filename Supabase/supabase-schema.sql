-- Create tables for SafeScan app

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    brand VARCHAR(255),
    barcode VARCHAR(255),
    image TEXT,
    safety_score INTEGER NOT NULL,
    safety_rating TEXT NOT NULL,
    scanned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL, -- Make user_id required
    ingredients_json JSONB,
    alternatives_json JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_products_user_id ON public.products(user_id);

-- Ingredients table
CREATE TABLE IF NOT EXISTS public.ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255),
    description TEXT,
    scientific_name VARCHAR(255),
    common_aliases TEXT[],
    health_risks TEXT[],
    safety_level VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on name for faster searches
CREATE INDEX IF NOT EXISTS idx_ingredients_name ON public.ingredients(name);

-- Product ingredients junction table
CREATE TABLE IF NOT EXISTS public.product_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES public.ingredients(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(product_id, ingredient_id)
);

-- Saved products table
CREATE TABLE IF NOT EXISTS public.saved_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    saved_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- Create index on user_id for faster queries
CREATE INDEX IF NOT EXISTS idx_saved_products_user_id ON public.saved_products(user_id);

-- User preferences table
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    allergens UUID[] DEFAULT '{}',
    avoided_ingredients TEXT[] DEFAULT '{}',  -- Store names of ingredients to avoid
    is_premium BOOLEAN DEFAULT false,         -- Premium subscription status
    last_login TIMESTAMP WITH TIME ZONE,      -- Track login times
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Allergens table
CREATE TABLE IF NOT EXISTS public.allergens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    severity VARCHAR(50) DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common allergens
INSERT INTO public.allergens (name, description, severity)
VALUES 
  ('Peanuts', 'Legume commonly causing severe allergic reactions', 'high'),
  ('Tree Nuts', 'Includes almonds, walnuts, cashews, etc.', 'high'),
  ('Milk', 'Dairy products and milk derivatives', 'medium'),
  ('Eggs', 'Whole eggs and egg derivatives', 'medium'),
  ('Fish', 'Various fish species and products', 'high'),
  ('Shellfish', 'Includes shrimp, crab, lobster, etc.', 'high'),
  ('Soy', 'Soybeans and soy derivatives', 'medium'),
  ('Wheat', 'Wheat and wheat derivatives', 'medium'),
  ('Sesame', 'Sesame seeds and sesame oil', 'medium'),
  ('Gluten', 'Protein found in wheat, barley, rye', 'medium'),
  ('Sulfites', 'Used as preservatives in many foods', 'medium'),
  ('Mustard', 'Mustard seeds and derivatives', 'medium')
ON CONFLICT (name) DO NOTHING;

-- Dietary preferences table
CREATE TABLE IF NOT EXISTS public.dietary_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Insert common dietary preferences
INSERT INTO public.dietary_preferences (name, description)
VALUES 
  ('Vegetarian', 'Avoids meat including beef, poultry, and seafood'),
  ('Vegan', 'Avoids all animal products including dairy, eggs, and honey'),
  ('Gluten-Free', 'Avoids wheat, barley, rye, and other gluten-containing ingredients'),
  ('Dairy-Free', 'Avoids milk and dairy products'),
  ('Keto', 'Low carbohydrate, high fat diet'),
  ('Paleo', 'Focuses on whole foods, avoids processed foods, grains, legumes, and dairy'),
  ('Kosher', 'Follows Jewish dietary laws'),
  ('Halal', 'Follows Islamic dietary laws'),
  ('Low FODMAP', 'Avoids certain carbohydrates that can trigger digestive issues'),
  ('Low Sodium', 'Limits salt and sodium intake')
ON CONFLICT (name) DO NOTHING;

-- Avoided ingredients table
CREATE TABLE IF NOT EXISTS public.avoided_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Row Level Security Policies
-- Enable Row Level Security
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.saved_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.avoided_ingredients ENABLE ROW LEVEL SECURITY;

-- Products policies
CREATE POLICY "Users can view their own products" ON public.products
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can insert their own products" ON public.products
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can update their own products" ON public.products
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY "Users can delete their own products" ON public.products
  FOR DELETE USING (auth.uid() = user_id);

-- Saved products policies
CREATE POLICY "Users can view their own saved products" ON public.saved_products
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can save products" ON public.saved_products
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can unsave products" ON public.saved_products
  FOR DELETE USING (auth.uid() = user_id);

-- User preferences policies
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can insert their own preferences" ON public.user_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can update their own preferences" ON public.user_preferences
  FOR UPDATE USING (auth.uid() = user_id);

-- Avoided ingredients policies
CREATE POLICY "Users can view their avoided ingredients" ON public.avoided_ingredients
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY "Users can add avoided ingredients" ON public.avoided_ingredients
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY "Users can update their avoided ingredients" ON public.avoided_ingredients
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY "Users can delete their avoided ingredients" ON public.avoided_ingredients
  FOR DELETE USING (auth.uid() = user_id);

-- Everyone can view ingredients, allergens, and dietary preferences
ALTER TABLE public.ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.allergens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dietary_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_ingredients ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view ingredients" ON public.ingredients
  FOR SELECT USING (true);
  
CREATE POLICY "Anyone can view allergens" ON public.allergens
  FOR SELECT USING (true);
  
CREATE POLICY "Anyone can view dietary preferences" ON public.dietary_preferences
  FOR SELECT USING (true);
  
CREATE POLICY "Anyone can view product ingredients" ON public.product_ingredients
  FOR SELECT USING (true);