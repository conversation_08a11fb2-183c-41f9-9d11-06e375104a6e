-- SafeScan Enhanced Database Schema for Safety Analysis Flow
-- Run this in your Supabase SQL Editor to set up the required tables and indexes

-- 1. AI Analysis Cache Table (for storing AI analysis results)
CREATE TABLE IF NOT EXISTS ai_analysis_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id VARCHAR NOT NULL,
    analysis_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster retrieval
CREATE INDEX IF NOT EXISTS idx_ai_analysis_cache_product_id ON ai_analysis_cache(product_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_cache_created_at ON ai_analysis_cache(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE ai_analysis_cache ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Public read access for ai_analysis_cache" ON ai_analysis_cache
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert ai_analysis_cache" ON ai_analysis_cache
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- 2. Scan Events Table (for tracking scan operations)
CREATE TABLE IF NOT EXISTS scan_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR NOT NULL,
    request_id VARCHAR NOT NULL,
    user_id VARCHAR NOT NULL,
    status VARCHAR NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_scan_events_barcode ON scan_events(barcode);
CREATE INDEX IF NOT EXISTS idx_scan_events_user_id ON scan_events(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_events_timestamp ON scan_events(created_at);

-- Enable RLS
ALTER TABLE scan_events ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can see their own scan events" ON scan_events
    FOR SELECT USING (auth.uid() = user_id OR user_id = 'anonymous');

CREATE POLICY "Users can insert their own scan events" ON scan_events
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id = 'anonymous');

-- 3. Product Fallback Table (for when regular product storage fails)
CREATE TABLE IF NOT EXISTS product_analysis_fallback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    barcode VARCHAR NOT NULL,
    product_name VARCHAR,
    analysis_data JSONB NOT NULL,
    source VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_product_analysis_fallback_barcode ON product_analysis_fallback(barcode);
CREATE INDEX IF NOT EXISTS idx_product_analysis_fallback_user_id ON product_analysis_fallback(user_id);

-- Enable RLS
ALTER TABLE product_analysis_fallback ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can see their own fallback products" ON product_analysis_fallback
    FOR SELECT USING (auth.uid() = user_id OR user_id = 'anonymous');

-- 4. Pending Ingredients Table (for new ingredients discovered during analysis)
CREATE TABLE IF NOT EXISTS pending_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR NOT NULL,
    category VARCHAR,
    description TEXT,
    scientific_name VARCHAR,
    common_aliases VARCHAR,
    health_claims VARCHAR,
    safety_level VARCHAR,
    source VARCHAR,
    confidence_score FLOAT4,
    submitted_by UUID,
    status VARCHAR NOT NULL DEFAULT 'pending',
    reviewed_by UUID,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_pending_ingredients_normalized_name ON pending_ingredients(normalized_name);
CREATE INDEX IF NOT EXISTS idx_pending_ingredients_pending_review ON pending_ingredients(pending_review);

-- Enable RLS
ALTER TABLE pending_ingredients ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Public read access for pending_ingredients" ON pending_ingredients
    FOR SELECT USING (true);

-- 5. User Feedback Table (for AI improvement)
CREATE TABLE IF NOT EXISTS user_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR NOT NULL,
    product_id VARCHAR NOT NULL,
    feedback_type VARCHAR NOT NULL,
    feedback_text TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_feedback_product_id ON user_feedback(product_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(user_id);

-- Enable RLS
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own feedback" ON user_feedback
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own feedback" ON user_feedback
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 6. Products Table Enhancements (if it already exists)
DO $$
BEGIN
    -- Check if match_stats_json column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'match_stats_json'
    ) THEN
        -- Add match_stats_json column for advanced analysis stats
        ALTER TABLE products ADD COLUMN IF NOT EXISTS match_stats_json JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Check if nutrition_concerns_json column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'nutrition_concerns_json'
    ) THEN
        -- Add nutrition_concerns_json column for detailed nutrition analysis
        ALTER TABLE products ADD COLUMN IF NOT EXISTS nutrition_concerns_json JSONB DEFAULT '{}'::jsonb;
    END IF;
    
    -- Check if alternatives_json column exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'alternatives_json'
    ) THEN
        -- Add alternatives_json column for product alternatives
        ALTER TABLE products ADD COLUMN IF NOT EXISTS alternatives_json JSONB DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- 7. Add brand column to products table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'brand'
    ) THEN
        ALTER TABLE products ADD COLUMN IF NOT EXISTS brand TEXT;
    END IF;
END $$;

-- 8. Enhanced User Preferences table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'user_preferences'
    ) THEN
        -- Add new columns for AI features if they don't exist
        ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS preferred_brands TEXT[];
        ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS safety_threshold INTEGER DEFAULT 70;
        ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS ai_analysis_enabled BOOLEAN DEFAULT true;
        ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS personalized_recommendations BOOLEAN DEFAULT true;
    END IF;
END $$;

-- Add RPC function for executing SQL (used by migration script)
CREATE OR REPLACE FUNCTION execute_sql(sql text) RETURNS void AS $$
BEGIN
    EXECUTE sql;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 