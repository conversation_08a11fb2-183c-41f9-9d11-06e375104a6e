-- Production RLS Policies for Safescan App
-- This script creates proper Row Level Security policies for production use
-- Run this AFTER the quick fix (rls_fix.sql) when you're ready for production

-- Re-enable RLS on main tables
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_analysis_fallback ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scan_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Products table policies
CREATE POLICY "Allow anonymous read access" ON public.products 
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users full access" ON public.products 
    FOR ALL USING (auth.uid() IS NOT NULL);

CREATE POLICY "Allow anonymous insert" ON public.products 
    FOR INSERT WITH CHECK (true);

-- Product analysis fallback table policies
CREATE POLICY "Allow anonymous access" ON public.product_analysis_fallback 
    FOR ALL USING (true);

-- Scan results policies
CREATE POLICY "Users can view their own scan results" ON public.scan_results 
    FOR SELECT USING (
        auth.uid()::text = user_id OR 
        user_id = 'anonymous' OR 
        auth.uid() IS NULL
    );

CREATE POLICY "Users can insert scan results" ON public.scan_results 
    FOR INSERT WITH CHECK (
        auth.uid()::text = user_id OR 
        user_id = 'anonymous' OR 
        auth.uid() IS NULL
    );

CREATE POLICY "Users can update their own scan results" ON public.scan_results 
    FOR UPDATE USING (
        auth.uid()::text = user_id OR 
        user_id = 'anonymous'
    );

-- User preferences policies
CREATE POLICY "Users can access their own preferences" ON public.user_preferences 
    FOR ALL USING (auth.uid()::text = user_id);

-- Optional: Saved products policies (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'saved_products') THEN
        EXECUTE 'ALTER TABLE public.saved_products ENABLE ROW LEVEL SECURITY';
        EXECUTE 'CREATE POLICY "Users can access their own saved products" ON public.saved_products FOR ALL USING (auth.uid()::text = user_id)';
    END IF;
END $$;

-- Optional: Product ratings policies (if table exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_ratings') THEN
        EXECUTE 'ALTER TABLE public.product_ratings ENABLE ROW LEVEL SECURITY';
        EXECUTE 'CREATE POLICY "Users can access their own ratings" ON public.product_ratings FOR ALL USING (auth.uid()::text = user_id)';
        EXECUTE 'CREATE POLICY "Allow reading all ratings" ON public.product_ratings FOR SELECT USING (true)';
    END IF;
END $$;

-- Verify policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('products', 'product_analysis_fallback', 'scan_results', 'user_preferences')
ORDER BY tablename, policyname; 