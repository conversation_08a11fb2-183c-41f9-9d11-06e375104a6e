-- Quick RLS Fix for Safescan App
-- This script disables Row Level Security on main tables to allow app functionality
-- Use this for development/testing - for production, use rls_policies.sql instead

-- Disable R<PERSON> on main tables
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_analysis_fallback DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.scan_results DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences DISABLE ROW LEVEL SECURITY;

-- Optional: Also disable on other tables if they have RLS issues
ALTER TABLE public.saved_products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_ratings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.scan_logs DISABLE ROW LEVEL SECURITY;

-- Verify RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('products', 'product_analysis_fallback', 'scan_results', 'user_preferences');

-- This should show rowsecurity = false for all tables 