# SafeScan Database Scripts

This directory contains SQL scripts for setting up and maintaining the SafeScan database in Supabase.

## Scripts Overview

1. **add-nutrition-columns.sql**
   - Adds nutrition_facts_json and nutrition_concerns_json columns to the products table
   - Creates indexes for efficient queries
   - Adds an updated_at trigger

2. **create-nutrition-facts-table.sql**
   - Creates a dedicated table for structured nutrition facts data
   - Sets up foreign key relationship with products table
   - Adds indexes and triggers

3. **create-views.sql**
   - Creates views for product safety and nutrition summaries
   - Simplifies common queries

4. **maintenance-functions.sql**
   - Adds functions for database maintenance
   - Includes table analysis, reindexing, and orphaned record cleanup

5. **validation-functions.sql**
   - Adds functions and triggers to validate JSON data
   - Ensures data integrity

## How to Run These Scripts

### Option 1: Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste each script
4. Run them in the order listed above

### Option 2: Using Supabase CLI

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Log in to Supabase:
   ```
   supabase login
   ```

3. Edit the `run-all-scripts.sh` file to include your Supabase project ID

4. Make the script executable:
   ```
   chmod +x run-all-scripts.sh
   ```

5. Run the script:
   ```
   ./run-all-scripts.sh
   ```

## Verifying the Changes

After running the scripts, you can verify the changes by:

1. Checking the table structure in the Supabase dashboard
2. Running a test query against the new views:
   ```sql
   SELECT * FROM product_safety_summary LIMIT 10;
   SELECT * FROM product_nutrition_summary LIMIT 10;
   ```

3. Testing the maintenance functions:
   ```sql
   SELECT * FROM maintenance_analyze_tables();
   ```

## Maintenance Schedule

For optimal database performance, consider running these maintenance functions regularly:

- `maintenance_analyze_tables()`: Weekly
- `maintenance_reindex_tables()`: Monthly
- `cleanup_orphaned_records()`: Monthly

You can set up a cron job or scheduled function to run these automatically.
