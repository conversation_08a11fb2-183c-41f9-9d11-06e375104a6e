# SafeScan - Product Safety Scanner

SafeScan is a mobile application that helps users make informed decisions about product safety by scanning barcodes and analyzing ingredients for potential health risks.

## Features

- **Barcode Scanning**: Instantly scan product barcodes to get safety information
- **Ingredient Analysis**: Comprehensive analysis of product ingredients with safety ratings
- **Product Search**: Search for products by name or category
- **Safety Scoring**: Color-coded safety ratings (Green/Yellow/Red)
- **User Authentication**: Secure user accounts with Supabase
- **Saved Products**: Save and manage your favorite safe products
- **Personalized Recommendations**: Get recommendations based on your preferences
- **AI-Powered Analysis**: Utilizes Google's Gemini 2.0 models with intelligent fallbacks

## Tech Stack

- **Frontend**: Flutter
- **Navigation**: Flutter Go Router
- **Backend**: Supabase (Database, Authentication)
- **State Management**: Flutter Riverpod
- **Styling**: Flutter Theme & Material Design
- **AI Integration**: Google Gemini 2.0 with multi-tier fallbacks
- **Development**: Dart

## Prerequisites

1. **Node.js 18+** installed
2. **Expo CLI**: `npm install -g @expo/cli`
3. **Supabase account** with a project set up
4. **Cursor IDE** with MCP support (optional but recommended)

## Quick Start

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd SafeScan-Reinvented
npm install
```

### 2. Environment Setup

Create a `.env` file in the root directory:

```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here

# OpenFoodFacts Configuration
OPENFOODFACTS_USER_AGENT=SafeScan - AI-Powered Product Safety Scanner
OPENFOODFACTS_API_URL=https://world.openfoodfacts.org/api/v0/product

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
SEARCH_ENGINE_ID=your_search_engine_id_here

# App Environment
ENVIRONMENT=development
```

### 3. Cursor MCP Setup (Optional)

If you're using Cursor IDE, configure MCP for enhanced Supabase integration:

Update `.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@supabase/mcp-server"],
      "env": {
        "SUPABASE_URL": "https://your-project.supabase.co",
        "SUPABASE_ANON_KEY": "your_anon_key_here"
      }
    }
  }
}
```

### 4. Database Setup

Your Supabase database should include these tables:
- `products` - Product information and safety data
- `ingredients` - Ingredient database with safety levels
- `product_ingredients` - Many-to-many relationship
- `user_preferences` - User settings and preferences
- `saved_products` - User's saved products
- `user_scans` - Scan history

Refer to `Supabase-Structure-detailes.md` for the complete schema.

### 5. Run the App

```bash
# Start the development server
npx expo start

# For iOS
npx expo start --ios

# For Android
npx expo start --android

# If you encounter file watching issues on macOS
ulimit -n 65536 && npx expo start
```

## Project Structure

```
SafeScan-Reinvented/
├── app/                    # App screens and navigation
│   ├── auth/              # Authentication screens
│   ├── tabs/              # Tab-based navigation screens
│   ├── product/           # Product detail screens
│   ├── _layout.tsx        # Root layout with auth provider
│   └── index.tsx          # Entry point with auth routing
├── lib/                   # Utility functions and services
│   ├── auth/              # Authentication context and helpers
│   ├── supabase/          # Supabase client and database operations
│   └── analysis/          # Product safety analysis engine
├── components/            # Reusable UI components
├── constants/             # App constants and themes
├── assets/               # Images, icons, and static assets
├── .cursor/              # Cursor IDE configuration
│   └── mcp.json          # MCP server configuration
└── docs/                 # Documentation files
```

## Key Components

### Authentication
- **AuthContext**: Manages user authentication state
- **Sign-in/Sign-up**: User registration and login screens
- **Protected Routes**: Automatic redirection based on auth state

### Product Analysis
- **ProductAnalyzer**: Core safety analysis engine
- **Ingredient Analysis**: Detailed ingredient safety assessment
- **Safety Scoring**: 0-100 scale with risk categorization

### Database Integration
- **Supabase Client**: Enhanced client with MCP support
- **Database Helpers**: Simplified CRUD operations
- **Real-time Updates**: Live data synchronization

## MCP (Model Context Protocol) Integration

SafeScan integrates with Cursor's MCP to provide enhanced development experience:

### Benefits
- **Direct Database Access**: Query Supabase directly from Cursor
- **Real-time Data**: Access live product and ingredient data
- **Enhanced Debugging**: Better error tracking and logging
- **Seamless Development**: Reduced context switching

### Configuration
The MCP server is configured in `.cursor/mcp.json` and provides:
- Database schema introspection
- Real-time query execution
- Data validation and testing
- Enhanced error reporting

## AI Integration Details

SafeScan leverages Google's latest Gemini AI models for advanced product analysis:

### Gemini Model Configuration
- **Primary Models**: Uses auto-updated aliases to always access the latest stable models
  - `gemini-2.0-flash`: Primary model for text and vision capabilities
  - `gemini-2.0-flash-lite`: Cost-effective alternative for simpler analyses

### Intelligent Fallback System
- Automatic detection of rate limiting and API issues
- Seamless fallback to legacy stable models when needed:
  1. `gemini-2.0-flash-001`: Latest specific stable model (May 2025)
  2. `gemini-1.5-flash-002`: Legacy stable model as final fallback
- Exponential backoff retry mechanism for transient errors

### AI Capabilities
- Image-based product analysis (vision model)
- Ingredient safety assessment
- Product information extraction from unstructured data
- Web scraping assistance for hard-to-find products
- Personalized safety recommendations

## Troubleshooting

### Common Issues

**1. "EMFILE: too many open files" (macOS)**
```bash
ulimit -n 65536
npx expo start
```

**2. Supabase Connection Errors**
- Verify your `.env` file has correct credentials
- Check that your Supabase project is active
- Ensure RLS policies are properly configured

**3. Camera Permissions**
- iOS: Permissions requested automatically
- Android: Grant camera permissions in device settings

**4. MCP Server Issues**
- Verify `.cursor/mcp.json` configuration
- Check Cursor has MCP support enabled
- Ensure Supabase credentials are correct

**5. Dependency Version Conflicts**
```bash
npx expo install --check
npx expo-doctor
```

### Development Tips

1. **Use Expo Go** for quick testing on physical devices
2. **Enable Hot Reload** for faster development
3. **Check Metro Bundler** logs for build issues
4. **Use Flipper** for advanced debugging (optional)

## API Documentation

### Supabase Client
```typescript
import { db } from '@/lib/supabase/client';

// Search products
const products = await db.products.search('query');

// Get product by barcode
const product = await db.products.getByBarcode('123456789');

// Save product for user
await db.savedProducts.add(userId, productId);
```

### Product Analysis
```typescript
import { ProductAnalyzer } from '@/lib/analysis/productAnalyzer';

const analyzer = new ProductAnalyzer();
const analysis = await analyzer.analyzeProduct(product);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section above
- Review the setup guide in `setup.md`
- Check existing issues in the repository
- Create a new issue if needed

---

**SafeScan**: Empowering consumers with knowledge for safer choices. 