# SafeScan MCP Setup Guide

This guide explains how to set up the Multi-Channel Protocol (MCP) tables in your Supabase database to enable enhanced safety analysis features in the SafeScan app.

## What are MCP Tables?

MCP tables are additional database tables that enable advanced features in the SafeScan app:

1. **AI Analysis Caching**: Store analysis results to improve performance and reduce API costs
2. **Scan Event Tracking**: Track product scans for analytics and improvement
3. **Product Analysis Fallback**: Handle cases where primary analysis fails
4. **Pending Ingredients**: Process new ingredients discovered during analysis
5. **User Feedback**: Collect feedback to improve analysis quality

## Prerequisites

- A Supabase account with an existing project
- The SafeScan app codebase
- Flutter development environment set up
- Supabase credentials in your .env file

## Setting Up MCP Tables

### Option 1: Using the Migration Script (Recommended)

1. Ensure you have the .env file set up with your Supabase credentials:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   GEMINI_API_KEY=your_gemini_api_key
   ```

2. Run the migration script:
   ```bash
   dart run lib/scripts/run_migration.dart
   ```

3. Follow the prompts in the terminal. The script will:
   - Verify connection to your Supabase database
   - Execute SQL statements to create the required tables
   - Verify that tables were created successfully

### Option 2: Manual Setup

If you prefer to set up the tables manually:

1. Go to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy the contents of the `supabase_migration.sql` file
4. Run the SQL statements in the Supabase SQL Editor

## Verifying Setup

After setting up the MCP tables:

1. Restart the SafeScan app
2. The warning banner about enhanced features being unavailable should disappear
3. The app will automatically use the enhanced analysis features

## Tables Created

The migration adds the following tables to your database:

1. **ai_analysis_cache**: Stores cached analysis results for products
2. **scan_events**: Records product scan events
3. **product_analysis_fallback**: Stores fallback analysis data
4. **pending_ingredients**: Manages ingredients awaiting review
5. **user_feedback**: Collects user feedback on analysis results

## Troubleshooting

### The migration script fails to connect to Supabase

- Check that your .env file has the correct credentials
- Verify that your Supabase project is active
- Check your network connection

### Tables aren't being created properly

- Check if you have sufficient permissions in your Supabase project
- Look for error messages in the console output
- Try running the statements manually in the Supabase SQL Editor

### The app still shows the warning banner after migration

- Restart the app completely
- Check the console logs for any error messages
- Verify that all tables were created successfully

## Getting Help

If you encounter issues with the MCP setup, please:

1. Check the console logs for specific error messages
2. Refer to the Supabase documentation
3. Contact the SafeScan development team for assistance

---

Happy scanning with enhanced safety analysis features! 