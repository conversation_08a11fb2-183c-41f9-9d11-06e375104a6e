# SafeScan App UI Documentation

This document provides a comprehensive overview of the SafeScan app's user interface, describing each screen, component layout, and design elements to help recreate the exact look and feel of the application.

## Table of Contents
1. [App Theme and Design System](#app-theme-and-design-system)
2. [Splash Screen](#splash-screen)
3. [Authentication Screens](#authentication-screens)
4. [Home Screen](#home-screen)
5. [Scan Screen](#scan-screen)
6. [Product Details Screen](#product-details-screen)
7. [Search Screen](#search-screen)
8. [History/Saved Screen](#historysaved-screen)
9. [Profile Screen](#profile-screen)
10. [Navigation and Tab Bar](#navigation-and-tab-bar)

## App Theme and Design System

### Color Palette
- **Primary Colors**: 
  - Light Blue: `#e3f0ff` (background), `#2f95dc` (accent)
  - Dark Blue: `#0d47a1` (primary)
- **Secondary Colors**:
  - Safety Rating Colors: Green `#28a745`, Yellow `#ffc107`, Red `#dc3545`
- **Dark Mode Colors**:
  - Background: `#121212`
  - Card Background: Various darker shades
  - Text: `#E0E0E0`

### Typography
- **Font Family**: Uses SpaceMono and system fonts
- **Text Sizes**:
  - App Title: Large bold (18-24pt)
  - Card Titles: Medium (16-18pt)
  - Body Text: Standard (14-16pt)
  - Statistics: Large numbers (24-28pt)
  - Secondary Text: Smaller (12-14pt)

### Common UI Elements
- **Cards**: Rounded corners (borderRadius: 16)
- **Buttons**: Filled and outlined variants with consistent padding
- **Icons**: Using Lucide React Native and FontAwesome
- **Gradients**: LinearGradient components for card backgrounds
- **Shadows**: Consistent shadow properties for elevation

## Splash Screen

The app features a clean splash screen with:
- Background color: White/Dark depending on theme
- Centered SafeScan logo
- Lottie animation (RippleLoadingScreen.json) providing visual feedback
- "Loading..." text below the animation

## Authentication Screens

### Sign In Screen
- **Layout**: Vertical layout with centered components
- **Elements**:
  - SafeScan logo at the top
  - Welcome text and description
  - Email and Password input fields
  - "Sign In" button (primary blue)
  - "Forgot Password?" link
  - "Don't have an account? Sign Up" link
  - Optional "Sign in with Google" button
  - Error messages appear below the relevant input field

### Sign Up Screen (Modal)
- **Layout**: Presented as a modal with slide-up animation
- **Elements**:
  - Close button (X) at the top right
  - "Create Account" title
  - Name, Email, Password input fields
  - Password strength indicator
  - "Sign Up" button
  - Terms and Privacy Policy text
  - "Already have an account? Sign In" link

## Home Screen

### Header Section
- **Layout**: Fixed header with logo and welcome message
- **Elements**:
  - SafeScan logo (left-aligned)
  - App name "SafeScan" and personalized welcome message
  - Light blue background (`#e3f0ff`)

### Statistics Section
- **Layout**: Grid layout with 2-3 cards per row
- **Cards**:
  - **Total Scans Card**: 
    - Blue gradient background
    - Camera icon in circle
    - Total scans count in large font
    - "Total Scans" label
  
  - **Safe Products Card**:
    - Green gradient background
    - CheckCircle icon in circle
    - Safe products count
    - "Safe Products" label
  
  - **Saved Items Card**:
    - Purple/blue gradient background
    - Star icon in circle
    - Saved items count
    - "Saved Items" label

### Charts Section
- **Layout**: Full-width cards with padding
- **Elements**:
  - **Scan Activity Chart**: 
    - Line chart showing scan activity over the past 7 days
    - Dates on x-axis, scan count on y-axis
    - Blue line with dots

  - **Safety Distribution Chart**:
    - Progress/Pie chart showing distribution of safe, caution, unsafe products
    - Color-coded segments (green, yellow, red)
    - Percentage labels

### Quick Actions Section
- **Layout**: Horizontal scrollable row of action buttons
- **Buttons**:
  - "Scan New Product" button with camera icon
  - "Search Products" button with search icon
  - "View History" button with history icon

### Information Cards
- **Layout**: Vertical stack of full-width cards
- **Cards**:
  - **Safety Evaluation Criteria Card**:
    - Expandable card with information about safety ratings
    - Color-coded explanation of green, yellow, and red ratings
  
  - **Recently Scanned Products Card** (if any):
    - List of recent product scans with:
      - Product name
      - Brand
      - Safety rating indicator
      - Date scanned
      - Chevron right icon for navigation

## Scan Screen

### Camera View
- **Layout**: Full-screen camera view with overlay elements
- **Elements**:
  - Close button (X) at top left
  - Flash toggle button at top right
  - Centered scan frame with animated border
  - Instructional text below scan frame
  - Action buttons at bottom

### Barcode Scanning Mode
- **Scan Frame**: Rectangular frame sized for barcodes
- **Instructions**: "Position barcode in the frame"
- **Controls**:
  - "Capture" button (large circular button)
  - Manual entry option text below

### Product Image Scanning Mode
- **Scan Frame**: Square frame for product front
- **Instructions**: "Position product in the frame"
- **Controls**:
  - "Capture" button (large circular button)
  - Back button to return to barcode scanning

### Processing Overlay
- **Layout**: Full-screen semi-transparent overlay
- **Elements**:
  - LottieView animation at center
  - "Processing..." or "Analyzing..." text
  - Cancel button

## Product Details Screen

### Header Section
- **Layout**: Image banner at top with product image
- **Elements**:
  - Back button (top left)
  - Save/bookmark button (top right)
  - Product image as background
  - Gradient overlay for text visibility
  - Product name and brand in large text

### Safety Score Section
- **Layout**: Prominent card with safety visualization
- **Elements**:
  - Circular safety score indicator
  - Score percentage in large text
  - Color-coded rating (Green/Yellow/Red)
  - "Safety Score" label
  - Rating explanation text

### Ingredients Section
- **Layout**: Collapsible card with list
- **Elements**:
  - "Ingredients" section header
  - List of ingredients with:
    - Ingredient name
    - Safety indicator dot (Green/Yellow/Red)
    - Information button for details
  - Expand/collapse controls

### Safety Analysis Section
- **Layout**: Card with detailed analysis
- **Elements**:
  - "Safety Analysis" section header
  - Potential concerns listed with:
    - Icon indicators (AlertTriangle, Info)
    - Concern description
    - Severity level

### Nutritional Information
- **Layout**: Card with table layout
- **Elements**:
  - "Nutritional Information" section header
  - Serving size information
  - Nutrition facts table with:
    - Nutrients, amounts, and % daily values
  - Color-coded highlights for concerning values

### Alternatives Section
- **Layout**: Horizontal scrollable card
- **Elements**:
  - "Safer Alternatives" section header
  - Horizontally scrollable product cards with:
    - Product image
    - Name and brand
    - Safety score indicator
    - "View" button

## Search Screen

### Search Header
- **Layout**: Full-width search bar at top
- **Elements**:
  - Search input with clear button
  - Filter/category buttons below
  - Recent searches list (when no query)

### Search Results
- **Layout**: Vertical scrollable list
- **Elements**:
  - Product cards with:
    - Product image (left)
    - Product name and brand (right)
    - Safety score indicator (right)
    - Chevron navigation icon

### Empty State
- **Layout**: Centered content when no results
- **Elements**:
  - SearchIllustration component
  - "No results found" text
  - Suggestion text for trying different keywords

## History/Saved Screen

### Tabs Navigation
- **Layout**: Horizontal tabs at top
- **Elements**:
  - "Scan History" tab
  - "Saved Products" tab
  - Sliding indicator for active tab

### Product List
- **Layout**: Vertical scrollable list
- **Elements**:
  - Product cards with:
    - Product image
    - Product name and brand
    - Safety score indicator (color-coded)
    - Date scanned/saved
    - Chevron navigation icon or swipe actions

### Filter Controls
- **Layout**: Row of filter options below tabs
- **Elements**:
  - Date range filter
  - Safety rating filter
  - Category filter
  - Sort controls (newest/oldest)

## Profile Screen

### User Info Section
- **Layout**: Card with user information
- **Elements**:
  - User avatar/image
  - User name
  - Email address
  - "Edit Profile" button

### Preferences Section
- **Layout**: List of preference options
- **Elements**:
  - "Appearance" option (Dark/Light mode)
  - "Notifications" toggle
  - "Safety Concerns" selection 
  - "Language" option

### Settings Section
- **Layout**: List of setting options
- **Elements**:
  - "Account Settings" option
  - "Privacy & Security" option
  - "Help & Support" option
  - "About SafeScan" option
  - "Sign Out" button (red text)

## Navigation and Tab Bar

### Custom Tab Bar
- **Layout**: Bottom tab bar with 5 tabs
- **Elements**:
  - **Home Tab**: House icon
  - **Search Tab**: Search icon
  - **Scan Tab**: Large circular scan button (elevated)
  - **History Tab**: Clock/bookmark icon
  - **Profile Tab**: User icon
- **Design**:
  - Background color matches theme
  - Active tab has filled icon and indicator
  - Scan button is larger and more prominent
  - Smooth animations on tab changes

---

## How to Recreate This App

### Setup and Dependencies
1. Create a React Native Expo project
2. Install required dependencies:
   ```
   npm install expo-router react-native-chart-kit expo-linear-gradient 
   expo-blur lottie-react-native lucide-react-native @expo/vector-icons
   react-native-reanimated @supabase/supabase-js
   ```

### Project Structure
Follow the structure in this codebase:
- `/app` - Main screens and navigation
- `/app/(tabs)` - Tab-based screens
- `/components` - Reusable UI components
- `/constants` - Theme and configuration
- `/context` - State management (Auth, Theme)
- `/lib` - Utility functions and API connections
- `/assets` - Images, animations, and fonts

### Theme Implementation
1. Create Color schemes in `constants/Colors.ts`
2. Implement ThemeContext for theme switching
3. Use useColorScheme hook to access current theme

### Component Building
1. Start with core components like Button, Card, SearchBar
2. Build screen layouts following the structure described above
3. Implement animations using Reanimated and Lottie
4. Use LinearGradient for card backgrounds
5. Implement Charts with react-native-chart-kit

### Navigation Setup
1. Use Expo Router for navigation
2. Set up tabs in `/app/(tabs)/_layout.tsx`
3. Create stack navigation for non-tab screens
4. Implement custom tab bar component

Following this documentation will allow you to recreate the SafeScan app with its exact look and feel across all screens.
