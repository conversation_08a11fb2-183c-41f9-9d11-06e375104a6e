# Cursor MCP Server Guide for SafeScan

This guide explains how to use the Multi-Channel Protocol (MCP) server with Cursor IDE for the SafeScan project.

## What is the MCP Server?

The MCP (Multi-Channel Protocol) server is a custom integration between Cursor IDE and the Supabase backend for SafeScan. It provides:

- Real-time database verification
- Schema validation
- Connection monitoring
- Database health checks

## Getting Started

### Prerequisites

- Flutter SDK installed
- Cursor IDE
- Access to Supabase project

### Setup

1. Clone the SafeScan repository
2. Make sure the `.cursor/mcp` directory exists
3. Verify the configuration in `.cursor/mcp/config.json`

## Running the MCP Server

You can start the MCP server using one of these methods:

### Method 1: Using the Shell Script

```bash
./.cursor/mcp/run_mcp_server.sh
```

### Method 2: Using Dart Directly

```bash
dart .cursor/mcp/run_server.dart
```

## Verifying the Database

Before using the MCP server, it's recommended to verify your database:

```bash
dart .cursor/mcp/verify_database.dart
```

This will:
- Check if all required tables exist
- Validate table schemas
- Generate a verification report
- Provide recommendations for fixes

## Fixing Database Issues

If the verification shows problems:

1. Run the SQL migration scripts:

```bash
# Using Supabase CLI (if installed)
supabase db push Supabase/migrations/fix_schema_issues.sql

# Or copy the SQL and run it in the Supabase SQL Editor
```

2. Re-run the verification to confirm fixes:

```bash
dart .cursor/mcp/verify_database.dart
```

## Cursor Integration Features

When the MCP server is running, Cursor will:

1. Show a status indicator in the status bar
2. Enable MCP-specific commands
3. Provide database connection information

## MCP Commands in Cursor

The following commands are available in Cursor when MCP is running:

- `MCP: Check Database Status` - Verify database connectivity
- `MCP: Run Schema Verification` - Check database schemas
- `MCP: Apply SQL Migrations` - Run migration scripts
- `MCP: Start/Stop Server` - Control the MCP server

## Troubleshooting

### Server Won't Start

1. Check if the required packages are installed:
   ```bash
   dart pub add http
   ```

2. Verify your Supabase credentials in `.cursor/mcp/config.json`

3. Check the logs in `.cursor/mcp/logs/mcp_server.log`

### Database Verification Fails

1. Make sure the Supabase project is accessible
2. Run the SQL migration script in `Supabase/migrations/fix_schema_issues.sql`
3. Check if the required tables exist in Supabase

### Connection Issues

1. Verify the Supabase URL and API key
2. Check your internet connection
3. Ensure the Supabase project is active

## Next Steps

1. Run the MCP server
2. Verify the database structure
3. Apply any necessary migrations
4. Start using SafeScan with full Supabase integration 