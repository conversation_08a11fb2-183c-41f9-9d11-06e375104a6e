# SafeScan Database Architecture Documentation

## Overview

SafeScan uses **Supabase** as its backend database solution, which is built on PostgreSQL. The database is designed to support product scanning, ingredient analysis, user management, and safety assessments. This document provides a comprehensive overview of the database structure, relationships, and how it's utilized throughout the application.

## Database Provider: Supabase

- **Platform**: Supabase (PostgreSQL-based)
- **Authentication**: Built-in Supabase Auth with Row Level Security (RLS)
- **Real-time**: Supports real-time subscriptions
- **Storage**: Integrated file storage for product images
- **API**: Auto-generated REST and GraphQL APIs

## Core Database Tables

### 1. Products Table (`public.products`)

The main table storing scanned product information.

```sql
CREATE TABLE public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    brand VARCHAR(255),
    barcode VARCHAR(255),
    image_url TEXT,
    safety_score INTEGER NOT NULL,
    safety_rating TEXT NOT NULL,
    scanned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    ingredients_json JSONB,
    alternatives_json JSONB,
    nutrition_facts_json JSONB,
    nutrition_concerns_json JSONB,
    match_stats_json JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Key Features:**
- Stores product metadata and analysis results
- Links to authenticated users via `user_id`
- Uses JSONB for flexible ingredient and nutrition data storage
- Includes safety scoring (0-100) and rating (safe/not safe)
- Tracks scan timestamps for analytics

**Indexes:**
- `idx_products_user_id` - Fast user-specific queries

### 2. Ingredients Table (`public.ingredients`)

Master table containing ingredient information and safety data.

```sql
CREATE TABLE public.ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255),
    description TEXT,
    scientific_name VARCHAR(255),
    common_aliases TEXT[],
    health_risks TEXT[],
    safety_level VARCHAR(50) NOT NULL, -- 'green', 'yellow', 'red'
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Key Features:**
- Central repository for ingredient safety information
- Supports multiple aliases for ingredient matching
- Categorized safety levels (green=safe, yellow=caution, red=unsafe)
- Extensible with scientific names and health risk data

**Indexes:**
- `idx_ingredients_name` - Fast ingredient name searches

### 3. Product Ingredients Junction Table (`public.product_ingredients`)

Links products to their ingredients with many-to-many relationship.

```sql
CREATE TABLE public.product_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES public.ingredients(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(product_id, ingredient_id)
);
```

**Key Features:**
- Maintains relationships between products and ingredients
- Prevents duplicate ingredient associations
- Cascading deletes maintain data integrity

### 4. Saved Products Table (`public.saved_products`)

Tracks user's saved/bookmarked products.

```sql
CREATE TABLE public.saved_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    saved_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);
```

**Key Features:**
- User-specific product bookmarking
- Prevents duplicate saves
- Tracks when products were saved

**Indexes:**
- `idx_saved_products_user_id` - Fast user-specific queries

### 5. User Preferences Table (`public.user_preferences`)

Stores user-specific settings and preferences.

```sql
CREATE TABLE public.user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    allergens UUID[] DEFAULT '{}',
    avoided_ingredients TEXT[] DEFAULT '{}',
    is_premium BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Key Features:**
- One-to-one relationship with users
- Stores allergen preferences
- Tracks premium subscription status
- Maintains list of ingredients to avoid

### 6. Allergens Table (`public.allergens`)

Reference table for common allergens.

```sql
CREATE TABLE public.allergens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    severity VARCHAR(50) DEFAULT 'medium',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Pre-populated with common allergens:**
- Peanuts, Tree Nuts, Milk, Eggs, Fish, Shellfish
- Soy, Wheat, Sesame, Gluten, Sulfites, Mustard

### 7. Dietary Preferences Table (`public.dietary_preferences`)

Reference table for dietary restrictions and preferences.

```sql
CREATE TABLE public.dietary_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Pre-populated with common diets:**
- Vegetarian, Vegan, Gluten-Free, Dairy-Free
- Keto, Paleo, Kosher, Halal, Low FODMAP, Low Sodium

### 8. Avoided Ingredients Table (`public.avoided_ingredients`)

User-specific ingredients to avoid.

```sql
CREATE TABLE public.avoided_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, name)
);
```

## Extended Tables for Analytics and Processing

### 9. Scan Results Table (`public.scan_results`)

Stores detailed scan processing results and metadata.

```sql
CREATE TABLE public.scan_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
    barcode VARCHAR(255),
    product_name VARCHAR(255),
    brand VARCHAR(255),
    scan_data_json JSONB NOT NULL,
    scan_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    source VARCHAR(100),
    confidence_score FLOAT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**Key Features:**
- Tracks scan processing metadata
- Stores confidence scores for quality assessment
- Links to products but allows orphaned records
- Supports analytics and improvement tracking

### 10. Pending Ingredients Table (`public.pending_ingredients`)

Queue for new ingredients awaiting review.

```sql
CREATE TABLE public.pending_ingredients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    details JSONB,
    source VARCHAR(100),
    date_found TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    reviewed BOOLEAN DEFAULT FALSE
);
```

**Key Features:**
- Staging area for new ingredient discoveries
- Supports manual review workflow
- Tracks data source for quality control

### 11. User Scan History Table (`public.user_scan_history`)

Detailed tracking of user scanning activity.

```sql
CREATE TABLE public.user_scan_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    scanned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, product_id, scanned_at)
);
```

**Key Features:**
- Detailed scan activity tracking
- Supports analytics and user behavior analysis
- Allows multiple scans of same product

## Security: Row Level Security (RLS)

All user-related tables implement Row Level Security policies:

### Products Policies
```sql
-- Users can only access their own products
CREATE POLICY "Users can view their own products" ON public.products
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own products" ON public.products
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### Saved Products Policies
```sql
-- Users can only access their own saved products
CREATE POLICY "Users can view their own saved products" ON public.saved_products
  FOR SELECT USING (auth.uid() = user_id);
```

### User Preferences Policies
```sql
-- Users can only access their own preferences
CREATE POLICY "Users can view their own preferences" ON public.user_preferences
  FOR SELECT USING (auth.uid() = user_id);
```

## Database Utilization in the Application

### 1. Authentication Integration

The app uses Supabase Auth with the following configuration:

```typescript
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: SecureStorageAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  }
});
```

**Features:**
- Secure token storage with custom adapter
- Automatic token refresh
- PKCE flow for mobile security
- OAuth integration (Google Sign-In)

### 2. Product Management

**Saving Products:**
```typescript
export const saveProduct = async (productData: any): Promise<any> => {
  const { data, error } = await supabase
    .from('products')
    .insert([productData])
    .select();
  
  return { data, error };
};
```

**Fetching User Products:**
```typescript
const { data: products } = await supabase
  .from('products')
  .select('*')
  .eq('user_id', user.id)
  .order('scanned_at', { ascending: false });
```

### 3. Ingredient Management

**Dynamic Ingredient Addition:**
```typescript
export const addNewIngredientToDatabase = async (
  ingredient: AnalyzedIngredient
): Promise<any | null> => {
  const newIngredient = {
    name: ingredient.name,
    description: ingredient.description || '',
    safety_level: safetyLevel,
    health_risks: healthRisks,
    common_aliases: aliases,
    category: category,
  };

  const { data, error } = await supabase
    .from('ingredients')
    .insert([newIngredient])
    .select();
};
```

**Ingredient Matching:**
```typescript
export const searchIngredients = async (query: string) => {
  const { data, error } = await supabase
    .from('ingredients')
    .select('*')
    .or(`name.ilike.%${query}%, common_aliases.cs.{${query}}`);
};
```

### 4. User Analytics

**Scan Statistics:**
```typescript
// Total scans count
const { count: totalCount } = await supabase
  .from('products')
  .select('*', { count: 'exact', head: true })
  .eq('user_id', user.id);

// Safe products count
const { count: safeCount } = await supabase
  .from('products')
  .select('*', { count: 'exact', head: true })
  .eq('user_id', user.id)
  .gte('safety_score', 80);
```

**Scan History for Charts:**
```typescript
const { data: historyData } = await supabase
  .from('products')
  .select('scanned_at')
  .eq('user_id', user.id)
  .gte('scanned_at', sevenDaysAgo.toISOString())
  .order('scanned_at', { ascending: true });
```

### 5. Offline Support

The app implements offline capabilities through:

- **Local caching** of frequently accessed data
- **Queue-based operations** for offline actions
- **Sync mechanisms** when connectivity returns
- **Conflict resolution** for concurrent updates

### 6. Data Synchronization

**Ingredient Database Updates:**
```typescript
export const queueNewIngredientsForInsertion = async (
  ingredients: AnalyzedIngredient[]
): Promise<boolean> => {
  // Filter existing ingredients
  // Queue new ingredients for insertion
  // Batch insert operations
};
```

**Pending Review System:**
```typescript
export const savePendingIngredient = async (ingredient: any) => {
  const { data, error } = await supabase
    .from('pending_ingredients')
    .insert([{
      name: ingredient.name,
      details: ingredient,
      source: 'user_scan',
      reviewed: false
    }]);
};
```

## Performance Optimizations

### 1. Indexing Strategy
- User-specific indexes for fast filtering
- Composite indexes for common query patterns
- Text search indexes for ingredient matching

### 2. Query Optimization
- Selective field fetching with `.select()`
- Pagination for large datasets
- Efficient filtering with RLS policies

### 3. Caching Strategy
- Local storage for frequently accessed data
- In-memory caching for session data
- Background sync for data freshness

### 4. Connection Management
- Connection pooling through Supabase
- Automatic retry mechanisms
- Graceful degradation for offline scenarios

## Data Migration and Versioning

The database includes migration scripts for:

1. **Schema updates** - Adding new columns and tables
2. **Data transformations** - Converting existing data formats
3. **Index optimization** - Adding performance improvements
4. **Security updates** - Updating RLS policies

Example migration:
```sql
-- Migration: Add brand_id to products table
ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS brand_id UUID REFERENCES public.brands(id);
```

## Monitoring and Analytics

### Database Metrics Tracked:
- Query performance and execution times
- User activity patterns
- Data growth rates
- Error rates and types
- Cache hit/miss ratios

### Health Checks:
- Connection status monitoring
- Schema validation
- Data integrity checks
- Performance threshold alerts

## Backup and Recovery

Supabase provides:
- **Automated daily backups**
- **Point-in-time recovery**
- **Cross-region replication**
- **Export capabilities** for data portability

## Future Enhancements

Planned database improvements:
1. **Advanced analytics tables** for business intelligence
2. **Machine learning integration** for better ingredient matching
3. **Community features** with user-generated content
4. **Enhanced search capabilities** with full-text search
5. **Real-time notifications** for product updates

---

This database architecture supports SafeScan's core functionality while providing scalability, security, and performance for a growing user base. The design emphasizes data integrity, user privacy, and efficient querying patterns essential for a mobile application focused on product safety analysis.
