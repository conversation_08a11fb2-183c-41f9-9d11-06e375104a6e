1. scan_results 📸
id (uuid)

product_id (uuid)

barcode (text)

product_name (text)

brand (text)

alternatives_json (jsonb)

confidence_score (numeric)

source (text)

scan_date (timestamp)

user_id (uuid)

created_at (timestamp)

updated_at (timestamp)

2. pending_ingredients ⏳🌿
id (uuid)

name (text)

category (text)

description (text)

scientific_name (text)

common_names (text)

health_risks (text)

safety_level (text)

confidence_score (numeric)

submitted_by (uuid)

status (text)

review_notes (text)

reviewed_by (uuid)

reviewed_at (timestamp)

created_at (timestamp)

updated_at (timestamp)

3. scan_logo 🖼️
id (uuid)

symbols (text)

report_id (text)

timestamp (timestamp)

user_id (uuid)

4. dietary_preferences 🥗
id (uuid)

user_id (uuid)

name (text)

description (text)

created_at (timestamp)

5. auth.users 👤
id (uuid)

6. product_ingredients 🔗
id (uuid)

product_id (uuid)

ingredient_id (uuid)

created_at (timestamp)

7. products 🛍️
id (uuid)

name (text)

barcode (text)

image (text)

edited_image (text)

safety_score (numeric)

user_id (uuid)

brand_id (uuid)

alternatives_json (jsonb)

nutrition_facts_json (jsonb)

nutriscore_grade (text)

ecoscore_grade (text)

nova_group (text)

carbon_footprint_percent_of_known_ingredients (numeric)

created_at (timestamp)

updated_at (timestamp)

created_by (uuid)

last_updated_by (uuid)

8. ingredients 🌿
id (uuid)

name (text)

description (text)

common_names (text)

health_risks (text)

safety_level (text)

created_at (timestamp)

9. database_version 💾
version (text)

description (text)

last_updated (timestamp)

10. saved_products 💾❤️
id (uuid)

user_id (uuid)

product_id (uuid)

reason (text)

created_at (timestamp)

updated_at (timestamp)

11. avoided_ingredients 🚫🌿
id (uuid)

user_id (uuid)

ingredient_id (uuid)

reason (text)

created_at (timestamp)

12. allergens ⚠️
id (uuid)

user_id (uuid)

name (text)

description (text)

severity (text)

created_at (timestamp)

13. brands ™️
id (uuid)

name (text)

created_at (timestamp)

updated_at (timestamp)

14. ingredients_lookup 🔍🌿
id (uuid)

name (text)

description (text)

common_uses (text)

health_risks (text)

safety_level (text)

created_at (timestamp)

15. profiles 🧑
id (uuid)

email (text)

name (text)

is_premium (boolean)

created_at (timestamp)

updated_at (timestamp)

16. unknown_barcodes ❓🏷️
id (uuid)

barcode (text)

count (numeric)

first_scan (timestamp)

last_scan (timestamp)

user_id (uuid)

status (text)

17. user_preferences ⚙️
id (uuid)

user_id (uuid)

allergens (text)

avoid_ingredients (text)

created_at (timestamp)

updated_at (timestamp)

18. product_analysis_fallback 📊
id (uuid)

barcode (varchar)

product_name (varchar)

analysis_data (jsonb)

source (varchar)

user_id (uuid)

created_at (timestamptz)

updated_at (timestamptz)