# SafeScan MCP Protocol Guide

## Overview

The SafeScan app uses a Multi-Channel Protocol (MCP) to communicate with the Supabase backend. This guide explains how to use, test, and extend the MCP protocol.

## Quick Start

### Testing the Connection

To quickly test if the MCP protocol is working:

```bash
flutter run -d chrome lib/mcp_test.dart
```

This will run a comprehensive test of the MCP connection and report the results.

### Adding to an Existing Project

To use the MCP protocol in your own Flutter project:

1. Add the required dependencies:
   ```yaml
   dependencies:
     supabase_flutter: ^2.8.0
     flutter_dotenv: ^5.1.0
   ```

2. Create a `.env` file with your Supabase credentials:
   ```
   SUPABASE_URL=https://your-project-url.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   ```

3. Initialize the connection:
   ```dart
   import 'package:flutter_dotenv/flutter_dotenv.dart';
   import 'package:safescan_flutter/services/supabase_init_service.dart';

   // In your main() function
   await dotenv.load(fileName: ".env");
   final initialized = await SupabaseInitService.initialize();
   ```

## Main Components

### SupabaseInitService

Handles initialization and connection verification:

```dart
// Initialize Supabase
await SupabaseInitService.initialize();

// Check for specific tables
final tableStatus = await SupabaseInitService.checkTables(['products', 'ingredients']);

// Get Supabase client
final supabase = SupabaseInitService.client;
```

### SupabaseService

Handles all data operations:

```dart
// Get all ingredients
final ingredients = await SupabaseService.getAllIngredients();

// Search for specific ingredients
final results = await SupabaseService.searchIngredients('sodium benzoate');

// Get user's saved products
final savedProducts = await SupabaseService.getSavedProducts();
```

### DatabaseVerificationService

Tests database connectivity and structure:

```dart
// Quick health check
final isHealthy = await DatabaseVerificationService.quickHealthCheck();

// Comprehensive verification
final report = await DatabaseVerificationService.verifyIngredientDatabase();
```

## Best Practices

1. **Always initialize first**: Call `SupabaseInitService.initialize()` before any other Supabase operations.

2. **Error handling**: Wrap Supabase calls in try/catch blocks to handle connectivity issues.

3. **Check table existence**: Use `SupabaseInitService.checkTables()` to verify required tables exist.

4. **Fall back gracefully**: Implement fallbacks for when the server is unavailable.

5. **Validate responses**: Don't assume data structure, validate responses before processing.

## Troubleshooting

### Connection Issues

If you're having trouble connecting:

1. Verify your `.env` file has the correct credentials.
2. Check internet connectivity.
3. Run the `mcp_test.dart` script to diagnose issues.

### Missing Tables

If tables are missing:

1. Run the SQL migration script in `Supabase/migrations/missing_tables.sql`.
2. Verify the migration completed successfully.

### Data Type Errors

If you're getting type errors:

1. Check the model classes for proper JSON parsing.
2. Ensure the database schema matches the expected structure.

## Extending the Protocol

To add new functionality:

1. Add new methods to `SupabaseService` for data operations.
2. Update models to handle new data structures.
3. Add tests to verify the new functionality.
4. Document the changes for other developers. 