# Enhanced Supabase Database Schema for AI-Powered SafeScan

## New Tables for AI Integration

### 1. AI Analysis Cache Table
```sql
CREATE TABLE ai_analysis_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id TEXT NOT NULL, -- Barcode or unique product identifier
    analysis_data JSONB NOT NULL, -- Complete AI analysis result
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for fast product lookup
CREATE INDEX idx_ai_analysis_cache_product_id ON ai_analysis_cache(product_id);
CREATE INDEX idx_ai_analysis_cache_created_at ON ai_analysis_cache(created_at);
```

### 2. Enhanced User Preferences Table
```sql
-- Extend existing user_preferences table
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS preferred_brands TEXT[];
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS safety_threshold INTEGER DEFAULT 70;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS ai_analysis_enabled BOOLEAN DEFAULT true;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS personalized_recommendations BOOLEAN DEFAULT true;
```

### 3. User Feedback Table
```sql
CREATE TABLE user_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL,
    feedback_type TEXT NOT NULL CHECK (feedback_type IN ('ai_accuracy', 'safety_rating', 'ingredient_accuracy', 'recommendation_quality')),
    feedback_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for analytics
CREATE INDEX idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX idx_user_feedback_product_id ON user_feedback(product_id);
CREATE INDEX idx_user_feedback_type ON user_feedback(feedback_type);
CREATE INDEX idx_user_feedback_created_at ON user_feedback(created_at);
```

### 4. Product Ratings Table
```sql
CREATE TABLE product_ratings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    safety_rating INTEGER CHECK (safety_rating >= 1 AND safety_rating <= 5),
    review TEXT,
    additional_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one rating per user per product
    UNIQUE(user_id, product_id)
);

-- Indexes
CREATE INDEX idx_product_ratings_product_id ON product_ratings(product_id);
CREATE INDEX idx_product_ratings_user_id ON product_ratings(user_id);
```

### 5. Enhanced Ingredients Table
```sql
-- Extend existing ingredients table for AI data
ALTER TABLE ingredients ADD COLUMN IF NOT EXISTS ai_analyzed BOOLEAN DEFAULT false;
ALTER TABLE ingredients ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2);
ALTER TABLE ingredients ADD COLUMN IF NOT EXISTS benefits TEXT[];
ALTER TABLE ingredients ADD COLUMN IF NOT EXISTS regulatory_status TEXT;
ALTER TABLE ingredients ADD COLUMN IF NOT EXISTS scientific_references TEXT[];
```

## Row Level Security (RLS) Policies

### AI Analysis Cache
```sql
-- Enable RLS
ALTER TABLE ai_analysis_cache ENABLE ROW LEVEL SECURITY;

-- Anyone can read cached analysis (public data)
CREATE POLICY "Public read access for ai_analysis_cache" ON ai_analysis_cache
    FOR SELECT USING (true);

-- Only authenticated users can insert/update
CREATE POLICY "Authenticated users can insert ai_analysis_cache" ON ai_analysis_cache
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

### User Feedback
```sql
-- Enable RLS
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- Users can only see their own feedback
CREATE POLICY "Users can view own feedback" ON user_feedback
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own feedback
CREATE POLICY "Users can insert own feedback" ON user_feedback
    FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### Product Ratings
```sql
-- Enable RLS
ALTER TABLE product_ratings ENABLE ROW LEVEL SECURITY;

-- Users can view all product ratings (public reviews)
CREATE POLICY "Public read access for product_ratings" ON product_ratings
    FOR SELECT USING (true);

-- Users can only insert/update their own ratings
CREATE POLICY "Users can manage own ratings" ON product_ratings
    FOR ALL USING (auth.uid() = user_id);
```

## Functions for Analytics

### 1. AI Performance Metrics Function
```sql
CREATE OR REPLACE FUNCTION get_ai_performance_metrics(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_analyses', (
            SELECT COUNT(*) 
            FROM ai_analysis_cache 
            WHERE created_at > NOW() - INTERVAL '30 days'
        ),
        'user_feedback_count', (
            SELECT COUNT(*) 
            FROM user_feedback 
            WHERE user_id = user_uuid 
            AND feedback_type = 'ai_accuracy'
        ),
        'average_accuracy_rating', (
            SELECT AVG((feedback_data->>'accuracy_rating')::numeric)
            FROM user_feedback 
            WHERE user_id = user_uuid 
            AND feedback_type = 'ai_accuracy'
        ),
        'cache_hit_rate', (
            SELECT 
                ROUND(
                    (COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '7 days'))::numeric / 
                    NULLIF(COUNT(*), 0) * 100, 2
                )
            FROM ai_analysis_cache
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. Enhanced User Stats Function
```sql
CREATE OR REPLACE FUNCTION get_enhanced_user_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_scans', (
            SELECT COUNT(*) FROM products WHERE user_id = user_uuid
        ),
        'safe_products', (
            SELECT COUNT(*) FROM products 
            WHERE user_id = user_uuid AND safety_score >= 80
        ),
        'saved_products', (
            SELECT COUNT(*) FROM saved_products WHERE user_id = user_uuid
        ),
        'avg_safety_score', (
            SELECT ROUND(AVG(safety_score), 1) 
            FROM products WHERE user_id = user_uuid
        ),
        'products_this_month', (
            SELECT COUNT(*) FROM products 
            WHERE user_id = user_uuid 
            AND scanned_at > DATE_TRUNC('month', NOW())
        ),
        'ai_analyses_used', (
            SELECT COUNT(*) FROM ai_analysis_cache 
            WHERE product_id IN (
                SELECT barcode FROM products WHERE user_id = user_uuid
            )
        ),
        'feedback_provided', (
            SELECT COUNT(*) FROM user_feedback WHERE user_id = user_uuid
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Product Recommendation Function
```sql
CREATE OR REPLACE FUNCTION get_product_recommendations(user_uuid UUID, product_category TEXT DEFAULT NULL)
RETURNS TABLE(
    product_name TEXT,
    brand TEXT,
    safety_score INTEGER,
    avg_rating DECIMAL,
    recommendation_reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.name,
        p.brand,
        p.safety_score,
        AVG(pr.safety_rating) as avg_rating,
        CASE 
            WHEN p.safety_score >= 90 THEN 'Excellent safety profile'
            WHEN p.safety_score >= 80 THEN 'Good safety rating'
            WHEN AVG(pr.safety_rating) >= 4 THEN 'Highly rated by users'
            ELSE 'Popular choice'
        END as recommendation_reason
    FROM products p
    LEFT JOIN product_ratings pr ON p.id = pr.product_id
    WHERE p.safety_score >= 70
    AND (product_category IS NULL OR p.category = product_category)
    AND p.user_id != user_uuid -- Exclude user's own products
    GROUP BY p.id, p.name, p.brand, p.safety_score
    HAVING COUNT(pr.id) >= 3 -- Products with at least 3 ratings
    ORDER BY p.safety_score DESC, AVG(pr.safety_rating) DESC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Triggers for Data Integrity

### 1. Update AI Analysis Cache Timestamp
```sql
CREATE OR REPLACE FUNCTION update_ai_cache_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_ai_cache_timestamp
    BEFORE UPDATE ON ai_analysis_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_cache_timestamp();
```

### 2. Clean Old Cache Entries
```sql
CREATE OR REPLACE FUNCTION cleanup_old_ai_cache()
RETURNS void AS $$
BEGIN
    -- Delete cache entries older than 30 days
    DELETE FROM ai_analysis_cache 
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Schedule this function to run daily using pg_cron extension
-- SELECT cron.schedule('cleanup-ai-cache', '0 2 * * *', 'SELECT cleanup_old_ai_cache();');
```

## Views for Analytics

### 1. AI Performance Dashboard View
```sql
CREATE VIEW ai_performance_dashboard AS
SELECT 
    DATE_TRUNC('day', created_at) as analysis_date,
    COUNT(*) as daily_analyses,
    COUNT(DISTINCT product_id) as unique_products_analyzed,
    AVG(CASE 
        WHEN analysis_data->>'ai_confidence' IS NOT NULL 
        THEN (analysis_data->>'ai_confidence')::numeric 
        ELSE NULL 
    END) as avg_confidence
FROM ai_analysis_cache
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY analysis_date DESC;
```

### 2. User Engagement View
```sql
CREATE VIEW user_engagement_stats AS
SELECT 
    u.id as user_id,
    u.email,
    COUNT(p.id) as total_scans,
    COUNT(sp.id) as saved_products,
    COUNT(f.id) as feedback_count,
    MAX(p.scanned_at) as last_scan_date,
    AVG(p.safety_score) as avg_safety_preference
FROM auth.users u
LEFT JOIN products p ON u.id = p.user_id
LEFT JOIN saved_products sp ON u.id = sp.user_id
LEFT JOIN user_feedback f ON u.id = f.user_id
GROUP BY u.id, u.email;
```

## Setup Instructions

1. **Run the table creation scripts** in your Supabase SQL editor
2. **Set up RLS policies** for security
3. **Create the analytics functions** for enhanced insights
4. **Set up triggers** for automatic data maintenance
5. **Create views** for dashboard analytics
6. **Configure environment variables** in your Flutter app:
   ```
   GEMINI_API_KEY=your_gemini_api_key
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

This enhanced schema provides:
- ✅ AI result caching for performance
- ✅ User feedback collection for AI improvement
- ✅ Enhanced analytics and insights
- ✅ Product rating and review system
- ✅ Personalized recommendations
- ✅ Performance monitoring
- ✅ Data integrity and cleanup automation 