# Gemini AI Integration Setup Guide for SafeScan

## Overview

SafeScan uses two specialized Gemini AI models:

- 🚀 **Gemini 2.0 Flash** - Fast, versatile model for most operations
  - Correct Production Name: `gemini-2.0-flash`
  - Use Case: Fast, versatile performance across diverse tasks
  - Input Types: Audio, images, videos, and text
  - Output Type: Text
  - Strengths: Next generation features, speed, thinking capabilities, and realtime streaming

- 🧠 **Gemini 1.5 Pro** - Advanced model for complex reasoning
  - Correct Production Name: `gemini-1.5-pro`
  - Use Case: Complex reasoning tasks requiring more intelligence
  - Input Types: Audio, images, videos, and text
  - Output Type: Text
  - Strengths: Better for complex reasoning, deep analysis, and tasks requiring higher intelligence

This integration provides:

- 🧠 **AI-Powered Ingredient Analysis** - Intelligent safety assessment of food ingredients
- 🎯 **Personalized Recommendations** - Tailored advice based on user preferences and health conditions
- 🔍 **Comprehensive Safety Assessment** - Detailed product evaluation with scientific backing
- 📊 **Smart Caching** - Optimized performance with intelligent result caching
- 🚀 **Fallback Protection** - Intelligent model selection based on task complexity

## Prerequisites

1. **Google AI Studio Account** (free)
2. **Gemini API Key** 
3. **Flutter Development Environment**
4. **Supabase Project** (for caching and user data)

## Step 1: Get Gemini API Key

### 1.1 Create Google AI Studio Account
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Accept the terms of service

### 1.2 Generate API Key
1. Click on **"Get API key"** in the left sidebar
2. Click **"Create API key"**
3. Choose your Google Cloud project (or create a new one)
4. Copy the generated API key
5. **Important**: Keep this key secure and never commit it to version control

### 1.3 API Limits (Free Tier)
- **15 requests per minute**
- **1,500 requests per day**
- **1 million tokens per minute**
- **32,000 tokens per request**

*Perfect for development and moderate production usage*

## Step 2: Configure Environment Variables

### 2.1 Create .env File
```bash
# Copy the example file
cp env.example .env
```

### 2.2 Add Your Gemini API Key
```env
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# OpenFoodFacts Configuration
OPENFOODFACTS_USER_AGENT=SafeScan - AI-Powered Product Safety Scanner

# Environment
APP_ENV=development
```

### 2.3 Verify Environment Loading
Ensure your `.env` file is in the project root and listed in `pubspec.yaml`:

```yaml
flutter:
  assets:
    - assets/images/
    - assets/icons/
    - .env  # ✅ This line should be present
```

## Step 3: Set Up Enhanced Database Schema

### 3.1 Run SQL Scripts in Supabase
Execute these SQL commands in your Supabase SQL Editor:

```sql
-- 1. Create AI Analysis Cache Table
CREATE TABLE ai_analysis_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id TEXT NOT NULL,
    analysis_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes
CREATE INDEX idx_ai_analysis_cache_product_id ON ai_analysis_cache(product_id);
CREATE INDEX idx_ai_analysis_cache_created_at ON ai_analysis_cache(created_at);

-- 3. Enable RLS
ALTER TABLE ai_analysis_cache ENABLE ROW LEVEL SECURITY;

-- 4. Create policies
CREATE POLICY "Public read access for ai_analysis_cache" ON ai_analysis_cache
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert ai_analysis_cache" ON ai_analysis_cache
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

### 3.2 Enhance User Preferences Table
```sql
-- Add new columns for AI features
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS preferred_brands TEXT[];
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS safety_threshold INTEGER DEFAULT 70;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS ai_analysis_enabled BOOLEAN DEFAULT true;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS personalized_recommendations BOOLEAN DEFAULT true;
```

### 3.3 Create User Feedback Table
```sql
CREATE TABLE user_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id TEXT NOT NULL,
    feedback_type TEXT NOT NULL CHECK (feedback_type IN ('ai_accuracy', 'safety_rating', 'ingredient_accuracy', 'recommendation_quality')),
    feedback_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes and RLS
CREATE INDEX idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX idx_user_feedback_product_id ON user_feedback(product_id);
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own feedback" ON user_feedback
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own feedback" ON user_feedback
    FOR INSERT WITH CHECK (auth.uid() = user_id);
```

## Step 4: Test the Integration

### 4.1 Test Environment Setup
```dart
// Add this test to verify environment loading
void testEnvironmentSetup() {
  print('Testing environment setup...');
  
  final geminiKey = dotenv.env['GEMINI_API_KEY'];
  if (geminiKey == null || geminiKey.isEmpty) {
    print('❌ GEMINI_API_KEY not found in .env file');
    return;
  }
  
  if (geminiKey.length < 30) {
    print('❌ GEMINI_API_KEY appears to be invalid (too short)');
    return;
  }
  
  print('✅ Gemini API key loaded successfully');
  print('Key preview: ${geminiKey.substring(0, 10)}...');
}
```

### 4.2 Test AI Service
Create a test file `test/gemini_service_test.dart`:

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../lib/services/gemini_service.dart';

void main() async {
  // Load environment variables
  await dotenv.load(fileName: ".env");
  
  test('Gemini Service - Basic API Connectivity', () async {
    final result = await GeminiService.assessUnknownIngredient('citric acid');
    
    // Verify the response structure
    expect(result, isA<Map<String, dynamic>>());
    expect(result.containsKey('safety_level'), true);
    expect(result.containsKey('description'), true);
    expect(result.containsKey('health_risks'), true);
  });
}
```

### 4.3 Test Product Scanning
```dart
// Test scanning a real product
void testProductScanning() async {
  try {
    // Test with Coca-Cola barcode
    const testBarcode = '5449000000996';
    
    final product = await EnhancedProductAnalysisService.analyzeProductByBarcode(
      testBarcode,
      userId: 'test-user-id',
    );
    
    if (product != null) {
      print('✅ Product analysis successful');
      print('Product: ${product.name}');
      print('Safety Score: ${product.safetyScore}/100');
      print('AI Analysis: ${product.ingredientsJson?['ai_analyzed']}');
    } else {
      print('❌ Product analysis failed - no product returned');
    }
  } catch (e) {
    print('❌ Product analysis failed: $e');
  }
}
```

## Step 5: Model Usage Guidelines

### 5.1 When to Use Gemini 2.0 Flash
- For standard ingredient analysis
- For image processing and OCR
- For quick safety checks
- For all vision-related tasks
- For general-purpose text generation

### 5.2 When to Use Gemini 1.5 Pro
- For complex safety assessments
- For in-depth ingredient analysis
- For advanced research when web scraping fails
- For ingredient interaction analysis
- For generating comprehensive reports

### 5.3 Fallback Mechanism
SafeScan implements intelligent fallback between models:
- If Flash model is rate-limited, Pro model is used as fallback
- If Pro model is rate-limited, Flash model is used as fallback
- If both models are rate-limited, exponential backoff is applied before retry

## Step 6: Monitoring and Optimization

### 6.1 Tracking API Usage
Monitor your Gemini API usage in the Google Cloud Console to avoid exceeding quotas:
- https://console.cloud.google.com/apis/dashboard

### 6.2 Optimizing Costs
- Implement caching for common requests
- Batch similar operations
- Use appropriate model for each task type
- Ensure prompt engineering is efficient to minimize token usage

### 6.3 Improving Response Quality
- Use temperature parameter (0.2-0.4) for deterministic responses
- Include clear instructions in prompts
- Structure output format requirements explicitly
- Test prompts with various inputs to ensure robustness

## Step 7: Production Deployment

### 7.1 Security Checklist
- ✅ Never commit `.env` file to version control
- ✅ Use separate API keys for development/production
- ✅ Enable RLS policies in Supabase
- ✅ Monitor API usage and costs
- ✅ Set up error logging and monitoring

### 7.2 Scaling Considerations
For high-volume production usage:
- Consider upgrading to **Gemini Pro paid tier**
- Implement **request batching** for multiple ingredients
- Set up **Redis caching** for frequently accessed products
- Use **Cloud Functions** for heavy AI processing

### 7.3 Cost Management
Monitor and control costs:
- Track daily API usage in Google Cloud Console
- Set up billing alerts
- Implement usage analytics
- Cache aggressively for repeated requests

## Troubleshooting

### Common Issues

#### 1. API Key Not Working
```
Error: Gemini API error: 400 - API key not valid
```
**Solution**: 
- Verify API key is correct
- Check API key permissions in Google AI Studio
- Ensure API key is properly loaded in `.env`

#### 2. Rate Limit Exceeded
```
Error: Gemini API error: 429 - Resource has been exhausted
```
**Solution**:
- Wait for rate limit reset (1 minute)
- Implement exponential backoff
- Consider upgrading API plan

#### 3. Analysis Fails
```
Error: AI analysis failed: No ingredients found for AI analysis
```
**Solution**:
- Check product data from OpenFoodFacts
- Verify ingredient parsing logic
- Enable fallback to basic analysis

#### 4. Caching Issues
```
Error: Error caching AI analysis
```
**Solution**:
- Check Supabase connection
- Verify table permissions
- Check RLS policies

### Debug Mode
Enable debug logging:
```dart
// Add to main.dart
if (kDebugMode) {
  print('Gemini API Key configured: ${dotenv.env['GEMINI_API_KEY']?.isNotEmpty}');
}
```

## Best Practices

### 1. Prompt Engineering
- Use specific, clear prompts
- Provide context about user preferences
- Request structured JSON responses
- Include safety guidelines and examples

### 2. Error Handling
- Always implement fallback mechanisms
- Log errors for debugging
- Provide user-friendly error messages
- Cache successful results

### 3. User Experience
- Show loading states during AI analysis
- Provide immediate feedback
- Allow users to provide feedback
- Gracefully handle offline scenarios

### 4. Data Privacy
- Only send necessary ingredient data to AI
- Don't send personal user information
- Comply with data protection regulations
- Implement data retention policies

## Support and Resources

- [Gemini API Documentation](https://ai.google.dev/docs/gemini_api_overview)
- [Gemini 1.5 Pro Documentation](https://ai.google.dev/models/gemini)
- [Gemini 2.0 Flash Documentation](https://ai.google.dev/models/gemini)
- [Google AI Studio](https://aistudio.google.com/)
- **Supabase Documentation**: https://supabase.com/docs
- **SafeScan GitHub Issues**: For technical support

---

## Summary

With this setup, SafeScan now provides:

✅ **Intelligent Analysis** - AI-powered ingredient safety assessment
✅ **Personalized Insights** - Recommendations based on user preferences  
✅ **Comprehensive Safety Data** - Detailed health risk analysis
✅ **Performance Optimized** - Smart caching and fallback systems
✅ **User Feedback Loop** - Continuous improvement through user input
✅ **Production Ready** - Robust error handling and monitoring

The integration transforms SafeScan from a basic barcode scanner into an intelligent product safety advisor powered by Google's most advanced AI model. 