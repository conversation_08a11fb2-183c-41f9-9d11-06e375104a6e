# Gemini AI Integration in SafeScan

SafeScan leverages Google's latest Gemini AI models for comprehensive product safety analysis. This document outlines the AI integration architecture and explains how it enhances the application's capabilities.

## Model Architecture

### Primary Models

SafeScan uses two specific Gemini models for different tasks:

- **gemini-2.0-flash**: Fast, versatile model for general tasks
  - Input types: Audio, images, videos, and text
  - Output type: Text
  - Strengths: Next generation features, speed, thinking capabilities, and realtime streaming
  - Used for: Standard ingredient analysis, image processing, and general tasks

- **gemini-1.5-pro**: Specialized model for complex reasoning
  - Input types: Audio, images, videos, and text
  - Output type: Text
  - Strengths: Better for complex reasoning, deep analysis, and tasks requiring higher intelligence
  - Used for: Complex safety assessments, detailed ingredient research, and in-depth analyses

### Task-Specific Model Assignment

The application intelligently assigns tasks to the appropriate model:

1. **Tasks for gemini-2.0-flash**:
   - Image analysis (ingredient list extraction, nutrition facts)
   - Basic product information extraction
   - Standard ingredient safety checks
   - User interface responses

2. **Tasks for gemini-1.5-pro**:
   - Complex ingredient safety assessments
   - Comprehensive product safety analysis
   - Advanced research when web scraping fails
   - In-depth analysis of ingredient interactions

### Fallback System

The application implements a robust fallback system to handle API rate limits:

1. If gemini-2.0-flash is rate-limited, fall back to gemini-1.5-pro for standard tasks
2. If gemini-1.5-pro is rate-limited, fall back to gemini-2.0-flash for complex tasks
3. If both models are rate-limited, use exponential backoff to retry after a delay
4. Offline fallback responses as a last resort when all API calls fail

## AI Capabilities

### Product Analysis

- **Ingredient Safety Analysis**: Assesses individual ingredients for health risks
- **Interaction Detection**: Identifies potentially harmful interactions between ingredients
- **Safety Scoring**: Provides numerical safety scores and risk categorization

### Image Processing

- **OCR**: Extracts text from product packaging images
- **Ingredient List Extraction**: Identifies and parses ingredient lists from images
- **Nutrition Facts Parsing**: Extracts structured nutrition information

### Web Intelligence

- **Product Information Retrieval**: Finds and extracts product data when barcode lookup fails
- **Alternative Product Suggestions**: Recommends safer alternatives to problematic products
- **Data Enrichment**: Enhances product information with web-sourced data

## Implementation Details

### Services Architecture

The AI functionality is implemented across three main services:

1. **GeminiService**: Core text-based AI operations and safety analysis
   - Manages both gemini-2.0-flash and gemini-1.5-pro models
   - Intelligently routes tasks to the appropriate model
   - Handles fallback mechanisms and error recovery

2. **ImageAnalysisService**: Visual analysis capabilities
   - Uses gemini-2.0-flash for all vision tasks
   - Processes product packaging images to extract information

3. **WebScrapingService**: Web data extraction
   - Uses conventional web scraping as primary method
   - Falls back to GeminiService (using gemini-1.5-pro) for complex research tasks

### Performance Optimization

- **Parallel Processing**: Runs multiple AI operations concurrently
- **Response Caching**: Stores common AI responses to reduce API calls
- **Prompt Engineering**: Carefully crafted prompts for optimal AI responses
- **Context Windowing**: Optimizes token usage for large product datasets

## User Privacy

- No personal user data is sent to the Gemini AI API
- Product and ingredient analysis is anonymized
- All AI requests are made securely with encryption
- No user data persistence in AI systems

## Setup Requirements

To enable the AI features, you must configure the following in your `.env` file:

```
# Gemini API Configuration
GEMINI_API_KEY=your_api_key_here

# Google Custom Search (for web scraping)
GOOGLE_API_KEY=your_google_api_key_here
SEARCH_ENGINE_ID=your_search_engine_id_here
```

## Troubleshooting

If you encounter issues with the AI features:

1. **API Key Validation**: Ensure your Gemini API key is valid and has sufficient quota
2. **Network Connectivity**: Check your internet connection
3. **Rate Limiting**: If you receive "rate limit exceeded" errors, try again after a few minutes
4. **Model Availability**: Verify the Gemini API status if all AI features fail

For persistent issues, refer to the Google AI Platform status page or contact support. 