---
config:
  layout: fixed
---
flowchart TD
    A["User Taps Scan Product Button"] --> B["Request Camera Permission"]
    B --> B1{"Permission Granted?"}
    B1 -- No --> B2["Show Permission Dialog"]
    B2 --> B3["User Goes to Settings"]
    B1 -- Yes --> C["Open ML Kit Camera Screen"]
    C --> C1["Initialize Camera Controller"]
    C1 --> C2["Start ML Kit Image Stream"]
    C2 --> C3["Process Camera Frames"]
    C3 --> C4{"Barcode Detected?"}
    C4 -- No --> C3
    C4 -- Yes --> C5["Extract Barcode Value"]
    C5 --> C6["Stop Camera & Return to HomeScreen"]
    C6 --> D@{ label: "Show 'Checking database...' Loading Message" }
    D --> E["SafeScanService.analyzeProductByBarcode"]
    E --> E1["Start Database Check & Prepare API Call in Parallel"]
    E1 --> G["Check Database for Existing Product"] & G3["Prepare Open Food Facts API Connection"]
    G --> G1{"Product Found in DB?"}
    G1 -- Yes --> G2["Return Cached Product"]
    G1 -- No --> D2@{ label: "Show 'Retrieving product details...' Loading Message" }
    D2 --> H["Call Open Food Facts API"]
    G3 --> H
    H --> H1{"Open Food Facts Success?"}
    H1 -- Yes --> H2["Extract Product Name, Brand, Ingredients"]
    H1 -- No --> H3["Try Alternative Data Sources"]
    H3 --> D5@{ label: "Show 'Analyzing product image...' Loading Message" }
    D5 --> H4["Capture Product Image"]
    H4 --> H5["Send Image to Google Gemini API"]
    H5 --> H6{"Gemini Visual Analysis Success?"}
    H6 -- Yes --> H7["Extract Product Details from Gemini"]
    H6 -- No --> H8["Try Web Scraping Approach"]
    H7 --> H7A["Identify Quality Certifications"]
    H7A --> H7B["Detect USDA Organic, Non-GMO, Fair Trade, etc."]
    H7B --> H9["Validate & Parse Gemini Response"]
    H9 --> H10{"Product Info Complete?"}
    H10 -- Yes --> H2
    H10 -- No --> H8
    H8 --> D6@{ label: "Show 'Searching online databases...' Loading Message" }
    D6 --> H11["Search Product Online"]
    H11 --> H12["Scrape from Trusted Sources"]
    H12 --> H13{"Web Scraping Success?"}
    H13 -- Yes --> H14["Extract Nutrition & Ingredients"]
    H13 -- No --> H20["Request User Product Images"]
    H14 --> H16{"Data Quality Check Passed?"}
    H16 -- Yes --> H2
    H16 -- No --> H20
    H20 --> I["Analyze Ingredient Coverage"] & D3@{ label: "Show 'Analyzing ingredients...' Loading Message" }
    H2 --> D3
    D3 --> I
    G2 --> D4@{ label: "Show 'Analyzing ingredients...' Loading Message" }
    D4 --> P["Analysis Complete"]
    I --> I1["Check Ingredients Against Safety Database"]
    I1 --> I2{"Any Unsafe Ingredients Found?"}
    I2 -- Yes --> K1["Generate Not Recommend Result"]
    I2 -- No --> I3{"Coverage &gt;= 50%?"}
    I3 -- No --> K1
    I3 -- Yes --> K2["Generate Good Result"]
    K1 --> M["Create Final Product Object with not_recommend"]
    K2 --> M2["Create Final Product Object with good"]
    M --> N["Save Product to Database"]
    M2 --> N
    N --> N1{"Save to Main Table Success?"}
    N1 -- Yes --> N2["Database Save Complete"]
    N1 -- No --> N3["Try Fallback Table Save"]
    N3 --> N4{"Fallback Save Success?"}
    N4 -- Yes --> N2
    N4 -- No --> N5["Continue Without Database Save"]
    N5 --> N2
    N2 --> O["Update Ingredients Database"]
    O --> O1{"New Ingredients Found?"}
    O1 -- Yes --> O2["Add to Pending Ingredients"]
    O1 -- No --> P
    O2 --> P
    P --> Q["Return Product to UI"]
    Q --> R["HomeScreen Handles Result"]
    R --> R1{"Analysis Successful?"}
    R1 -- Yes --> T["Display Safety Results"]
    R1 -- No --> H20
    SE["SE"] --> U["User Sees Error"]
    T --> T1{"Recommendation is good?"}
    T1 -- Yes --> T2["Show Green GOOD Result"]
    T1 -- No --> T3["Show Red NOT RECOMMEND Result"]
    T2 --> T4["Navigate to Product Detail Screen"]
    T3 --> T4
    T4 --> T5["Display Recommendation & Concerning Ingredients"]
    T5 --> T6["Show Alternative Products"]
    U --> V["User Can Try Again"]
    V --> A
    C1 -- Camera Error --> ER1["Log Camera Error - Show H20"]
    C2 -- ML Kit Error --> ER2["Log ML Kit Error - Show H20"]
    E -- Network Error --> ER3["Log Network Error - Show H20"]
    I -- Analysis Error --> ER4["Log Analysis Error - Show H20"]
    H5 -- Gemini API Error --> ER5["Log Gemini API Error - Show H20"]
    H12 -- Scraping Error --> ER6["Log Web Scraping Error - Show H20"]
    ER1 --> H20
    ER2 --> H20
    ER3 --> H20
    ER4 --> H20
    ER5 --> H20
    ER6 --> H20
    D@{ shape: rect}
    D2@{ shape: rect}
    D5@{ shape: rect}
    D6@{ shape: rect}
    D3@{ shape: rect}
    D4@{ shape: rect}
     A:::userAction
     B1:::decision
     B3:::userAction
     C1:::systemProcess
     C2:::systemProcess
     C3:::systemProcess
     C4:::decision
     D:::loadingMessage
     E:::systemProcess
     E1:::systemProcess
     G:::database
     G3:::systemProcess
     G1:::decision
     D2:::loadingMessage
     H1:::decision
     D5:::loadingMessage
     H4:::systemProcess
     H5:::apiCall
     H6:::decision
     H7:::systemProcess
     H8:::webScraping
     H7A:::certification
     H7B:::certification
     H9:::systemProcess
     H9:::dataValidation
     H10:::decision
     D6:::loadingMessage
     H11:::systemProcess
     H11:::webScraping
     H12:::webScraping
     H13:::decision
     H14:::systemProcess
     H20:::userFeedback
     H16:::decision
     H16:::dataValidation
     I:::systemProcess
     D3:::loadingMessage
     D4:::loadingMessage
     I1:::systemProcess
     I2:::decision
     K1:::badResult
     I3:::decision
     K2:::goodResult
     M:::systemProcess
     M2:::systemProcess
     N:::database
     N1:::decision
     N2:::database
     N3:::database
     N4:::decision
     N5:::database
     O:::systemProcess
     O1:::decision
     O2:::database
     R1:::decision
     T:::success
     T1:::decision
     T2:::success
     T2:::goodResult
     T3:::badResult
     T4:::success
     T5:::success
     T6:::success
     V:::userAction
     ER1:::errorLog
     ER2:::errorLog
     ER3:::errorLog
     ER4:::errorLog
     ER5:::errorLog
     ER6:::errorLog
    classDef userAction fill:#e1f5fe
    classDef systemProcess fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef success fill:#e8f5e8
    classDef error fill:#ffebee
    classDef database fill:#e3f2fd
    classDef goodResult fill:#c8e6c9
    classDef apiCall fill:#fff9c4
    classDef dataValidation fill:#f1f8e9
    classDef webScraping fill:#e8eaf6
    classDef certification fill:#e8f5e8
    classDef userFeedback fill:#fce4ec
    classDef errorLog fill:#f3e5f5
    classDef loadingMessage fill:#e0f2f1
