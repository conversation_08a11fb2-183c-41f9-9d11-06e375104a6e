# SafeScan Setup Guide

## Prerequisites

1. **Node.js 18+** installed
2. **Expo CLI** installed globally: `npm install -g @expo/cli`
3. **Supabase account** with a project set up
4. **Cursor IDE** with MCP support

## Environment Setup

1. **Create your `.env` file** (copy from `.env.example` if available):
   ```bash
   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your_anon_key_here
   
   # OpenFoodFacts Configuration
   OPENFOODFACTS_USER_AGENT=SafeScan - AI-Powered Product Safety Scanner
   OPENFOODFACTS_API_URL=https://world.openfoodfacts.org/api/v0/product
   
   # API Keys
   GEMINI_API_KEY=your_gemini_api_key_here
   GOOGLE_API_KEY=your_google_api_key_here
   SEARCH_ENGINE_ID=your_search_engine_id_here
   
   # App Environment
   ENVIRONMENT=development
   ```

2. **Configure MCP Supabase Integration** (in Cursor):
   - Update `.cursor/mcp.json` with your actual Supabase credentials:
   ```json
   {
     "mcpServers": {
       "supabase": {
         "command": "npx",
         "args": ["-y", "@supabase/mcp-server"],
         "env": {
           "SUPABASE_URL": "https://your-project.supabase.co",
           "SUPABASE_ANON_KEY": "your_anon_key_here"
         }
       }
     }
   }
   ```

## Database Setup

Your Supabase database should have the following tables:
- `products` - Product information
- `ingredients` - Ingredient database
- `product_ingredients` - Many-to-many relationship
- `user_preferences` - User settings
- `saved_products` - User's saved products
- `user_scans` - Scan history

Refer to `Supabase-Structure-detailes.md` for the complete schema.

## Running the App

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npx expo start
   ```

3. **Run on iOS**:
   ```bash
   npx expo start --ios
   ```

4. **Run on Android**:
   ```bash
   npx expo start --android
   ```

## Troubleshooting

### Common Issues

1. **"ConfigError: The expected package.json path does not exist"**
   - Make sure you're in the correct directory
   - Ensure `package.json` exists in the root

2. **Supabase connection errors**
   - Verify your `.env` file has correct credentials
   - Check that your Supabase project is active
   - Ensure RLS policies are properly configured

3. **Camera permissions**
   - On iOS: The app will request permissions automatically
   - On Android: Ensure camera permissions are granted

4. **MCP Server not working**
   - Verify `.cursor/mcp.json` configuration
   - Check that Cursor has MCP support enabled
   - Ensure Supabase credentials are correct

### Development Tips

1. **Use Expo Go app** for quick testing on physical devices
2. **Enable hot reload** for faster development
3. **Check Expo DevTools** for debugging information
4. **Use Flipper** for advanced debugging (optional)

## Next Steps

1. Configure your actual Supabase credentials
2. Set up your product database
3. Test barcode scanning functionality
4. Customize the app for your specific needs

For more information, see the main `README.md` file. 