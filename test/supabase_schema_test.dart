import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  setUpAll(() async {
    // Load environment variables
    await dotenv.load(fileName: ".env");
    
    // Initialize Supabase
    await Supabase.initialize(
      url: dotenv.env['SUPABASE_URL'] ?? 'https://lwnfzmvqahraputvfkos.supabase.co',
      anonKey: dotenv.env['SUPABASE_ANON_KEY'] ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao',
    );
    
    print('Supabase initialized for testing');
  });

  test('Connect to Supabase and list available tables', () async {
    // Get Supabase client
    final client = Supabase.instance.client;
    
    print('Connected to Supabase at: ${dotenv.env['SUPABASE_URL']}');
    
    try {
      // List tables by querying information_schema
      final response = await client
          .rpc('get_schema_info')
          .select();
      
      print('Database tables:');
      for (var table in response) {
        print('- ${table['table_name']} (${table['schema_name']}.${table['table_name']})');
      }
    } catch (e) {
      // If the RPC method doesn't exist, try a different approach
      print('Error getting schema info: $e');
      print('Trying to query specific tables...');
      
      // List of tables to check based on the app code
      final tablesToCheck = [
        'products',
        'ingredients',
        'users',
        'scan_events',
        'user_preferences',
        'product_ratings',
        'saved_products',
        'allergens',
        'pending_ingredients',
        'product_analysis_fallback',
      ];
      
      for (var table in tablesToCheck) {
        try {
          // Check if table exists by trying to get a single row
          final response = await client
              .from(table)
              .select('*')
              .limit(1);
          
          print('✅ Table exists: $table');
          
          // If the table exists, try to get a list of its columns
          if (table == 'products') {
            try {
              // For products table, try to get column names directly
              final columnSample = response.isNotEmpty ? response[0] : null;
              if (columnSample != null) {
                print('  Sample column names: ${columnSample.keys.join(', ')}');
              } else {
                print('  No data in table to determine columns');
              }
            } catch (e) {
              print('  Could not get column info: $e');
            }
          }
        } catch (e) {
          print('❌ Table does not exist or error: $table');
          print('  Error: $e');
        }
      }
    }
  });
} 