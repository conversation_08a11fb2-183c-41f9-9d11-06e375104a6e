import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  setUpAll(() async {
    // Load the environment variables before running tests
    try {
      await dotenv.load(fileName: ".env");
      print("Environment variables loaded from .env");
    } catch (e) {
      print("Error loading .env file: $e");
      // Test will continue with default values
    }
  });

  test('OpenFoodFacts environment variables are present', () {
    final apiUrl = dotenv.env['OPENFOODFACTS_API_URL'];
    final userAgent = dotenv.env['OPENFOODFACTS_USER_AGENT'];
    
    print('OPENFOODFACTS_API_URL: $apiUrl');
    print('OPENFOODFACTS_USER_AGENT: $userAgent');
    
    expect(apiUrl, isNotNull);
    expect(userAgent, isNotNull);
    
    expect(apiUrl, contains('openfoodfacts.org'));
  });
  
  test('Gemini API Key is present', () {
    final apiKey = dotenv.env['GEMINI_API_KEY'];
    
    print('GEMINI_API_KEY first 5 chars: ${apiKey?.substring(0, 5)}...');
    
    expect(apiKey, isNotNull);
    expect(apiKey, isNotEmpty);
  });
} 