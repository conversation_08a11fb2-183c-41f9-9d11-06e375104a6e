// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:safescan_flutter/main.dart';
import 'package:safescan_flutter/widgets/mlkit_scan_screen.dart';
import 'package:flutter/material.dart';
import 'package:safescan_flutter/screens/auth/login_screen.dart';
import 'package:safescan_flutter/screens/auth/register_screen.dart';

void main() {
  testWidgets('SafeScan app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: SafeScanApp()));

    // Verify that the splash screen loads
    expect(find.text('SafeScan'), findsOneWidget);
    expect(find.text('Your Personal Product Safety Guardian'), findsOneWidget);
  });

  // Skipped: MLKitScanScreen widget tests require camera/permission mocking or integration test environment.
  // testWidgets('MLKitScanScreen shows error on camera permission denied', (WidgetTester tester) async {
  //   await tester.pumpWidget(const MaterialApp(home: MLKitScanScreen()));
  //   await tester.pumpAndSettle();
  //   expect(find.textContaining('Camera permission denied'), findsOneWidget);
  // });
  //
  // testWidgets('MLKitScanScreen shows scan area overlay when initialized', (WidgetTester tester) async {
  //   await tester.pumpWidget(const MaterialApp(home: MLKitScanScreen()));
  //   expect(find.text('Align the barcode within the square'), findsOneWidget);
  // });

  testWidgets('LoginScreen UI loads and validates required fields', (WidgetTester tester) async {
    // Use a larger test screen size to avoid overflow
    await tester.pumpWidget(const ProviderScope(child: MaterialApp(
      home: MediaQuery(
        data: MediaQueryData(size: Size(800, 1600)),
        child: LoginScreen(),
      ),
    )));
    // Check for SafeScan title and email/password fields
    expect(find.text('SafeScan'), findsWidgets); // allow multiple
    expect(find.byType(TextFormField), findsNWidgets(2));
    // Try to submit with empty fields
    // Find the Login button by Key
    final loginButton = find.byKey(const Key('login_button'));
    expect(loginButton, findsOneWidget);
    await tester.tap(loginButton);
    await tester.pump();
    expect(find.text('Please enter your email'), findsOneWidget);
    expect(find.text('Please enter your password'), findsOneWidget);
  });

  testWidgets('RegisterScreen UI loads and validates required fields', (WidgetTester tester) async {
    await tester.pumpWidget(const ProviderScope(child: MaterialApp(
      home: MediaQuery(
        data: MediaQueryData(size: Size(800, 1600)),
        child: RegisterScreen(),
      ),
    )));
    // Check for Create Account title and fields
    expect(find.text('Create Account'), findsWidgets); // allow multiple
    expect(find.byType(TextFormField), findsNWidgets(4));
    // Try to submit with empty fields
    // Find the Register button by Key
    final registerButton = find.byKey(const Key('register_button'));
    expect(registerButton, findsOneWidget);
    await tester.tap(registerButton);
    await tester.pump();
    expect(find.text('Please enter your full name'), findsOneWidget);
    expect(find.text('Please enter your email'), findsOneWidget);
    expect(find.text('Please enter your password'), findsOneWidget);
    expect(find.text('Please confirm your password'), findsOneWidget);
  });
}
