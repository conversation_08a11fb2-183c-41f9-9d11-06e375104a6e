import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import 'package:gotrue/gotrue.dart' show User, Session, AuthResponse, GoTrueClient;
import 'package:supabase/supabase.dart';

import '../lib/main.dart';
import '../lib/providers/supabase_provider.dart';
import '../lib/services/supabase_service.dart';
import '../lib/models/user.dart' as app_user;
import '../lib/widgets/safescan_header.dart';
import '../lib/providers/product_provider.dart';
import '../lib/providers/auth_provider.dart';
import '../lib/models/product.dart';
import '../lib/screens/home_screen.dart';

// Define a mock session and user
final _mockSession = Session(
  accessToken: 'fake-token',
  tokenType: 'bearer',
  user: User(
    id: 'test-id',
    email: '<EMAIL>',
    userMetadata: {},
    appMetadata: {},
    aud: 'authenticated',
    createdAt: DateTime.now().toIso8601String(),
  ),
  refreshToken: 'fake-refresh',
  expiresIn: 3600,
  providerToken: null,
  providerRefreshToken: null,
);

// More comprehensive mock of GoTrueClient
class MockGoTrueClient extends GoTrueClient {
  final Session _session;
  
  MockGoTrueClient(this._session) : super(
    url: 'https://test.supabase.co',
    // Disable auto refresh to prevent timers
    autoRefreshToken: false,
  );

  @override
  Session? get currentSession => _session;

  @override
  User? get currentUser => _session.user;

  @override
  Future<AuthResponse> signInWithPassword({
    String? email,
    String? phone,
    required String password,
    String? captchaToken,
  }) async {
    return AuthResponse(session: _session, user: _session.user);
  }

  @override
  Future<void> signOut({SignOutScope scope = SignOutScope.global}) async {
    // Mock sign out - do nothing
  }

  @override
  void startAutoRefresh() {
    // Do nothing - prevent timer creation
  }
  
  @override
  void stopAutoRefresh() {
    // Do nothing - no timer to stop
  }

  @override
  Stream<AuthState> get onAuthStateChange => Stream.value(
    AuthState(AuthChangeEvent.signedIn, _session)
  );
}

// Subclass SupabaseClient for testing
class TestSupabaseClient extends SupabaseClient {
  final GoTrueClient _mockAuth;
  
  TestSupabaseClient(this._mockAuth)
      : super('https://test.supabase.co', 'test-anon-key');

  @override
  GoTrueClient get auth => _mockAuth;
}

// Dummy user for currentUserProvider
final _dummyUser = app_user.User(
  id: 'test-user',
  email: '<EMAIL>',
  name: 'Test User',
  createdAt: DateTime.now(),
);

// Mock controllers that don't perform any real operations
class DummyProductController extends ProductController {
  DummyProductController(Ref ref);
  
  @override
  Future<void> scanProduct(String barcode) async {
    // Mock implementation - do nothing
  }
}

class DummySavedProductsController extends SavedProductsController {
  DummySavedProductsController(Ref ref);
  
  @override
  Future<void> saveProduct(String productId) async {
    // Mock implementation - do nothing
  }
  
  @override
  Future<void> removeProduct(String productId) async {
    // Mock implementation - do nothing
  }
}

// Test-specific SupabaseService
class TestSupabaseService extends SupabaseService {
  final SupabaseClient _mockClient;
  
  TestSupabaseService(this._mockClient);
  
  @override
  Future<void> dispose() async {
    // Override to prevent any cleanup that might cause timer issues
  }
}

void main() {
  late TestSupabaseClient testClient;
  late MockGoTrueClient mockAuth;
  
  setUp(() {
    // Create fresh mocks for each test
    mockAuth = MockGoTrueClient(_mockSession);
    testClient = TestSupabaseClient(mockAuth);
  });

  testWidgets('SafeScanApp launches and navigates to HomeScreen', (WidgetTester tester) async {
    // Set a proper test window size
    tester.view.physicalSize = const Size(800, 1600);
    tester.view.devicePixelRatio = 1.0;
    
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          supabaseServiceProvider.overrideWithValue(TestSupabaseService(testClient)),
          savedProductsProvider.overrideWith((ref) async => <Product>[]),
          currentUserProvider.overrideWithValue(_dummyUser),
          productControllerProvider.overrideWith((ref) => DummyProductController(ref)),
          savedProductsControllerProvider.overrideWith((ref) => DummySavedProductsController(ref)),
        ],
        child: const SafeScanApp(),
      ),
    );
    
    // Allow splash screen to complete and navigation to occur
    await tester.pumpAndSettle(const Duration(seconds: 3));
    
    // Debug: Print widget tree to understand what's actually rendered
    debugPrint('Widget tree:');
    debugPrint(tester.allWidgets.map((w) => w.runtimeType.toString()).join(', '));
    
    // Look for the SafeScanHeader with pageTitle: 'Home'
    expect(find.widgetWithText(SafeScanHeader, 'Home'), findsOneWidget);
  });

  testWidgets('HomeScreen renders SafeScanHeader with Home', (WidgetTester tester) async {
    // Set a proper test window size
    tester.view.physicalSize = const Size(800, 1600);
    tester.view.devicePixelRatio = 1.0;
    
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          supabaseServiceProvider.overrideWithValue(TestSupabaseService(testClient)),
          savedProductsProvider.overrideWith((ref) async => <Product>[]),
          currentUserProvider.overrideWithValue(_dummyUser),
          productControllerProvider.overrideWith((ref) => DummyProductController(ref)),
          savedProductsControllerProvider.overrideWith((ref) => DummySavedProductsController(ref)),
        ],
        child: const MaterialApp(
          home: HomeScreen(),
        ),
      ),
    );
    
    // Allow all async operations to complete
    await tester.pumpAndSettle(const Duration(seconds: 2));
    
    // Debug: Check if HomeScreen is actually rendered
    expect(find.byType(HomeScreen), findsOneWidget);
    
    // Look for SafeScanHeader
    expect(find.byType(SafeScanHeader), findsOneWidget);
    expect(find.widgetWithText(SafeScanHeader, 'Home'), findsOneWidget);
  });

  testWidgets('SafeScanApp builds without exceptions', (WidgetTester tester) async {
    // Set a larger test window size to prevent overflow
    tester.view.physicalSize = const Size(800, 1600);
    tester.view.devicePixelRatio = 1.0;

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          supabaseServiceProvider.overrideWithValue(TestSupabaseService(testClient)),
          savedProductsProvider.overrideWith((ref) async => <Product>[]),
          currentUserProvider.overrideWithValue(_dummyUser),
          productControllerProvider.overrideWith((ref) => DummyProductController(ref)),
          savedProductsControllerProvider.overrideWith((ref) => DummySavedProductsController(ref)),
        ],
        child: const SafeScanApp(),
      ),
    );
    
    // Allow the app to fully initialize
    await tester.pumpAndSettle(const Duration(seconds: 3));
    
    // Verify no exceptions were thrown and the app tree exists
    expect(find.byType(SafeScanApp), findsOneWidget);
  });
  
  // Alternative test that checks for specific text if SafeScanHeader lookup fails
  testWidgets('HomeScreen contains expected Home text', (WidgetTester tester) async {
    tester.view.physicalSize = const Size(800, 1600);
    tester.view.devicePixelRatio = 1.0;
    
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          supabaseServiceProvider.overrideWithValue(TestSupabaseService(testClient)),
          savedProductsProvider.overrideWith((ref) async => <Product>[]),
          currentUserProvider.overrideWithValue(_dummyUser),
          productControllerProvider.overrideWith((ref) => DummyProductController(ref)),
          savedProductsControllerProvider.overrideWith((ref) => DummySavedProductsController(ref)),
        ],
        child: const MaterialApp(
          home: HomeScreen(),
        ),
      ),
    );
    
    await tester.pumpAndSettle(const Duration(seconds: 2));
    
    // Look for the text "Home" anywhere in the widget tree
    expect(find.text('Home'), findsAtLeastNWidgets(1));
  });
} 