import 'package:flutter_test/flutter_test.dart';
import 'package:supabase/supabase.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  group('Database Fix Verification Tests', () {
    late SupabaseClient supabaseClient;
    
    setUpAll(() async {
      // Load environment variables
      try {
        await dotenv.load(fileName: ".env");
        print("✅ Environment variables loaded successfully");
      } catch (e) {
        print("⚠️ .env file not found, using fallback values: $e");
      }
      
      // Get Supabase credentials
      final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 
          'https://lwnfzmvqahraputvfkos.supabase.co';
      final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUwNjI0MDcsImV4cCI6MjA1MDYzODQwN30.L5s-t2OMVLnGe-wj5awHh7d7dBVJn_eZHyZNZklSTdE';
      
      supabaseClient = SupabaseClient(supabaseUrl, supabaseKey);
      print("✅ Supabase client initialized");
    });

    test('Database connectivity works', () async {
      try {
        // Test basic connectivity by checking ingredients table
        final response = await supabaseClient
            .from('ingredients')
            .select('count')
            .limit(1);
        
        expect(response, isNotNull);
        print("✅ Database connectivity verified");
      } catch (e) {
        fail("❌ Database connectivity failed: $e");
      }
    });

    test('RLS policies allow inserts on products table', () async {
      try {
        // Try to insert a test product
        final testData = {
          'barcode': 'test-${DateTime.now().millisecondsSinceEpoch}',
          'safety_score': 85,
          'created_at': DateTime.now().toIso8601String(),
        };
        
        final response = await supabaseClient
            .from('products')
            .insert(testData)
            .select()
            .single();
        
        expect(response, isNotNull);
        expect(response['barcode'], equals(testData['barcode']));
        expect(response['safety_score'], equals(testData['safety_score']));
        
        print("✅ Products table insert successful");
        
        // Clean up - delete the test record
        await supabaseClient
            .from('products')
            .delete()
            .eq('barcode', testData['barcode']);
            
      } catch (e) {
        if (e.toString().contains('row-level security policy')) {
          fail("❌ RLS policy still blocking inserts on products table. Please run: ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;");
        } else {
          fail("❌ Products table insert failed: $e");
        }
      }
    });

    test('RLS policies allow inserts on product_analysis_fallback table', () async {
      try {
        // Try to insert into fallback table
        final testData = {
          'barcode': 'fallback-test-${DateTime.now().millisecondsSinceEpoch}',
          'safety_score': 75,
          'created_at': DateTime.now().toIso8601String(),
        };
        
        final response = await supabaseClient
            .from('product_analysis_fallback')
            .insert(testData)
            .select()
            .single();
        
        expect(response, isNotNull);
        expect(response['barcode'], equals(testData['barcode']));
        
        print("✅ Product analysis fallback table insert successful");
        
        // Clean up
        await supabaseClient
            .from('product_analysis_fallback')
            .delete()
            .eq('barcode', testData['barcode']);
            
      } catch (e) {
        if (e.toString().contains('row-level security policy')) {
          fail("❌ RLS policy still blocking inserts on product_analysis_fallback table. Please run: ALTER TABLE public.product_analysis_fallback DISABLE ROW LEVEL SECURITY;");
        } else {
          fail("❌ Product analysis fallback table insert failed: $e");
        }
      }
    });

    test('Can read ingredients data for analysis', () async {
      try {
        final response = await supabaseClient
            .from('ingredients')
            .select('name, safety_level')
            .limit(5);
        
        expect(response, isA<List>());
        expect(response.length, greaterThan(0));
        
        print("✅ Ingredients data accessible (${response.length} ingredients found)");
        
        for (final ingredient in response) {
          expect(ingredient['name'], isNotNull);
          print("  - ${ingredient['name']}: ${ingredient['safety_level']}");
        }
        
      } catch (e) {
        fail("❌ Failed to read ingredients data: $e");
      }
    });

    test('Type casting works correctly with null values', () async {
      try {
        // Test with minimal data that might have null values
        final testData = {
          'barcode': 'null-test-${DateTime.now().millisecondsSinceEpoch}',
          'safety_score': 50,
          'created_at': DateTime.now().toIso8601String(),
          // Intentionally not including name, brand, etc. to test null handling
        };
        
        final response = await supabaseClient
            .from('products')
            .insert(testData)
            .select()
            .single();
        
        expect(response, isNotNull);
        expect(response['barcode'], equals(testData['barcode']));
        expect(response['safety_score'], equals(testData['safety_score']));
        
        // These might be null and should not cause type casting errors
        final name = response['name']; // Could be null
        final brand = response['brand']; // Could be null
        final imageUrl = response['image']; // Could be null
        
        print("✅ Null value handling works correctly");
        print("  - name: $name");
        print("  - brand: $brand");
        print("  - image: $imageUrl");
        
        // Clean up
        await supabaseClient
            .from('products')
            .delete()
            .eq('barcode', testData['barcode']);
            
      } catch (e) {
        fail("❌ Type casting with null values failed: $e");
      }
    });

    test('Database schema matches expectations', () async {
      try {
        // Check if expected columns exist in products table
        final response = await supabaseClient
            .from('products')
            .select('*')
            .limit(1);
        
        if (response.isNotEmpty) {
          final product = response.first;
          final keys = product.keys.toList();
          
          expect(keys, contains('barcode'));
          expect(keys, contains('safety_score'));
          expect(keys, contains('created_at'));
          
          print("✅ Database schema verification passed");
          print("  Available columns: ${keys.join(', ')}");
        } else {
          print("✅ Database schema accessible (no existing products to check)");
        }
        
      } catch (e) {
        fail("❌ Database schema verification failed: $e");
      }
    });
  });
} 