import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('OpenFoodFacts API test', () async {
    final barcode = '3017620422003'; // Example barcode (Nutella)
    final apiUrl = 'https://world.openfoodfacts.org/api/v0/product/$barcode.json';
    final userAgent = 'SafeScan - Product Safety App';
    
    print('Testing OpenFoodFacts API');
    print('URL: $apiUrl');
    print('User-Agent: $userAgent');
    
    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'User-Agent': userAgent,
        },
      ).timeout(const Duration(seconds: 10));
      
      print('Response status code: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('API response data status: ${data['status']}');
        
        if (data['status'] == 1) {
          print('Successfully found product data in OpenFoodFacts');
          print('Product name: ${data['product']['product_name']}');
        } else {
          print('Product not found in OpenFoodFacts (status = ${data['status']})');
        }
      } else {
        print('OpenFoodFacts API returned error: ${response.body}');
      }
      
      expect(response.statusCode, 200);
    } catch (e) {
      print('Error fetching product from OpenFoodFacts: $e');
      fail('API request failed: $e');
    }
  });
} 