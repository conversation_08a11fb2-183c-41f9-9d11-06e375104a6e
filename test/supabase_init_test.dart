import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:safescan_flutter/services/supabase_init_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  setUpAll(() async {
    // Load environment variables for testing
    try {
      await dotenv.load(fileName: ".env");
    } catch (e) {
      // Use default values in tests
    }
  });
  
  group('SupabaseInitService Tests', () {
    test('Initialize Supabase connection', () async {
      // Initialize Supabase
      final result = await SupabaseInitService.initialize();
      
      // Verify initialization was successful
      expect(result, isTrue);
    });
    
    test('Check required tables', () async {
      // Initialize if not already initialized
      await SupabaseInitService.initialize();
      
      // Check if required tables exist
      final tableStatus = await SupabaseInitService.checkTables([
        'products',
        'ingredients',
        'product_ingredients',
        'saved_products',
        'user_preferences'
      ]);
      
      // Verify all required tables exist
      tableStatus.forEach((table, exists) {
        expect(exists, isTrue, reason: 'Table $table should exist');
      });
    });
    
    test('Verify connection', () async {
      // Initialize if not already initialized
      await SupabaseInitService.initialize();
      
      // Verify connection
      final connectionStatus = await SupabaseInitService.verifyConnection();
      
      // Connection should be successful
      expect(connectionStatus, isTrue);
    });
  });
} 