import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase/supabase.dart';
import 'package:safescan_flutter/constants/mcp_notifier.dart';
import 'package:safescan_flutter/router.dart';
import 'package:safescan_flutter/constants/app_theme.dart';
import 'package:safescan_flutter/providers/product_provider.dart';
import 'package:safescan_flutter/providers/supabase_provider.dart';
import 'package:safescan_flutter/providers/auth_provider.dart';
import 'package:safescan_flutter/services/supabase_service.dart';
import 'package:safescan_flutter/models/user.dart' as app_user;

// Mock Supabase client
class MockSupabaseClient extends SupabaseClient {
  MockSupabaseClient() : super('http://localhost:3000', 'test-key');
  
  @override
  SupabaseQueryBuilder from(String table) {
    return MockSupabaseQueryBuilder();
  }
}

class MockSupabaseQueryBuilder extends SupabaseQueryBuilder {
  MockSupabaseQueryBuilder() : super('http://localhost:3000', {}, {});
  
  @override
  SupabaseFilterBuilder select([String columns = '*']) {
    return MockSupabaseFilterBuilder();
  }
}

class MockSupabaseFilterBuilder extends SupabaseFilterBuilder {
  MockSupabaseFilterBuilder() : super('http://localhost:3000', {}, {});
  
  @override
  Future<List<Map<String, dynamic>>> call() async {
    return [];
  }
}

// Mock Supabase service
class MockSupabaseService extends SupabaseService {
  MockSupabaseService(super.client);
  
  @override
  Future<app_user.User?> getCurrentUser() async {
    return null;
  }
}

// Mock Product controller
class MockProductController extends ProductController {
  MockProductController(ref) : super(ref);
  
  @override
  Future<void> scanProductByBarcode(String barcode) async {
    // Mock implementation
  }
}

void main() {
  testWidgets('ProviderScope + ValueListenableBuilder + real appRoutes + banner', (WidgetTester tester) async {
    // Set a larger test window size to prevent overflow
    const testWidth = 800.0;
    const testHeight = 1600.0;
    tester.view.physicalSize = const Size(testWidth, testHeight);
    tester.view.devicePixelRatio = 1.0;

    final testRouter = GoRouter(
      initialLocation: '/home',
      routes: appRoutes,
    );
    
    final mockSupabaseClient = MockSupabaseClient();
    
    await tester.pumpWidget(
      MaterialApp(
        home: ProviderScope(
          overrides: [
            // Mock the Supabase client
            supabaseClientProvider.overrideWithValue(mockSupabaseClient),
            // Mock the Supabase service
            supabaseServiceProvider.overrideWith((ref) => MockSupabaseService(mockSupabaseClient)),
            // Mock the auth provider
            currentUserProvider.overrideWith((ref) => null),
            // Mock the product controller
            productControllerProvider.overrideWith((ref) => MockProductController(ref)),
          ],
          child: ValueListenableBuilder<bool>(
            valueListenable: mcpTablesAvailableNotifier,
            builder: (context, value, _) {
              return Column(
                children: [
                  if (!value)
                    Container(
                      width: double.infinity,
                      color: Theme.of(context).colorScheme.secondary,
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                      child: Text(
                        "Enhanced analysis features unavailable. Run migration script to enable.",
                        style: TextStyle(color: Theme.of(context).colorScheme.onSecondary),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  Expanded(
                    child: MaterialApp.router(
                      theme: AppTheme.lightTheme,
                      darkTheme: AppTheme.darkTheme,
                      routerConfig: testRouter,
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );

    await tester.pumpAndSettle();
    
    // Verify that the app loaded without crashing
    expect(find.byType(MaterialApp), findsOneWidget);
  });

  testWidgets('ProviderScope + ValueListenableBuilder + real appRoutes + banner + provider override', (WidgetTester tester) async {
    // Similar test with even more explicit overrides
    tester.view.physicalSize = const Size(800, 1600);
    tester.view.devicePixelRatio = 1.0;

    final testRouter = GoRouter(
      initialLocation: '/splash', // Start with splash to avoid immediate HomeScreen load
      routes: appRoutes,
    );
    
    final mockSupabaseClient = MockSupabaseClient();

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          supabaseServiceProvider.overrideWith((ref) => MockSupabaseService(mockSupabaseClient)),
          currentUserProvider.overrideWith((ref) => null),
          productControllerProvider.overrideWith((ref) => MockProductController(ref)),
        ],
        child: ValueListenableBuilder<bool>(
          valueListenable: mcpTablesAvailableNotifier,
          builder: (context, value, _) {
            return Column(
              children: [
                Container(
                  width: double.infinity,
                  color: Colors.orange,
                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  child: const Text(
                    "Test banner",
                    style: TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: MaterialApp.router(
                    theme: AppTheme.lightTheme,
                    darkTheme: AppTheme.darkTheme,
                    routerConfig: testRouter,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );

    await tester.pumpAndSettle();
    
    // Verify that the test banner is displayed
    expect(find.text("Test banner"), findsOneWidget);
    expect(find.byType(MaterialApp), findsOneWidget);
  });
} 