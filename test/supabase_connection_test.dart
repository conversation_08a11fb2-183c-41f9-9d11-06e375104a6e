import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/widgets.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  group('Supabase Connection Tests', () {
    late SupabaseClient supabaseClient;
    
    setUpAll(() async {
      // Ensure Flutter bindings are initialized
      WidgetsFlutterBinding.ensureInitialized();
      
      // Load environment variables
      try {
        await dotenv.load(fileName: ".env");
        print("✅ Environment variables loaded successfully");
      } catch (e) {
        print("⚠️ .env file not found, using fallback values: $e");
      }
      
      // Get Supabase credentials
      final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 
          'https://lwnfzmvqahraputvfkos.supabase.co';
      final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao';
      
      // Initialize Supabase
      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseKey,
      );
      
      supabaseClient = Supabase.instance.client;
      print("✅ Supabase initialized at: $supabaseUrl");
    });
    
    tearDownAll(() async {
      // Clean up if needed
      await Supabase.instance.dispose();
    });

    test('should connect to Supabase successfully', () async {
      expect(supabaseClient, isNotNull);
      expect(Supabase.instance.client, equals(supabaseClient));
    });

    test('should verify required tables exist', () async {
      final requiredTables = [
        'products',
        'ingredients', 
        'product_ingredients',
        'saved_products',
        'user_preferences',
        'allergens',
        'dietary_preferences',
        'avoided_ingredients'
      ];

      final tableResults = <String, bool>{};
      
      for (final tableName in requiredTables) {
        try {
          // Test table existence by querying with limit 0
          await supabaseClient
              .from(tableName)
              .select('*')
              .limit(0);
          tableResults[tableName] = true;
          print("✅ Table '$tableName' exists and is accessible");
        } catch (e) {
          tableResults[tableName] = false;
          print("❌ Table '$tableName' error: ${e.toString().split('\n')[0]}");
        }
      }

      // Verify critical tables exist
      expect(tableResults['products'], isTrue, 
          reason: 'Products table should exist');
      expect(tableResults['ingredients'], isTrue, 
          reason: 'Ingredients table should exist');
      expect(tableResults['user_preferences'], isTrue, 
          reason: 'User preferences table should exist');
      
      print("📊 Table verification completed");
    });

    test('should query ingredients table successfully', () async {
      try {
        final ingredients = await supabaseClient
            .from('ingredients')
            .select('*')
            .limit(5);
        
        expect(ingredients, isA<List>());
        
        if (ingredients.isNotEmpty) {
          print("✅ Sample ingredient found: ${ingredients.first}");
          
          // Verify ingredient structure
          final firstIngredient = ingredients.first;
          expect(firstIngredient, isA<Map<String, dynamic>>());
          
          // Check for expected fields (adjust based on your schema)
          final expectedFields = ['id', 'name'];
          for (final field in expectedFields) {
            if (firstIngredient.containsKey(field)) {
              print("✅ Ingredient has '$field' field");
            }
          }
        } else {
          print("⚠️ No ingredients found in database");
        }
      } catch (e) {
        fail("Failed to query ingredients table: $e");
      }
    });

    test('should query products table successfully', () async {
      try {
        final products = await supabaseClient
            .from('products')
            .select('*')
            .limit(3);
        
        expect(products, isA<List>());
        print("✅ Products query successful - found ${products.length} products");
        
        if (products.isNotEmpty) {
          final firstProduct = products.first;
          expect(firstProduct, isA<Map<String, dynamic>>());
          print("📦 Sample product: ${firstProduct['name'] ?? 'Unnamed product'}");
        }
      } catch (e) {
        print("⚠️ Products table query failed (may be empty): $e");
        // Don't fail the test if products table is empty
      }
    });

    test('should handle user preferences table', () async {
      try {
        final userPrefs = await supabaseClient
            .from('user_preferences')
            .select('id')
            .limit(10);
        
        expect(userPrefs, isA<List>());
        print("✅ User preferences query successful - found ${userPrefs.length} records");
      } catch (e) {
        print("⚠️ User preferences query failed: $e");
        // This might be expected if no users exist yet
      }
    });

    test('should test database write permissions (if applicable)', () async {
      // This test checks if we can perform write operations
      // Adjust based on your RLS policies
      
      try {
        // Try to insert a test record (this might fail due to RLS)
        final testData = {
          'name': 'Test User Preference',
          'created_at': DateTime.now().toIso8601String(),
        };
        
        // Note: This might fail due to RLS policies, which is expected
        await supabaseClient
            .from('user_preferences')
            .insert(testData)
            .select()
            .single();
            
        print("✅ Write operation successful");
      } catch (e) {
        print("⚠️ Write operation failed (likely due to RLS): ${e.toString().split('\n')[0]}");
        // This is often expected in production databases with proper RLS
      }
    });

    test('should verify real-time subscriptions work', () async {
      try {
        // Test if we can create a subscription (doesn't need to receive data)
        final subscription = supabaseClient
            .from('products')
            .stream(primaryKey: ['id'])
            .limit(1);
            
        expect(subscription, isNotNull);
        print("✅ Real-time subscription created successfully");
        
        // Clean up the subscription
        await subscription.drain();
      } catch (e) {
        print("⚠️ Real-time subscription test failed: $e");
      }
    });
  });
} 