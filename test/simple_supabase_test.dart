import 'package:flutter_test/flutter_test.dart';
import 'package:supabase/supabase.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() {
  group('Simple Supabase Connection Tests', () {
    late SupabaseClient supabaseClient;
    late String supabaseUrl;
    late String supabaseKey;
    
    setUpAll(() async {
      // Load environment variables
      try {
        await dotenv.load(fileName: ".env");
        print("✅ Environment variables loaded successfully");
      } catch (e) {
        print("⚠️ .env file not found, using fallback values: $e");
      }
      
      // Get Supabase credentials
      supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 
          'https://lwnfzmvqahraputvfkos.supabase.co';
      supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao';
      
      // Create a simple Supabase client (not the Flutter version)
      supabaseClient = SupabaseClient(supabaseUrl, supabaseKey);
      
      print("✅ Supabase client created for: $supabaseUrl");
    });

    test('should create Supabase client successfully', () {
      expect(supabaseClient, isNotNull);
      expect(supabaseUrl, isNotEmpty);
      expect(supabaseKey, isNotEmpty);
    });

    test('should verify required tables exist and are accessible', () async {
      final requiredTables = [
        'products',
        'ingredients', 
        'product_ingredients',
        'saved_products',
        'user_preferences',
        'allergens',
        'dietary_preferences',
        'avoided_ingredients'
      ];

      final tableResults = <String, bool>{};
      
      for (final tableName in requiredTables) {
        try {
          // Test table existence by querying with limit 0
          final response = await supabaseClient
              .from(tableName)
              .select('*')
              .limit(0);
          
          tableResults[tableName] = true;
          print("✅ Table '$tableName' exists and is accessible");
        } catch (e) {
          tableResults[tableName] = false;
          print("❌ Table '$tableName' error: ${e.toString().split('\n')[0]}");
        }
      }

      // Verify critical tables exist
      expect(tableResults['products'], isTrue, 
          reason: 'Products table should exist');
      expect(tableResults['ingredients'], isTrue, 
          reason: 'Ingredients table should exist');
      expect(tableResults['user_preferences'], isTrue, 
          reason: 'User preferences table should exist');
      
      print("📊 Table verification completed");
    });

    test('should query ingredients table successfully', () async {
      try {
        final response = await supabaseClient
            .from('ingredients')
            .select('*')
            .limit(5);
        
        expect(response, isA<List>());
        
        if (response.isNotEmpty) {
          print("✅ Sample ingredient found: ${response.first}");
          
          // Verify ingredient structure
          final firstIngredient = response.first;
          expect(firstIngredient, isA<Map<String, dynamic>>());
          
          // Check for expected fields (adjust based on your schema)
          final expectedFields = ['id', 'name'];
          for (final field in expectedFields) {
            if (firstIngredient.containsKey(field)) {
              print("✅ Ingredient has '$field' field");
            }
          }
        } else {
          print("⚠️ No ingredients found in database");
        }
      } catch (e) {
        fail("Failed to query ingredients table: $e");
      }
    });

    test('should query products table successfully', () async {
      try {
        final response = await supabaseClient
            .from('products')
            .select('*')
            .limit(3);
        
        expect(response, isA<List>());
        print("✅ Products query successful - found ${response.length} products");
        
        if (response.isNotEmpty) {
          final firstProduct = response.first;
          expect(firstProduct, isA<Map<String, dynamic>>());
          print("📦 Sample product: ${firstProduct['name'] ?? 'Unnamed product'}");
        }
      } catch (e) {
        print("⚠️ Products table query failed (may be empty): $e");
        // Don't fail the test if products table is empty
      }
    });

    test('should handle user preferences table', () async {
      try {
        final response = await supabaseClient
            .from('user_preferences')
            .select('id')
            .limit(10);
        
        expect(response, isA<List>());
        print("✅ User preferences query successful - found ${response.length} records");
      } catch (e) {
        print("⚠️ User preferences query failed: $e");
        // This might be expected if no users exist yet
      }
    });

    test('should test REST API endpoints availability', () async {
      try {
        // Test if we can access the REST API by making a simple query
        final response = await supabaseClient
            .from('ingredients')
            .select('count(*)')
            .limit(1);
            
        expect(response, isA<List>());
        print("✅ REST API is accessible");
      } catch (e) {
        print("⚠️ REST API test failed: $e");
      }
    });

    test('should validate database schema structure', () async {
      // Test that important tables have expected basic structure
      final testQueries = {
        'ingredients': 'id, name',
        'products': 'id, name, barcode',
        'user_preferences': 'id, user_id',
      };

      for (final entry in testQueries.entries) {
        final tableName = entry.key;
        final selectFields = entry.value;
        
        try {
          await supabaseClient
              .from(tableName)
              .select(selectFields)
              .limit(1);
          print("✅ Table '$tableName' has expected fields: $selectFields");
        } catch (e) {
          print("⚠️ Table '$tableName' schema issue: ${e.toString().split('\n')[0]}");
        }
      }
    });

    test('should test database connectivity and basic operations', () async {
      try {
        // Test a simple query to verify connectivity
        final response = await supabaseClient
            .from('ingredients')
            .select('id')
            .limit(1);
        
        expect(response, isA<List>());
        print("✅ Database connectivity test passed");
        
        // Test that we can access the database without errors
        expect(() => supabaseClient.from('ingredients'), returnsNormally);
        print("✅ Database client operations work correctly");
        
      } catch (e) {
        print("⚠️ Basic connectivity test failed: $e");
        // This test might pass even if no data exists
      }
    });
  });
} 