import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../lib/main.dart';
import '../lib/providers/supabase_provider.dart';
import '../lib/services/supabase_service.dart';
import '../lib/models/user.dart' as app_user;
import '../lib/providers/product_provider.dart';
import '../lib/providers/auth_provider.dart';
import '../lib/models/product.dart';
import '../lib/screens/home_screen.dart';
import '../lib/widgets/mlkit_scan_screen.dart';

// Simplified Mock Supabase client that doesn't require complex API matching
class MockSupabaseClient extends SupabaseClient {
  MockSupabaseClient() : super('http://localhost:3000', 'test-key');
  
  // We'll override specific methods in the service layer instead of the client layer
}

// Mock Supabase service with simplified implementations
class MockSupabaseService extends SupabaseService {
  MockSupabaseService() : super(MockSupabaseClient());
  
  @override
  Future<app_user.User?> getCurrentUser() async {
    return null; // Anonymous user for testing
  }
  
  @override
  Future<Product> saveProduct(Product product) async {
    // Mock successful save
    final now = DateTime.now();
    return Product(
      id: 'mock-saved-${DateTime.now().millisecondsSinceEpoch}',
      barcode: product.barcode,
      name: product.name,
      brand: product.brand,
      safetyScore: product.safetyScore,
      safetyRating: product.safetyRating,
      scannedAt: product.scannedAt,
      createdAt: now,
      updatedAt: now,
    );
  }
  
  @override
  Future<List<Product>> getUserProducts() async {
    final now = DateTime.now();
    return [
      Product(
        id: 'mock-user-product-1',
        barcode: '1234567890123',
        name: 'Mock User Product',
        brand: 'Mock Brand',
        safetyScore: 85,
        safetyRating: 'Good',
        scannedAt: now,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
  
  @override
  Future<List<Product>> getSavedProducts() async {
    final now = DateTime.now();
    return [
      Product(
        id: 'mock-saved-product-1',
        barcode: '9876543210987',
        name: 'Mock Saved Product',
        brand: 'Saved Brand',
        safetyScore: 92,
        safetyRating: 'Excellent',
        scannedAt: now,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
  
  @override
  Future<Map<String, int>> getUserStats() async {
    return {
      'totalScans': 5,
      'safeProducts': 3,
      'savedProducts': 2,
    };
  }
  
  @override
  Future<void> saveProductForUser(String productId) async {
    // Mock successful save
    await Future.delayed(const Duration(milliseconds: 10));
  }
  
  @override
  Future<void> unsaveProductForUser(String productId) async {
    // Mock successful unsave
    await Future.delayed(const Duration(milliseconds: 10));
  }
  
  @override
  Future<bool> isProductSaved(String productId) async {
    return false; // Default to not saved
  }
  
  @override
  Future<List<Map<String, dynamic>>> getScanHistory(int days) async {
    return [
      {
        'date': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'scans': 2,
      },
      {
        'date': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
        'scans': 1,
      },
    ];
  }
}

// Mock Product controller with proper API
class MockProductController extends ProductController {
  MockProductController(super.ref);
  
  @override
  Future<void> scanProductByBarcode(String barcode, {String? imagePath}) async {
    // Simulate the complete analysis workflow
    state = const AsyncValue.loading();
    
    await Future.delayed(const Duration(milliseconds: 100));
    
    final now = DateTime.now();
    final mockProduct = Product(
      id: 'mock-analyzed-${DateTime.now().millisecondsSinceEpoch}',
      barcode: barcode,
      name: 'Mock Analyzed Product',
      brand: 'Mock Test Brand',
      safetyScore: 85,
      safetyRating: 'Good',
      scannedAt: now,
      createdAt: now,
      updatedAt: now,
      description: 'Mock product for integration testing',
      ingredientsJson: {
        'ingredients': ['Water', 'Sugar', 'Natural Flavoring']
      },
      nutritionScore: {'grade': 'B'},
      nutriscoreGrade: 'B',
    );
    
    // Simulate successful analysis completion
    state = AsyncValue.data(mockProduct);
  }
  
  @override
  Future<void> processManualIngredients(String productName, String brand, List<String> ingredients) async {
    state = const AsyncValue.loading();
    
    await Future.delayed(const Duration(milliseconds: 100));
    
    final now = DateTime.now();
    final mockProduct = Product(
      id: 'mock-manual-${DateTime.now().millisecondsSinceEpoch}',
      barcode: 'MANUAL-${DateTime.now().millisecondsSinceEpoch}',
      name: productName,
      brand: brand,
      safetyScore: 78,
      safetyRating: 'Good',
      scannedAt: now,
      createdAt: now,
      updatedAt: now,
      description: 'Manual entry product',
      ingredientsJson: {
        'ingredients': ingredients
      },
    );
    
    state = AsyncValue.data(mockProduct);
  }
}

// Mock Saved Products controller
class MockSavedProductsController extends SavedProductsController {
  MockSavedProductsController(super.ref);
  
  @override
  Future<void> saveProduct(String productId) async {
    state = const AsyncValue.loading();
    await Future.delayed(const Duration(milliseconds: 50));
    state = const AsyncValue.data(null);
  }
  
  @override
  Future<void> removeProduct(String productId) async {
    state = const AsyncValue.loading();
    await Future.delayed(const Duration(milliseconds: 50));
    state = const AsyncValue.data(null);
  }
  
  @override
  Future<bool> isProductSaved(String productId) async {
    return false; // Default to not saved for testing
  }
}

void main() {
  group('SafeScan Integration Tests', () {
    late MockSupabaseService mockSupabaseService;
    late List<Override> testOverrides;

    setUp(() {
      mockSupabaseService = MockSupabaseService();
      testOverrides = [
        supabaseServiceProvider.overrideWithValue(mockSupabaseService),
        productControllerProvider.overrideWith((ref) => MockProductController(ref)),
        savedProductsControllerProvider.overrideWith((ref) => MockSavedProductsController(ref)),
      ];
    });

    testWidgets('HomeScreen loads successfully with mock data', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      // Wait for initial build
      await tester.pumpAndSettle();

      // Verify home screen loads
      expect(find.byType(HomeScreen), findsOneWidget);
      
      print('✅ HomeScreen integration test passed');
    });

    testWidgets('MLKitScanScreen can be instantiated', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: Scaffold(
              body: MLKitScanScreen(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.byType(MLKitScanScreen), findsOneWidget);
      
      print('✅ MLKitScanScreen instantiation test passed');
    });

    testWidgets('Product scanning workflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Get the product controller to test scanning
      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );
      
      final productController = container.read(productControllerProvider.notifier);

      // Verify initial state
      expect(container.read(productControllerProvider).value, null);

      // Trigger barcode scan
      await productController.scanProductByBarcode('1234567890123');
      await tester.pump();

      // Verify product was analyzed and stored
      final productState = container.read(productControllerProvider);
      expect(productState.hasValue, true);
      expect(productState.value?.barcode, '1234567890123');
      expect(productState.value?.name, 'Mock Analyzed Product');
      expect(productState.value?.safetyScore, 85);
      
      print('✅ Product scanning workflow test passed');
    });

    testWidgets('Product scanning with image path', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );
      
      final productController = container.read(productControllerProvider.notifier);

      // Trigger barcode scan with image path
      await productController.scanProductByBarcode('9876543210123', imagePath: '/test/image.jpg');
      await tester.pump();

      // Verify product was analyzed with image
      final productState = container.read(productControllerProvider);
      expect(productState.hasValue, true);
      expect(productState.value?.barcode, '9876543210123');
      
      print('✅ Product scanning with image path test passed');
    });

    testWidgets('Manual ingredient processing workflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );
      
      final productController = container.read(productControllerProvider.notifier);

      // Test manual product analysis
      await productController.analyzeManualProduct(
        'Manual Test Product',
        'Manual Brand',
      );
      await tester.pump();

      final manualProduct = container.read(productControllerProvider).value!;
      expect(manualProduct.name, 'Manual Test Product');
      expect(manualProduct.brand, 'Manual Brand');
      expect(manualProduct.barcode, startsWith('MANUAL-'));
      
      print('✅ Manual ingredient processing test passed');
    });

    testWidgets('Saved products workflow', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );
      
      final savedProductsController = container.read(savedProductsControllerProvider.notifier);

      // Test saving a product
      await savedProductsController.saveProduct('test-product-id');
      await tester.pump();

      // Verify save operation completed
      final saveState = container.read(savedProductsControllerProvider);
      expect(saveState.hasValue, true);
      
      // Test unsaving a product
      await savedProductsController.removeProduct('test-product-id');
      await tester.pump();

      // Verify unsave operation completed
      final unsaveState = container.read(savedProductsControllerProvider);
      expect(unsaveState.hasValue, true);
      
      print('✅ Saved products workflow test passed');
    });

    testWidgets('Backend integration - User stats and products', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );

      // Test user stats provider
      final userStats = await container.read(userStatsProvider.future);
      expect(userStats['totalScans'], 5);
      expect(userStats['safeProducts'], 3);
      expect(userStats['savedProducts'], 2);

      // Test user products provider
      final userProducts = await container.read(userProductsProvider.future);
      expect(userProducts.length, 1);
      expect(userProducts.first.name, 'Mock User Product');

      // Test saved products provider
      final savedProducts = await container.read(savedProductsProvider.future);
      expect(savedProducts.length, 1);
      expect(savedProducts.first.name, 'Mock Saved Product');
      
      print('✅ Backend integration test passed');
    });

    testWidgets('Scan history provider', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );

      // Test scan history provider
      final scanHistory = await container.read(scanHistoryProvider(7).future);
      expect(scanHistory.length, 2);
      expect(scanHistory.first['scans'], 2);
      
      print('✅ Scan history provider test passed');
    });

    testWidgets('Complete end-to-end workflow simulation', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: testOverrides,
          child: MaterialApp(
            home: HomeScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final container = ProviderScope.containerOf(
        tester.element(find.byType(HomeScreen)),
      );

      print('🚀 Starting end-to-end workflow simulation...');

      // Step 1: Scan a product
      final productController = container.read(productControllerProvider.notifier);
      await productController.scanProductByBarcode('9876543210123', imagePath: '/test/image.jpg');
      await tester.pump();

      final scannedProduct = container.read(productControllerProvider).value!;
      expect(scannedProduct.barcode, '9876543210123');
      print('✅ Step 1: Product scanned and analyzed');

      // Step 2: Save the analyzed product
      final savedProductsController = container.read(savedProductsControllerProvider.notifier);
      await savedProductsController.saveProduct(scannedProduct.id);
      await tester.pump();
      print('✅ Step 2: Product saved successfully');

      // Step 3: Verify backend state updated
      final updatedStats = await container.read(userStatsProvider.future);
      expect(updatedStats['totalScans'], greaterThan(0));
      print('✅ Step 3: Backend stats verified');

      // Step 4: Test manual ingredient processing
      await productController.analyzeManualProduct(
        'Manual Test Product',
        'Manual Brand',
      );
      await tester.pump();

      final manualProduct = container.read(productControllerProvider).value!;
      expect(manualProduct.name, 'Manual Test Product');
      expect(manualProduct.barcode, startsWith('MANUAL-'));
      print('✅ Step 4: Manual ingredient processing completed');

      // Step 5: Clear current product and verify state
      productController.clearCurrentProduct();
      await tester.pump();
      
      final clearedState = container.read(productControllerProvider);
      expect(clearedState.value, null);
      print('✅ Step 5: Product state cleared');

      print('🎉 Complete end-to-end workflow simulation passed!');
    });
  });
}