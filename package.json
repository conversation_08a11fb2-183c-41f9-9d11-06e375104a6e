{"name": "safescan-taskmaster", "version": "1.0.0", "description": "Task Master AI integration for SafeScan Flutter project", "scripts": {"taskmaster": "npx task-master-ai", "setup": "echo 'Task Master AI is ready! Add your API keys to .env file and restart Cursor.'"}, "dependencies": {"task-master-ai": "^1.0.0"}, "devDependencies": {}, "keywords": ["task-management", "ai", "cursor", "mcp"], "author": "SafeScan Team", "license": "MIT"}