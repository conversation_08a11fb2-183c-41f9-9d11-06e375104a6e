# SafeScan Product Requirements Document (PRD)

## 1. App Purpose & Vision
SafeScan is a mobile app that allows users to scan product barcodes, analyze ingredients, and determine product safety using AI, web data, and a central database. The app empowers users to make informed, safe choices about the products they use.

## 2. Core Features
- Barcode Scanning: Users scan product barcodes.
- Database Lookup: Check if product exists in local/Supabase database.
- Open Food Facts API Integration: Fetch product info if not in local DB.
- Image Capture & AI Analysis: If no data, prompt user for product image; use Gemini AI for vision analysis.
- Web Scraping & Data Aggregation: Use Gemini and web scraping to gather ingredient and product info.
- Ingredient Normalization & Safety Analysis: Normalize ingredient names, match against Supabase, and apply safety criteria.
- Safety Report Generation: Generate and present detailed safety/risk reports.
- User Feedback Loop: Collect feedback to improve ML models and database.
- User Authentication: Register/login for personalized experience.
- Profile & Scan History: View past scans and reports.
- Modern, Responsive UI: Intuitive, accessible, and visually appealing.

## 3. User Stories
- As a user, I want to scan a product barcode and instantly know if it’s safe.
- As a user, I want to see detailed ingredient and safety information.
- As a user, I want to scan a product image if barcode data is missing.
- As a user, I want to view my scan history and previous reports.
- As a user, I want to register and log in to save my data.
- As a user, I want to provide feedback on product safety results.

## 4. Technical Requirements
- Flutter for cross-platform mobile development.
- Supabase for database, authentication, and storage.
- Gemini AI for image and web data analysis.
- Open Food Facts API for product data.
- Modern state management (Provider, Riverpod, or Bloc).
- Secure API and data handling.
- Automated and manual testing.
- CI/CD ready structure.

## 5. Non-Functional Requirements
- Fast and responsive UI/UX.
- High accuracy in ingredient and safety analysis.
- Scalable backend and database.
- Secure user data and privacy.
- Accessibility for all users.

## 6. Success Criteria
- Users can scan products and receive accurate safety reports.
- The app handles missing/unknown products gracefully.
- All major flows (scan, analyze, report, feedback) work without errors.
- The app passes all tests and is ready for release.
