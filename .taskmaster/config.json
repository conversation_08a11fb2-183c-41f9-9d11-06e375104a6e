{"models": {"main": {"provider": "openrouter", "modelId": "openai/gpt-4.1", "maxTokens": 1000000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "openai/gpt-4.1", "maxTokens": 1000000, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview-04-17", "maxTokens": 1048000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}