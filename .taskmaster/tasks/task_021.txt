# Task ID: 21
# Title: Implement User Profile and Scan History View
# Status: pending
# Dependencies: 13, 20
# Priority: medium
# Description: Implement the user profile screen and display the user's scan history.
# Details:
Create a 'Profile' screen UI. Query the `scans` and `reports` tables in Supabase filtered by the authenticated user's ID. Display a list of past scans, potentially with a summary or link to the full report. Allow users to view basic profile information.

# Test Strategy:
Test viewing history for a user with multiple scans. Test viewing history for a new user with no scans. Verify correct reports are linked from the history. Ensure profile information is displayed correctly.
