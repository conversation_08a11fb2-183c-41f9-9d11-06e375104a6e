# Task ID: 15
# Title: Implement Supabase Database Product Lookup
# Status: pending
# Dependencies: 12, 14
# Priority: medium
# Description: Develop logic to query the local Supabase database for product information using the scanned barcode.
# Details:
Write a function that takes a barcode string. Use `Supabase.instance.client.from('products').select().eq('barcode', barcode)` to query the database. Handle cases where the product is found or not found. Return product data or a 'not found' indicator.

# Test Strategy:
Test lookup with barcodes known to be in the database. Test lookup with barcodes not in the database. Verify correct product data is returned when found. Ensure error handling for database connection issues.
