# Task ID: 22
# Title: Implement User Feedback Mechanism
# Status: pending
# Dependencies: 13, 20
# Priority: low
# Description: Develop a mechanism for users to provide feedback on the accuracy of safety reports.
# Details:
Add a 'Provide Feedback' option on the safety report screen. Create a UI form for feedback text and potentially a rating. Implement logic to save the feedback data (linked to the user and report) into the `feedback` table in Supabase.

# Test Strategy:
Test submitting feedback on a report. Verify feedback is correctly stored in the database, linked to the user and report. Test input validation for the feedback form.
