import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Script to run SQL migrations for the SafeScan MCP tables on Supabase
/// 
/// Usage:
/// dart run lib/scripts/run_migration.dart
///
/// This will read the supabase_migration.sql file and execute the SQL statements
/// on your Supabase database to create the necessary MCP tables.

Future<void> main() async {
  print('🚀 SafeScan MCP Database Migration');
  print('=================================\n');
  
  try {
    // Load environment variables
    await dotenv.load(fileName: '.env');
    print('✅ Environment variables loaded');
    
    // Get Supabase URL and key
    final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 'https://lwnfzmvqahraputvfkos.supabase.co';
    final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao';
    
    print('🌐 Supabase URL: $supabaseUrl');
    
    // Test connection
    print('\n🔄 Testing connection to Supabase...');
    final connectionResult = await _testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful');
    
    // Load SQL file
    final sqlFile = File('supabase_migration.sql');
    if (!await sqlFile.exists()) {
      print('❌ SQL migration file not found at: ${sqlFile.absolute.path}');
      print('Make sure the supabase_migration.sql file exists in the project root.');
      exit(1);
    }
    
    final sqlContent = await sqlFile.readAsString();
    print('✅ Migration file loaded: ${sqlFile.absolute.path}');
    
    // Split SQL content into individual statements
    final statements = _splitSqlStatements(sqlContent);
    
    print('\n🔄 Found ${statements.length} SQL statements to execute');
    
    // Ask for confirmation
    print('\n⚠️  Warning: This will create or modify tables in your Supabase database.');
    stdout.write('Continue? (y/n): ');
    final response = stdin.readLineSync()?.toLowerCase() ?? 'n';
    
    if (response != 'y') {
      print('❌ Migration aborted by user.');
      exit(0);
    }
    
    // Execute statements
    print('\n🔄 Executing SQL migration...\n');
    
    int successful = 0;
    int failed = 0;
    final errors = <String>[];
    
    for (var i = 0; i < statements.length; i++) {
      final statement = statements[i];
      final truncatedStatement = statement.length > 50 
          ? '${statement.substring(0, 50)}...' 
          : statement;
      
      stdout.write('Executing statement ${i+1}/${statements.length}: $truncatedStatement');
      
      try {
        final result = await _executeStatement(supabaseUrl, supabaseKey, statement);
        if (result) {
          print(' ✅');
          successful++;
        } else {
          print(' ❌');
          failed++;
          errors.add('Statement ${i+1}: $truncatedStatement');
        }
      } catch (e) {
        print(' ❌ - Error: $e');
        failed++;
        errors.add('Statement ${i+1}: $truncatedStatement - Error: $e');
      }
    }
    
    print('\n🎉 Migration completed!');
    print('  - Successful statements: $successful');
    print('  - Failed statements: $failed');
    
    if (errors.isNotEmpty) {
      print('\n⚠️  Errors encountered:');
      for (final error in errors) {
        print('  - $error');
      }
      print('\nSome tables may not have been created properly.');
    }
    
    // Verify tables
    print('\n🔍 Verifying tables...');
    final mcpTables = [
      'ai_analysis_cache',
      'scan_events',
      'product_analysis_fallback',
      'pending_ingredients',
      'user_feedback'
    ];
    
    for (final table in mcpTables) {
      final exists = await _checkTableExists(supabaseUrl, supabaseKey, table);
      print('  ${exists ? '✅' : '❌'} $table');
    }
    
    print('\n✅ Migration script completed');
    
  } catch (e) {
    print('❌ Error during migration: $e');
    exit(1);
  }
}

/// Test connection to Supabase
Future<bool> _testConnection(String supabaseUrl, String supabaseKey) async {
  try {
    final url = Uri.parse('$supabaseUrl/rest/v1/');
    final response = await http.get(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
      },
    );
    
    return response.statusCode == 200;
  } catch (e) {
    print('Error testing connection: $e');
    return false;
  }
}

/// Execute a SQL statement via the Supabase REST API
Future<bool> _executeStatement(String supabaseUrl, String supabaseKey, String sql) async {
  try {
    // Use the Supabase SQL API to execute the statement
    final url = Uri.parse('$supabaseUrl/rest/v1/rpc/');
    
    final response = await http.post(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'command': 'execute_sql',
        'sql': sql,
      }),
    );
    
    return response.statusCode >= 200 && response.statusCode < 300;
  } catch (e) {
    print('Error executing statement: $e');
    return false;
  }
}

/// Check if a table exists
Future<bool> _checkTableExists(String supabaseUrl, String supabaseKey, String tableName) async {
  try {
    final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?limit=0');
    final response = await http.get(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
      },
    );
    
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}

/// Split SQL content into individual statements
List<String> _splitSqlStatements(String sqlContent) {
  // Basic splitting by semicolons, considering semicolons in strings and comments
  // This is a simple implementation and might not handle all SQL syntax perfectly
  
  final statements = <String>[];
  final buffer = StringBuffer();
  bool inString = false;
  bool inLineComment = false;
  bool inBlockComment = false;
  
  for (var i = 0; i < sqlContent.length; i++) {
    final char = sqlContent[i];
    final nextChar = i < sqlContent.length - 1 ? sqlContent[i + 1] : '';
    
    // Handle string literals
    if (char == "'" && !inLineComment && !inBlockComment) {
      inString = !inString;
    }
    
    // Handle line comments
    if (char == '-' && nextChar == '-' && !inString && !inBlockComment) {
      inLineComment = true;
    }
    
    // End line comment at newline
    if ((char == '\n' || char == '\r') && inLineComment) {
      inLineComment = false;
    }
    
    // Handle block comments
    if (char == '/' && nextChar == '*' && !inString && !inLineComment) {
      inBlockComment = true;
    }
    
    // End block comment
    if (char == '*' && nextChar == '/' && inBlockComment) {
      inBlockComment = false;
      buffer.write(char);
      buffer.write(nextChar);
      i++;
      continue;
    }
    
    // Check for semicolon (statement separator)
    if (char == ';' && !inString && !inLineComment && !inBlockComment) {
      buffer.write(char);
      final statement = buffer.toString().trim();
      if (statement.isNotEmpty) {
        statements.add(statement);
      }
      buffer.clear();
      continue;
    }
    
    buffer.write(char);
  }
  
  // Add the last statement if there's anything left in the buffer
  final lastStatement = buffer.toString().trim();
  if (lastStatement.isNotEmpty) {
    statements.add(lastStatement);
  }
  
  return statements.where((s) => s.trim().isNotEmpty).toList();
} 