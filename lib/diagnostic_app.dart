import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Import our services
import 'services/gemini_service.dart';
import 'services/supabase_service.dart';
import 'services/supabase_init_service.dart';
// import 'services/safety_analysis_service.dart'; // Commented out - using enhanced service instead
import 'models/product.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  try {
    await dotenv.load(fileName: ".env");
  } catch (e) {
    print("Error loading .env file: $e");
  }
  
  // Initialize Supabase (required before app starts)
  try {
    await SupabaseInitService.initialize();
  } catch (e) {
    print("Error initializing Supabase: $e");
  }
  
  runApp(const DiagnosticApp());
}

class DiagnosticApp extends StatelessWidget {
  const DiagnosticApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SafeScan Diagnostic Tool',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Roboto',
      ),
      home: const DiagnosticScreen(),
    );
  }
}

class DiagnosticScreen extends StatefulWidget {
  const DiagnosticScreen({Key? key}) : super(key: key);

  @override
  _DiagnosticScreenState createState() => _DiagnosticScreenState();
}

class _DiagnosticScreenState extends State<DiagnosticScreen> {
  final _logController = TextEditingController();
  bool _isRunningTests = false;
  bool _showMigrationButton = false;
  final ScrollController _scrollController = ScrollController();
  
  // Define a test barcode (Beyond Good 70% Dark Chocolate)
  static const testBarcode = "898575001337";

  @override
  void dispose() {
    _logController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  void _log(String message) {
    setState(() {
      _logController.text += message + '\n';
    });
    
    // Auto-scroll to bottom
    Future.delayed(const Duration(milliseconds: 50), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
  
  void _showMigrationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Database Migration Required'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Some required tables are missing from your Supabase database.'),
                SizedBox(height: 10),
                Text('We\'ve created a SQL migration script that maps existing tables to the ones needed by the app:'),
                SizedBox(height: 10),
                Text('1. Open the Supabase dashboard'),
                Text('2. Go to the SQL Editor'),
                Text('3. Copy and paste the contents of lib/supabase_migration.sql'),
                Text('4. Run the script'),
                SizedBox(height: 10),
                Text('This will create views and procedures that allow the app to work with your existing database schema.'),
                SizedBox(height: 10),
                Text('For detailed instructions, refer to:'),
                Text('lib/database_migration_readme.md', style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK'),
            ),
          ],
        );
      }
    );
  }
  
  Future<void> _runDiagnostics() async {
    setState(() {
      _isRunningTests = true;
      _logController.clear();
    });
    
    _log("===== SAFESCAN API DIAGNOSTIC TOOL =====");
    _log("Testing critical services for product safety analysis");
    _log("===========================================\n");
    
    // 1. Test Supabase Connection
    _log("1. INITIALIZING SUPABASE CONNECTION");
    try {
      // Supabase is already initialized in main(), just check if it's connected
      final supabase = Supabase.instance.client;
      await supabase.from('ingredients').select().limit(1);
      _log("✅ Supabase connection verified");
    } catch (e) {
      _log("❌ Supabase connection failed: $e");
      setState(() {
        _isRunningTests = false;
      });
      return;
    }
    
    // 2. Test OpenFoodFacts API
    _log("\n2. TESTING OPENFOODFACTS API");
    _log("Using test barcode: $testBarcode");
    
    try {
      final openFoodFactsBaseUrl = dotenv.env['OPENFOODFACTS_API_URL'] ?? 'https://world.openfoodfacts.org/api/v0/product';
      final openFoodFactsUserAgent = dotenv.env['OPENFOODFACTS_USER_AGENT'] ?? 'SafeScan - Product Safety App';
      
      _log("Connecting to OpenFoodFacts API: $openFoodFactsBaseUrl");
      final apiUrl = '$openFoodFactsBaseUrl/$testBarcode.json';
      
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {'User-Agent': openFoodFactsUserAgent},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1) {
          _log("✅ OpenFoodFacts API connection successful");
          _log("Product found: ${data['product']['product_name']}");
          _log("Brand: ${data['product']['brands']}");
          
          // Display ingredients if available
          if (data['product']['ingredients_text'] != null) {
            _log("Ingredients: ${data['product']['ingredients_text']}");
          } else {
            _log("No ingredients information available");
          }
        } else {
          _log("⚠️ OpenFoodFacts API returned status ${data['status']} - product not found");
        }
      } else {
        _log("❌ OpenFoodFacts API returned error: ${response.statusCode}");
      }
    } catch (e) {
      _log("❌ OpenFoodFacts API test failed: $e");
    }
    
    // 3. Test Gemini AI API
    _log("\n3. TESTING GEMINI AI API");
    try {
      final geminiApiKey = dotenv.env['GEMINI_API_KEY'];
      if (geminiApiKey == null || geminiApiKey.isEmpty) {
        _log("⚠️ Gemini API key not found in environment variables");
      } else {
        _log("Gemini API key found in environment variables");
      }
      
      // Initialize Gemini service
      final geminiInitialized = await GeminiService.initialize();
      if (geminiInitialized) {
        _log("✅ Gemini service initialized successfully");
        
        // First test a lightweight request to gemini-2.0-flash model
        try {
          _log("\nTesting gemini-2.0-flash model with a lightweight request");
          // This is a very small prompt to test basic connectivity
          final flashResponse = await _testGeminiModel('gemini-2.0-flash', 'What is 2+2?');
          if (flashResponse.contains('4')) {
            _log("✅ gemini-2.0-flash model working");
          } else {
            _log("⚠️ gemini-2.0-flash returned unexpected response: $flashResponse");
          }
        } catch (e) {
          _log("❌ gemini-2.0-flash test failed: $e");
        }
        
        // Test gemini-1.5-pro model with a lightweight request
        try {
          _log("\nTesting gemini-1.5-pro model with a lightweight request");
          final proResponse = await _testGeminiModel('gemini-1.5-pro', 'What is 3+3?');
          if (proResponse.contains('6')) {
            _log("✅ gemini-1.5-pro model working");
          } else {
            _log("⚠️ gemini-1.5-pro returned unexpected response: $proResponse");
          }
        } catch (e) {
          // Check for rate limit error
          if (e.toString().contains('429') || e.toString().contains('rate limit')) {
            _log("⚠️ gemini-1.5-pro model rate limited (429 error)");
            _log("  This is expected if you've made many requests recently");
            _log("  The app should automatically fall back to gemini-2.0-flash");
          } else {
            _log("❌ gemini-1.5-pro test failed: $e");
          }
        }
        
        // Test a simple ingredient analysis request to verify API functionality
        try {
          final testIngredient = "Organic cocoa beans";
          _log("\nTesting ingredient analysis with: '$testIngredient'");
          
          final ingredientAnalysis = await GeminiService.assessUnknownIngredient(testIngredient);
          _log("✅ Gemini AI API ingredient analysis succeeded");
          _log("Safety level: ${ingredientAnalysis['safety_level']}");
          _log("Description: ${ingredientAnalysis['description']}");
        } catch (e) {
          _log("❌ Gemini ingredient analysis failed: $e");
          
          if (e.toString().contains('429') || e.toString().contains('rate limit')) {
            _log("\n⚠️ Gemini API is rate limited");
            _log("Please note the following about Gemini API rate limits:");
            _log("- Free tier: 60 requests/minute, up to ~350k characters/minute");
            _log("- If rate limited, the app should automatically switch models");
            _log("- Wait a few minutes and try again");
          }
        }
      } else {
        _log("❌ Gemini service initialization failed");
      }
    } catch (e) {
      _log("❌ Gemini AI API test failed: $e");
    }
    
    // 4. Test Supabase for Safety Analysis
    _log("\n4. TESTING SUPABASE FOR SAFETY ANALYSIS");
    try {
      // Check if the required tables exist for safety analysis
      final tableNames = [
        'ingredients',
        'products',
        'ai_analysis_cache',
        'pending_ingredients',
        'scan_logs',
        'unknown_barcodes'
      ];
      
      _log("Checking required tables for safety analysis:");
      
      final missingTables = <String>[];
      
      for (final tableName in tableNames) {
        try {
          final supabase = Supabase.instance.client;
          await supabase.from(tableName).select().limit(1);
          _log(" - ✅ $tableName (exists)");
        } catch (e) {
          _log(" - ❌ $tableName (error: ${e.toString().split('\n')[0]})");
          missingTables.add(tableName);
        }
      }
      
      if (missingTables.isNotEmpty) {
        _log("\nWARNING: Some required tables are missing: ${missingTables.join(', ')}");
        _log("Run the SQL migration script from lib/supabase_migration.sql to fix this issue");
        
        // Set flag to show migration button
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            _showMigrationButton = true;
          });
        });
      }
      
      // Test ingredient database query
      try {
        _log("\nTesting ingredient database lookup:");
        final testIngredient = "cocoa";
        
        // Create SupabaseService instance
        final supabaseService = SupabaseService(SupabaseInitService.client);
        final ingredients = await supabaseService.searchIngredients(testIngredient);
        
        if (ingredients.isNotEmpty) {
          _log("✅ Found ${ingredients.length} ingredients matching '$testIngredient'");
          _log("First match: ${ingredients[0].name} (Safety level: ${ingredients[0].safetyLevel})");
        } else {
          _log("⚠️ No ingredients found matching '$testIngredient'");
        }
      } catch (e) {
        _log("❌ Ingredient database test failed: $e");
      }
    } catch (e) {
      _log("❌ Supabase safety analysis test failed: $e");
    }
    
    // 5. Test End-to-End Safety Analysis
    _log("\n5. TESTING END-TO-END SAFETY ANALYSIS");
    try {
      // Create a test product for safety analysis
      final testProduct = Product(
        id: 'test-id',
        name: 'Organic Dark Chocolate',
        brand: 'Test Brand',
        barcode: testBarcode,
        safetyScore: 0,
        safetyRating: 'Unknown',
        scannedAt: DateTime.now(),
        userId: 'test-user',
        ingredientsJson: {
          'ingredients': [
            {'name': 'Organic cocoa beans'},
            {'name': 'Organic cane sugar'},
            {'name': 'Organic cocoa butter'},
            {'name': 'Sea salt'}
          ]
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _log("Running safety analysis on test product: ${testProduct.name}");
      // final analysisResult = await SafetyAnalysisService.analyzeProduct(testProduct); // Commented out - using enhanced service instead
      
      _log("✅ Safety analysis completed successfully (diagnostic disabled)");
      // _log("Safety Score: ${analysisResult.overallScore}/100");
      // _log("Safety Rating: ${analysisResult.safetyRating}");
      
      // if (analysisResult.violatingIngredients.isNotEmpty) {
      //   _log("Violating ingredients found: ${analysisResult.violatingIngredients}");
      // } else {
        _log("No violating ingredients found (diagnostic disabled)");
      // }
    } catch (e) {
      _log("❌ End-to-end safety analysis test failed: $e");
    }
    
    _log("\n===== DIAGNOSTIC TEST SUMMARY =====");
    _log("1. Supabase Connection: TESTED");
    _log("2. OpenFoodFacts API: TESTED");
    _log("3. Gemini AI API: TESTED");
    _log("4. Supabase for Safety Analysis: TESTED");
    _log("5. End-to-End Safety Analysis: TESTED");
    _log("===================================");
    
    _log("\nDiagnostic tests completed. You can review the results above to identify any issues.");
    
    setState(() {
      _isRunningTests = false;
    });
  }

  Future<String> _testGeminiModel(String model, String prompt) async {
    try {
      // Call the Gemini API directly with the specified model
      final apiKey = dotenv.env['GEMINI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        return "No API key available";
      }
      
      final url = Uri.parse('https://generativelanguage.googleapis.com/v1/models/$model:generateContent?key=$apiKey');
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'role': 'user',
              'parts': [{'text': prompt}],
            },
          ],
          'generationConfig': {
            'temperature': 0.2,
            'topP': 0.8,
            'topK': 40,
            'maxOutputTokens': 100,
          },
        }),
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final candidates = data['candidates'] as List<dynamic>;
        
        if (candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          final parts = content['parts'] as List<dynamic>;
          
          if (parts.isNotEmpty) {
            return parts[0]['text'] as String;
          }
        }
        
        return "No text content in response";
      } else if (response.statusCode == 429) {
        throw Exception('Gemini API rate limit exceeded (429) for model $model');
      } else {
        throw Exception('Gemini API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw e; // Re-throw the exception for the caller to handle
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SafeScan Diagnostic Tool'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      'Service Diagnostic Tool',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This tool tests the connection to critical services for product safety analysis:',
                      style: TextStyle(fontSize: 16),
                    ),
                    SizedBox(height: 8),
                    Text('• Supabase Database Connection'),
                    Text('• OpenFoodFacts API'),
                    Text('• Gemini AI API'),
                    Text('• Safety Analysis with all components'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunningTests ? null : _runDiagnostics,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Text(
                  _isRunningTests ? 'Running Tests...' : 'Run Diagnostic Tests',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            if (_showMigrationButton)
              Container(
                margin: const EdgeInsets.only(top: 16),
                child: ElevatedButton.icon(
                  onPressed: _showMigrationDialog,
                  icon: const Icon(Icons.engineering),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                  ),
                  label: const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Text(
                      'Run Database Migration',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            const Text(
              'Diagnostic Log:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _logController,
                  scrollController: _scrollController,
                  readOnly: true,
                  maxLines: null,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontFamily: 'monospace',
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Run tests to see diagnostic results',
                    hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 