/// Constants and utility methods for safety analysis criteria
class SafetyCriteria {
  /// List of harmful ingredients that should be avoided
  static const List<String> harmfulIngredients = [
    'parabens',
    'phthalates',
    'formaldehyde',
    'toluene',
    'triclosan',
    'sodium lauryl sulfate',
    'bha',
    'bht',
    'coal tar',
    'hydroquinone',
    'oxybenzone',
    'resorcinol',
    'dibutyl phthalate',
    'trichloroethylene',
    'lead acetate',
    'petroleum distillates',
    'methylisothiazolinone',
    'styrene',
    'polyethylene glycol',
    'propylene glycol',
    'butylated compounds',
    'parfum',
  ];

  /// List of ingredients with potential allergenic properties
  static const List<String> allergenicIngredients = [
    'fragrances',
    'parfum',
    'sodium lauryl sulfate',
    'sodium laureth sulfate',
    'lanolin',
    'propylene glycol',
    'methylisothiazolinone',
    'formaldehyde',
    'imidazolidinyl urea',
    'cobalt chloride',
    'balsam of peru',
    'cocamidopropyl betaine',
    'neomycin',
    'benzalkonium chloride',
    'nickel',
    'quaternium-15',
    'diazolidinyl urea',
  ];

  /// List of ingredients to avoid during pregnancy
  static const List<String> pregnancyRestricted = [
    'retinol',
    'retinyl palmitate',
    'retinoic acid',
    'tretinoin',
    'isotretinoin',
    'hydroquinone',
    'benzoyl peroxide',
    'salicylic acid',
    'tetracycline',
    'dihydroxyacetone',
    'formaldehyde',
    'toluene',
    'dibutyl phthalate',
    'chemical sunscreens',
    'oxybenzone',
    'thioglycolic acid',
  ];

  /// List of eco-friendly ingredients
  static const List<String> ecoFriendlyIngredients = [
    'aloe vera',
    'shea butter',
    'coconut oil',
    'jojoba oil',
    'tea tree oil',
    'olive oil',
    'argan oil',
    'cocoa butter',
    'beeswax',
    'glycerin',
    'vegetable glycerin',
    'zinc oxide',
    'titanium dioxide',
    'plant extracts',
    'essential oils',
    'rosemary extract',
    'calendula extract',
    'chamomile extract',
  ];

  /// List of concerning chemicals
  static const List<String> concerningChemicals = [
    'phthalates',
    'parabens',
    'triclosan',
    'benzophenone',
    'dmdm hydantoin',
    'formaldehyde',
    'toluene',
    'petroleum',
    'mineral oil',
    'paraffin',
    'butylated hydroxyanisole',
    'butylated hydroxytoluene',
    'sodium lauryl sulfate',
    'polyethylene',
    'microbeads',
    'diethanolamine',
    'triethanolamine',
    'synthetic fragrances',
    'artificial colors',
  ];

  /// Criteria map for reference in other parts of the app
  static final Map<String, Map<String, String>> criteria = {
    'harmful_free': {
      'name': 'Harmful Ingredients',
      'description': 'Product should be free from known harmful ingredients',
    },
    'allergen_safety': {
      'name': 'Allergen Safety',
      'description': 'Product should have minimal allergen risks',
    },
    'pregnancy_safety': {
      'name': 'Pregnancy Safety',
      'description': 'Product should be safe for use during pregnancy',
    },
    'chemical_safety': {
      'name': 'Chemical Safety',
      'description': 'Product should minimize use of concerning chemicals',
    },
    'eco_friendly': {
      'name': 'Eco-Friendly',
      'description': 'Product should use environmentally friendly ingredients',
    },
  };

  /// Get a detailed analysis of ingredients based on the safety criteria
  static Map<String, dynamic> getDetailedAnalysis(List<String> ingredients) {
    // Convert ingredients to lowercase for case-insensitive matching
    final ingredientsLower = ingredients.map((i) => i.toLowerCase().trim()).toList();
    
    // Track violating ingredients by criteria
    final Map<String, List<String>> violatingIngredients = {
      'harmful': [],
      'allergenic': [],
      'pregnancy_restricted': [],
      'concerning_chemicals': [],
    };
    
    // Assess ingredients against each criteria
    for (final ingredient in ingredientsLower) {
      // Check for harmful ingredients
      for (final harmful in harmfulIngredients) {
        if (ingredient.contains(harmful)) {
          violatingIngredients['harmful']!.add(ingredient);
          break;
        }
      }
      
      // Check for allergenic ingredients
      for (final allergenic in allergenicIngredients) {
        if (ingredient.contains(allergenic)) {
          violatingIngredients['allergenic']!.add(ingredient);
          break;
        }
      }
      
      // Check for pregnancy restricted ingredients
      for (final restricted in pregnancyRestricted) {
        if (ingredient.contains(restricted)) {
          violatingIngredients['pregnancy_restricted']!.add(ingredient);
          break;
        }
      }
      
      // Check for concerning chemicals
      for (final chemical in concerningChemicals) {
        if (ingredient.contains(chemical)) {
          violatingIngredients['concerning_chemicals']!.add(ingredient);
          break;
        }
      }
    }
    
    // Count eco-friendly ingredients
    final ecoFriendlyCount = ingredientsLower.where((ingredient) {
      return ecoFriendlyIngredients.any((eco) => ingredient.contains(eco));
    }).length;
    
    // Calculate criteria scores (0-100)
    final Map<String, int> criteriaScores = {
      'harmful_free': _calculateCriteriaScore(violatingIngredients['harmful']!.length, ingredients.length, 3.0),
      'allergen_safety': _calculateCriteriaScore(violatingIngredients['allergenic']!.length, ingredients.length, 2.0),
      'pregnancy_safety': _calculateCriteriaScore(violatingIngredients['pregnancy_restricted']!.length, ingredients.length, 2.5),
      'chemical_safety': _calculateCriteriaScore(violatingIngredients['concerning_chemicals']!.length, ingredients.length, 2.0),
      'eco_friendly': _calculateEcoFriendlyScore(ecoFriendlyCount, ingredients.length),
    };
    
    // Calculate overall score (weighted average)
    final overallScore = (
      criteriaScores['harmful_free']! * 0.35 +
      criteriaScores['allergen_safety']! * 0.20 +
      criteriaScores['pregnancy_safety']! * 0.15 +
      criteriaScores['chemical_safety']! * 0.20 +
      criteriaScores['eco_friendly']! * 0.10
    ).round();
    
    return {
      'overall_score': overallScore,
      'criteria_scores': criteriaScores,
      'criteria_results': {
        'harmful_free': {
          'score': criteriaScores['harmful_free'],
          'description': _getHarmfulFreeDescription(criteriaScores['harmful_free']!),
        },
        'allergen_safety': {
          'score': criteriaScores['allergen_safety'],
          'description': _getAllergenSafetyDescription(criteriaScores['allergen_safety']!),
        },
        'pregnancy_safety': {
          'score': criteriaScores['pregnancy_safety'],
          'description': _getPregnancySafetyDescription(criteriaScores['pregnancy_safety']!),
        },
        'chemical_safety': {
          'score': criteriaScores['chemical_safety'],
          'description': _getChemicalSafetyDescription(criteriaScores['chemical_safety']!),
        },
        'eco_friendly': {
          'score': criteriaScores['eco_friendly'],
          'description': _getEcoFriendlyDescription(criteriaScores['eco_friendly']!),
        },
      },
      'violating_ingredients': violatingIngredients,
    };
  }
  
  /// Calculate a criteria score based on number of violating ingredients
  static int _calculateCriteriaScore(int violatingCount, int totalCount, double weight) {
    if (totalCount == 0) return 50; // Default score for empty ingredient list
    
    // Weighted penalty for each violating ingredient
    final double violationRatio = violatingCount / totalCount;
    final double weightedRatio = violationRatio * weight;
    
    // Convert to a 0-100 score (higher is better)
    return ((1.0 - weightedRatio) * 100).clamp(0, 100).round();
  }
  
  /// Calculate eco-friendly score
  static int _calculateEcoFriendlyScore(int ecoFriendlyCount, int totalCount) {
    if (totalCount == 0) return 50; // Default score for empty ingredient list
    
    // Percentage of eco-friendly ingredients
    final double ratio = ecoFriendlyCount / totalCount;
    
    // Convert to a 0-100 score (higher is better)
    return (ratio * 100).clamp(0, 100).round();
  }
  
  /// Get description for harmful-free criteria
  static String _getHarmfulFreeDescription(int score) {
    if (score >= 90) return 'Free from harmful ingredients';
    if (score >= 70) return 'Contains few potentially harmful ingredients';
    if (score >= 50) return 'Contains some harmful ingredients';
    return 'Contains many harmful ingredients';
  }
  
  /// Get description for allergen safety criteria
  static String _getAllergenSafetyDescription(int score) {
    if (score >= 90) return 'Very low allergy risk';
    if (score >= 70) return 'Low allergy risk';
    if (score >= 50) return 'Moderate allergy risk';
    return 'High allergy risk';
  }
  
  /// Get description for pregnancy safety criteria
  static String _getPregnancySafetyDescription(int score) {
    if (score >= 90) return 'Safe during pregnancy';
    if (score >= 70) return 'Generally safe during pregnancy';
    if (score >= 50) return 'Use with caution during pregnancy';
    return 'Not recommended during pregnancy';
  }
  
  /// Get description for chemical safety criteria
  static String _getChemicalSafetyDescription(int score) {
    if (score >= 90) return 'Very low chemical concern';
    if (score >= 70) return 'Low chemical concern';
    if (score >= 50) return 'Moderate chemical concern';
    return 'High chemical concern';
  }
  
  /// Get description for eco-friendly criteria
  static String _getEcoFriendlyDescription(int score) {
    if (score >= 90) return 'Very eco-friendly';
    if (score >= 70) return 'Eco-friendly';
    if (score >= 50) return 'Somewhat eco-friendly';
    return 'Not eco-friendly';
  }

  static const List<Map<String, String>> safetyCriteria = [
    {
      'name': 'allergens',
      'description': 'Check for common allergens',
      'threshold': '0',
    },
    {
      'name': 'artificial_colors',
      'description': 'Check for artificial colors',
      'threshold': '0',
    },
    {
      'name': 'artificial_preservatives',
      'description': 'Check for artificial preservatives',
      'threshold': '0',
    },
    {
      'name': 'artificial_sweeteners',
      'description': 'Check for artificial sweeteners',
      'threshold': '0',
    },
    {
      'name': 'gmo_ingredients',
      'description': 'Check for GMO ingredients',
      'threshold': '0',
    },
    {
      'name': 'high_fructose_corn_syrup',
      'description': 'Check for high fructose corn syrup',
      'threshold': '0',
    },
    {
      'name': 'trans_fats',
      'description': 'Check for trans fats',
      'threshold': '0',
    },
    {
      'name': 'sodium_level',
      'description': 'Check sodium levels',
      'threshold': '500',
    },
  ];

  /// Evaluate a single safety criterion against a list of ingredients
  static Future<Map<String, dynamic>> evaluateCriterion(
    Map<String, String> criterion,
    List<Map<String, dynamic>> ingredients,
  ) async {
    try {
      final violations = <String>[];
      var score = 100;
      
      switch (criterion['name']) {
        case 'allergens':
          for (final ingredient in ingredients) {
            if (_isAllergen(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 20;
            }
          }
          break;
          
        case 'artificial_colors':
          for (final ingredient in ingredients) {
            if (_isArtificialColor(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 15;
            }
          }
          break;
          
        case 'artificial_preservatives':
          for (final ingredient in ingredients) {
            if (_isArtificialPreservative(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 15;
            }
          }
          break;
          
        case 'artificial_sweeteners':
          for (final ingredient in ingredients) {
            if (_isArtificialSweetener(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 15;
            }
          }
          break;
          
        case 'gmo_ingredients':
          for (final ingredient in ingredients) {
            if (_isGmoIngredient(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 20;
            }
          }
          break;
          
        case 'high_fructose_corn_syrup':
          for (final ingredient in ingredients) {
            if (_isHighFructoseCornSyrup(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 25;
            }
          }
          break;
          
        case 'trans_fats':
          for (final ingredient in ingredients) {
            if (_isTransFat(ingredient['name'] as String)) {
              violations.add(ingredient['name'] as String);
              score -= 30;
            }
          }
          break;
          
        case 'sodium_level':
          final sodiumLevel = _getSodiumLevel(ingredients);
          if (sodiumLevel > int.parse(criterion['threshold']!)) {
            violations.add('High sodium content: ${sodiumLevel}mg');
            score -= 25;
          }
          break;
      }
      
      return {
        'name': criterion['name'],
        'description': criterion['description'],
        'score': score.clamp(0, 100),
        'violations': violations,
        'threshold': criterion['threshold'],
      };
    } catch (e) {
      print('Error evaluating criterion ${criterion['name']}: $e');
      return {
        'name': criterion['name'],
        'description': criterion['description'],
        'score': 0,
        'violations': ['Error evaluating criterion'],
        'threshold': criterion['threshold'],
      };
    }
  }

  /// Check if an ingredient is a common allergen
  static bool _isAllergen(String ingredient) {
    final allergens = [
      'milk', 'eggs', 'fish', 'shellfish', 'tree nuts', 'peanuts',
      'wheat', 'soy', 'sesame', 'gluten', 'lactose',
    ];
    return allergens.any((allergen) => 
      ingredient.toLowerCase().contains(allergen.toLowerCase()));
  }

  /// Check if an ingredient is an artificial color
  static bool _isArtificialColor(String ingredient) {
    final colors = [
      'red 40', 'yellow 5', 'yellow 6', 'blue 1', 'blue 2',
      'green 3', 'red 3', 'caramel color', 'titanium dioxide',
    ];
    return colors.any((color) => 
      ingredient.toLowerCase().contains(color.toLowerCase()));
  }

  /// Check if an ingredient is an artificial preservative
  static bool _isArtificialPreservative(String ingredient) {
    final preservatives = [
      'bha', 'bht', 'sodium benzoate', 'potassium sorbate',
      'sodium nitrite', 'sodium nitrate', 'sulfites',
    ];
    return preservatives.any((preservative) => 
      ingredient.toLowerCase().contains(preservative.toLowerCase()));
  }

  /// Check if an ingredient is an artificial sweetener
  static bool _isArtificialSweetener(String ingredient) {
    final sweeteners = [
      'aspartame', 'sucralose', 'saccharin', 'acesulfame k',
      'neotame', 'advantame', 'sugar alcohols',
    ];
    return sweeteners.any((sweetener) => 
      ingredient.toLowerCase().contains(sweetener.toLowerCase()));
  }

  /// Check if an ingredient is likely GMO
  static bool _isGmoIngredient(String ingredient) {
    final gmoIngredients = [
      'corn', 'soy', 'canola', 'sugar beet', 'cottonseed',
      'papaya', 'squash', 'alfalfa', 'potato', 'apple',
    ];
    return gmoIngredients.any((gmo) => 
      ingredient.toLowerCase().contains(gmo.toLowerCase()));
  }

  /// Check if an ingredient is high fructose corn syrup
  static bool _isHighFructoseCornSyrup(String ingredient) {
    return ingredient.toLowerCase().contains('high fructose corn syrup') ||
           ingredient.toLowerCase().contains('hfcs');
  }

  /// Check if an ingredient is a trans fat
  static bool _isTransFat(String ingredient) {
    return ingredient.toLowerCase().contains('trans fat') ||
           ingredient.toLowerCase().contains('partially hydrogenated');
  }

  /// Get sodium level from ingredients
  static int _getSodiumLevel(List<Map<String, dynamic>> ingredients) {
    for (final ingredient in ingredients) {
      if (ingredient['name'].toString().toLowerCase().contains('sodium')) {
        final sodium = ingredient['amount'] as int? ?? 0;
        return sodium;
      }
    }
    return 0;
  }
} 