import 'package:flutter/material.dart';

class AppTheme {
  // SafeScan Color Scheme - Improved for better contrast and accessibility
  static const Color primaryBlue = Color(0xFF1976D2); // Darker blue for better contrast
  static const Color lightBlue = Color(0xFFE3F2FD); // Very Light Blue
  static const Color darkBlue = Color(0xFF0D47A1); // Darker Blue for text contrast
  static const Color successBlue = Color(0xFF1976D2); // Same as primaryBlue
  static const Color white = Color(0xFFFFFFFF);
  static const Color offWhite = Color(0xFFFAFAFA); // Lighter background
  static const Color greyText = Color(0xFF212121); // Darker grey for better visibility (WCAG AA compliant)
  static const Color lightGrey = Color(0xFF757575); // Medium grey for secondary text (WCAG AA compliant)
  static const Color warningOrange = Color(0xFFE65100); // Darker orange for better contrast
  static const Color dangerRed = Color(0xFFD32F2F); // Darker red for better contrast
  
  // High contrast colors for accessibility
  static const Color highContrastText = Color(0xFF000000); // Pure black for maximum contrast
  static const Color highContrastBackground = Color(0xFFFFFFFF); // Pure white
  static const Color mediumContrastText = Color(0xFF424242); // Dark grey for good readability
  
  // Backward compatibility aliases - for references that haven't been updated yet
  static const Color primaryGreen = primaryBlue; // Alias for primaryBlue to maintain compatibility
  static const Color successGreen = successBlue; // Alias for successBlue to maintain compatibility

  static ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    
    // Color Scheme with consistent blue palette and improved contrast
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.light,
      primary: primaryBlue,
      secondary: Color(0xFF42A5F5), // Lighter blue as secondary
      tertiary: lightBlue,
      surface: white,
      background: offWhite,
      onPrimary: white,
      onSecondary: white,
      onSurface: highContrastText,
      onBackground: highContrastText,
      error: dangerRed,
      onError: white,
    ),

    // App Bar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryBlue,
      foregroundColor: white,
      elevation: 2, // Increased elevation for better visibility
      centerTitle: false,
      titleTextStyle: TextStyle(
        color: white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: IconThemeData(color: white),
    ),

    // Scaffold Background
    scaffoldBackgroundColor: offWhite,

    // Card Theme with better shadows and contrast
    cardTheme: CardThemeData(
      color: white,
      elevation: 3, // Increased elevation for better visibility
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      shadowColor: Colors.black.withOpacity(0.15), // Increased shadow opacity
    ),

    // Button Themes with improved contrast
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryBlue,
        foregroundColor: white,
        elevation: 3, // Increased elevation
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryBlue,
        side: const BorderSide(color: primaryBlue, width: 2),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryBlue,
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    // Input Decoration Theme with improved contrast
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: white,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: lightGrey, width: 1.5), // Increased border width
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryBlue, width: 2.5), // Increased focused border width
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: lightGrey, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: dangerRed, width: 2), // Increased error border width
      ),
      labelStyle: TextStyle(color: greyText, fontWeight: FontWeight.w500), // Added font weight
      hintStyle: TextStyle(color: lightGrey, fontSize: 14), // Adjusted hint style
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),

    // Bottom Navigation Bar Theme with improved contrast
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: white,
      selectedItemColor: primaryBlue,
      unselectedItemColor: lightGrey,
      elevation: 8, // Increased elevation
      type: BottomNavigationBarType.fixed,
    ),

    // Text Theme with better contrast and accessibility
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        color: highContrastText,
        fontSize: 32,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        color: highContrastText,
        fontSize: 28,
        fontWeight: FontWeight.w600,
      ),
      headlineSmall: TextStyle(
        color: highContrastText,
        fontSize: 24,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: TextStyle(
        color: highContrastText,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      titleMedium: TextStyle(
        color: highContrastText,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
      titleSmall: TextStyle(
        color: highContrastText,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: TextStyle(
        color: greyText,
        fontSize: 16,
        fontWeight: FontWeight.normal,
      ),
      bodyMedium: TextStyle(
        color: greyText,
        fontSize: 14,
        fontWeight: FontWeight.normal,
      ),
      bodySmall: TextStyle(
        color: lightGrey,
        fontSize: 12,
        fontWeight: FontWeight.normal,
      ),
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: primaryBlue,
      foregroundColor: white,
      elevation: 6, // Increased elevation
    ),

    // Progress Indicator Theme
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: primaryBlue,
      linearTrackColor: lightBlue,
      circularTrackColor: lightBlue,
    ),

    // Divider Theme
    dividerTheme: DividerThemeData(
      color: lightBlue,
      thickness: 1.5, // Increased thickness
      space: 16,
    ),
  );

  // Dark theme with improved contrast
  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    
    // Color Scheme with consistent blue palette and improved contrast
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.dark,
      primary: primaryBlue,
      secondary: Color(0xFF64B5F6), // Lighter blue as secondary
      tertiary: lightBlue,
      surface: Color(0xFF1E1E1E),
      background: Color(0xFF121212),
      onPrimary: white,
      onSecondary: white,
      onSurface: white,
      onBackground: white,
      error: dangerRed,
      onError: white,
    ),

    // App Bar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: Color(0xFF1E1E1E),
      foregroundColor: white,
      elevation: 4, // Increased elevation
      centerTitle: false,
      titleTextStyle: TextStyle(
        color: white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: IconThemeData(color: white),
    ),

    // Scaffold Background
    scaffoldBackgroundColor: Color(0xFF121212),

    // Card Theme with better shadows and contrast
    cardTheme: CardThemeData(
      color: Color(0xFF1E1E1E),
      elevation: 4, // Increased elevation
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      shadowColor: Colors.black.withOpacity(0.4), // Increased shadow opacity
    ),

    // Button Themes with improved contrast
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryBlue,
        foregroundColor: white,
        elevation: 4, // Increased elevation
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryBlue,
        side: const BorderSide(color: primaryBlue, width: 2),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryBlue,
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    // Input Decoration Theme with improved contrast
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Color(0xFF2C2C2C),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey[600]!, width: 1.5), // Increased border width
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: primaryBlue, width: 2.5), // Increased focused border width
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey[600]!, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: dangerRed, width: 2), // Increased error border width
      ),
      labelStyle: TextStyle(color: Colors.grey[200], fontWeight: FontWeight.w500), // Added font weight
      hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14), // Adjusted hint style
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),

    // Bottom Navigation Bar Theme with improved contrast
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Color(0xFF1E1E1E),
      selectedItemColor: primaryBlue,
      unselectedItemColor: Colors.grey,
      elevation: 8, // Increased elevation
      type: BottomNavigationBarType.fixed,
    ),

    // Text Theme with better contrast and accessibility
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        color: white,
        fontSize: 32,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        color: white,
        fontSize: 28,
        fontWeight: FontWeight.w600,
      ),
      headlineSmall: TextStyle(
        color: white,
        fontSize: 24,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: TextStyle(
        color: white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      titleMedium: TextStyle(
        color: white,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
      titleSmall: TextStyle(
        color: white,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: TextStyle(
        color: white,
        fontSize: 16,
        fontWeight: FontWeight.normal,
      ),
      bodyMedium: TextStyle(
        color: white,
        fontSize: 14,
        fontWeight: FontWeight.normal,
      ),
      bodySmall: TextStyle(
        color: Colors.grey,
        fontSize: 12,
        fontWeight: FontWeight.normal,
      ),
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: primaryBlue,
      foregroundColor: white,
      elevation: 6, // Increased elevation
    ),

    // Progress Indicator Theme
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: primaryBlue,
      linearTrackColor: Color(0xFF2C2C2C),
      circularTrackColor: Color(0xFF2C2C2C),
    ),

    // Divider Theme
    dividerTheme: DividerThemeData(
      color: Colors.grey[700],
      thickness: 1.5, // Increased thickness
      space: 16,
    ),
  );

  // Safety Status Colors with improved contrast
  static const Color safeBlue = primaryBlue;
  static const Color warningYellow = warningOrange;
  static const Color dangerRedStatus = dangerRed;
  static const Color unknownGray = lightGrey;

  static Color getSafetyColor(String status) {
    switch (status.toLowerCase()) {
      case 'safe':
        return safeBlue;
      case 'warning':
        return warningYellow;
      case 'danger':
        return dangerRedStatus;
      default:
        return unknownGray;
    }
  }

  static IconData getSafetyIcon(String status) {
    switch (status.toLowerCase()) {
      case 'safe':
        return Icons.check_circle;
      case 'warning':
        return Icons.warning;
      case 'danger':
        return Icons.error;
      default:
        return Icons.help_outline;
    }
  }
  
  // Safety rating colors for ingredients with improved contrast
  static Color getIngredientSafetyColor(int rating) {
    switch (rating) {
      case 1:
        return dangerRed;
      case 2:
        return warningOrange;
      case 3:
        return Color(0xFFF57C00); // Darker orange for better contrast
      case 4:
        return Color(0xFF1976D2); // Darker blue for better contrast
      case 5:
        return Color(0xFF388E3C); // Darker green for better contrast
      default:
        return lightGrey;
    }
  }

  // Helper method to calculate contrast ratio (simplified)
  static double getContrastRatio(Color foreground, Color background) {
    // Simplified contrast ratio calculation
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    
    final brightest = luminance1 > luminance2 ? luminance1 : luminance2;
    final darkest = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (brightest + 0.05) / (darkest + 0.05);
  }

  // Helper method to check if contrast meets WCAG AA standards
  static bool meetsWCAGAA(Color foreground, Color background) {
    final ratio = getContrastRatio(foreground, background);
    return ratio >= 4.5; // WCAG AA standard for normal text
  }

  // Helper method to check if contrast meets WCAG AAA standards
  static bool meetsWCAGAAA(Color foreground, Color background) {
    final ratio = getContrastRatio(foreground, background);
    return ratio >= 7.0; // WCAG AAA standard for normal text
  }
} 