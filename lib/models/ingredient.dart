class Ingredient {
  final String id;
  final String name;
  final String? category;
  final String? description;
  final String? scientificName;
  final List<String> commonAliases;
  final List<String> healthRisks;
  final String safetyLevel; // 'green', 'yellow', 'red'
  final int? safetyRating;  // 1-5 rating
  final String? concernLevel; // 'High', 'Medium', 'Low'
  final DateTime createdAt;
  final DateTime updatedAt;

  Ingredient({
    required this.id,
    required this.name,
    this.category,
    this.description,
    this.scientificName,
    this.commonAliases = const [],
    this.healthRisks = const [],
    required this.safetyLevel,
    this.safetyRating,
    this.concernLevel,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    // Handle common_aliases which could be a string, list, or null
    List<String> aliases = [];
    if (json['common_aliases'] != null) {
      if (json['common_aliases'] is List) {
        aliases = List<String>.from(json['common_aliases'] as List);
      } else if (json['common_aliases'] is String) {
        aliases = [(json['common_aliases'] as String)];
      }
    }
    
    // Handle health_risks which could be a string, list, or null
    List<String> risks = [];
    if (json['health_risks'] != null) {
      if (json['health_risks'] is List) {
        risks = List<String>.from(json['health_risks'] as List);
      } else if (json['health_risks'] is String) {
        risks = [(json['health_risks'] as String)];
      }
    }
    
    return Ingredient(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String?,
      description: json['description'] as String?,
      scientificName: json['scientific_name'] as String?,
      commonAliases: aliases,
      healthRisks: risks,
      safetyLevel: json['safety_level'] as String,
      safetyRating: json['safety_rating'] as int?,
      concernLevel: json['concern_level'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'scientific_name': scientificName,
      'common_aliases': commonAliases,
      'health_risks': healthRisks,
      'safety_level': safetyLevel,
      'safety_rating': safetyRating,
      'concern_level': concernLevel,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper methods for safety assessment
  bool get isSafe => safetyLevel == 'green';
  bool get isCaution => safetyLevel == 'yellow';
  bool get isUnsafe => safetyLevel == 'red';

  // Get safety score based on level
  int get safetyScore {
    switch (safetyLevel) {
      case 'green':
        return 90;
      case 'yellow':
        return 60;
      case 'red':
        return 20;
      default:
        return 50;
    }
  }
}

class AnalyzedIngredient {
  final String name;
  final String? description;
  final String safetyLevel;
  final int? safetyRating;
  final String? concernLevel;
  final List<String> healthRisks;
  final List<String> aliases;
  final String? category;
  final bool isMatched;
  final double confidence;

  AnalyzedIngredient({
    required this.name,
    this.description,
    required this.safetyLevel,
    this.safetyRating,
    this.concernLevel,
    this.healthRisks = const [],
    this.aliases = const [],
    this.category,
    this.isMatched = false,
    this.confidence = 0.0,
  });

  factory AnalyzedIngredient.fromJson(Map<String, dynamic> json) {
    // Handle health_risks which could be a string, list, or null
    List<String> risks = [];
    if (json['health_risks'] != null) {
      if (json['health_risks'] is List) {
        risks = List<String>.from(json['health_risks'] as List);
      } else if (json['health_risks'] is String) {
        risks = [(json['health_risks'] as String)];
      }
    }
    
    // Handle aliases which could be a string, list, or null
    List<String> aliases = [];
    if (json['aliases'] != null) {
      if (json['aliases'] is List) {
        aliases = List<String>.from(json['aliases'] as List);
      } else if (json['aliases'] is String) {
        aliases = [(json['aliases'] as String)];
      }
    }
    
    return AnalyzedIngredient(
      name: json['name'] as String,
      description: json['description'] as String?,
      safetyLevel: json['safety_level'] as String,
      safetyRating: json['safety_rating'] as int?,
      concernLevel: json['concern_level'] as String?,
      healthRisks: risks,
      aliases: aliases,
      category: json['category'] as String?,
      isMatched: json['is_matched'] as bool? ?? false,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'safety_level': safetyLevel,
      'safety_rating': safetyRating,
      'concern_level': concernLevel,
      'health_risks': healthRisks,
      'aliases': aliases,
      'category': category,
      'is_matched': isMatched,
      'confidence': confidence,
    };
  }

  // Helper methods
  bool get isSafe => safetyLevel == 'green';
  bool get isCaution => safetyLevel == 'yellow';
  bool get isUnsafe => safetyLevel == 'red';

  int get safetyScore {
    switch (safetyLevel) {
      case 'green':
        return 90;
      case 'yellow':
        return 60;
      case 'red':
        return 20;
      default:
        return 50;
    }
  }
} 

extension AnalyzedIngredientStatus on AnalyzedIngredient {
  String get status {
    switch (safetyLevel) {
      case 'green': return 'safe';
      case 'yellow': return 'caution';
      case 'red': return 'danger';
      default: return 'unknown';
    }
  }
} 