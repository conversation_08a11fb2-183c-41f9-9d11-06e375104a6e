class User {
  final String id;
  final String email;
  final String? name;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime? lastSignInAt;

  User({
    required this.id,
    required this.email,
    this.name,
    this.avatarUrl,
    required this.createdAt,
    this.lastSignInAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['user_metadata']?['name'] as String?,
      avatarUrl: json['user_metadata']?['avatar_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastSignInAt: json['last_sign_in_at'] != null 
          ? DateTime.parse(json['last_sign_in_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'user_metadata': {
        'name': name,
        'avatar_url': avatarUrl,
      },
      'created_at': createdAt.toIso8601String(),
      'last_sign_in_at': lastSignInAt?.toIso8601String(),
    };
  }
}

class UserPreferences {
  final String id;
  final String userId;
  final List<String> allergens;
  final List<String> avoidedIngredients;
  final bool isPremium;
  final DateTime? lastLogin;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserPreferences({
    required this.id,
    required this.userId,
    this.allergens = const [],
    this.avoidedIngredients = const [],
    this.isPremium = false,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      allergens: json['allergens'] != null 
          ? List<String>.from(json['allergens'] as List)
          : [],
      avoidedIngredients: json['avoided_ingredients'] != null 
          ? List<String>.from(json['avoided_ingredients'] as List)
          : [],
      isPremium: json['is_premium'] as bool? ?? false,
      lastLogin: json['last_login'] != null 
          ? DateTime.parse(json['last_login'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'allergens': allergens,
      'avoided_ingredients': avoidedIngredients,
      'is_premium': isPremium,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserPreferences copyWith({
    String? id,
    String? userId,
    List<String>? allergens,
    List<String>? avoidedIngredients,
    bool? isPremium,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserPreferences(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      allergens: allergens ?? this.allergens,
      avoidedIngredients: avoidedIngredients ?? this.avoidedIngredients,
      isPremium: isPremium ?? this.isPremium,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class Allergen {
  final String id;
  final String name;
  final String? description;
  final String severity;
  final DateTime createdAt;

  Allergen({
    required this.id,
    required this.name,
    this.description,
    this.severity = 'medium',
    required this.createdAt,
  });

  factory Allergen.fromJson(Map<String, dynamic> json) {
    return Allergen(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      severity: json['severity'] as String? ?? 'medium',
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'severity': severity,
      'created_at': createdAt.toIso8601String(),
    };
  }
} 