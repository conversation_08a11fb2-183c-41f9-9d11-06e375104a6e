class Alternative {
  final String id;
  final String name;
  final String? brand;
  final String? description;
  final int? safetyScore;
  final String? safetyRating;
  final String? imageUrl;
  final double? price;
  final String? availability;
  final List<String> reasons;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Alternative({
    required this.id,
    required this.name,
    this.brand,
    this.description,
    this.safetyScore,
    this.safetyRating,
    this.imageUrl,
    this.price,
    this.availability,
    this.reasons = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory Alternative.fromJson(Map<String, dynamic> json) {
    return Alternative(
      id: json['id'] as String,
      name: json['name'] as String,
      brand: json['brand'] as String?,
      description: json['description'] as String?,
      safetyScore: json['safety_score'] as int?,
      safetyRating: json['safety_rating'] as String?,
      imageUrl: json['image_url'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      availability: json['availability'] as String?,
      reasons: (json['reasons'] as List?)?.cast<String>() ?? [],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'description': description,
      'safety_score': safetyScore,
      'safety_rating': safetyRating,
      'image_url': imageUrl,
      'price': price,
      'availability': availability,
      'reasons': reasons,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Alternative copyWith({
    String? id,
    String? name,
    String? brand,
    String? description,
    int? safetyScore,
    String? safetyRating,
    String? imageUrl,
    double? price,
    String? availability,
    List<String>? reasons,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Alternative(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      description: description ?? this.description,
      safetyScore: safetyScore ?? this.safetyScore,
      safetyRating: safetyRating ?? this.safetyRating,
      imageUrl: imageUrl ?? this.imageUrl,
      price: price ?? this.price,
      availability: availability ?? this.availability,
      reasons: reasons ?? this.reasons,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Alternative && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Alternative(id: $id, name: $name, brand: $brand, safetyScore: $safetyScore)';
  }
}
