/// Model representing the result of a product safety analysis
class SafetyAnalysisResult {
  /// The product ID
  final String productId;
  
  /// The product barcode
  final String? barcode;
  
  /// The product name
  final String productName;
  
  /// The product brand
  final String? brand;
  
  /// Overall safety score (0-100)
  final int overallScore;
  
  /// Safety rating (excellent, good, fair, poor, unsafe)
  final String safetyRating;
  
  /// Detailed results for each safety criteria
  final Map<String, dynamic> criteriaResults;
  
  /// Ingredients that violate safety criteria, grouped by criteria
  final Map<String, List<String>> violatingIngredients;
  
  /// The complete analysis data
  final Map<String, dynamic> analysis;
  
  /// Request ID for tracking
  final String requestId;
  
  /// User ID associated with the analysis
  final String? userId;
  
  /// Timestamp of the analysis
  final DateTime timestamp;

  SafetyAnalysisResult({
    required this.productId,
    this.barcode,
    required this.productName,
    this.brand,
    required this.overallScore,
    required this.safetyRating,
    required this.criteriaResults,
    required this.violatingIngredients,
    required this.analysis,
    required this.requestId,
    this.userId,
    DateTime? timestamp,
  }) : this.timestamp = timestamp ?? DateTime.now();
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'barcode': barcode,
      'product_name': productName,
      'brand': brand,
      'overall_score': overallScore,
      'safety_rating': safetyRating,
      'criteria_results': criteriaResults,
      'violating_ingredients': violatingIngredients,
      'analysis': analysis,
      'request_id': requestId,
      'user_id': userId,
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  /// Create from JSON
  factory SafetyAnalysisResult.fromJson(Map<String, dynamic> json) {
    return SafetyAnalysisResult(
      productId: json['product_id'] as String,
      barcode: json['barcode'] as String?,
      productName: json['product_name'] as String,
      brand: json['brand'] as String?,
      overallScore: json['overall_score'] as int,
      safetyRating: json['safety_rating'] as String,
      criteriaResults: Map<String, dynamic>.from(json['criteria_results'] as Map),
      violatingIngredients: Map<String, List<String>>.from(
        json['violating_ingredients'] as Map,
      ),
      analysis: json['analysis'] as Map<String, dynamic>,
      requestId: json['request_id'] as String,
      userId: json['user_id'] as String?,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : DateTime.now(),
    );
  }
  
  /// Get a human-readable summary of the safety analysis
  String getSummary() {
    final concernsList = violatingIngredients.entries
        .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
        .toList();
    
    return '''
Safety Score: $overallScore/100
Rating: ${safetyRating.toUpperCase()}
${concernsList.isNotEmpty ? 'Concerns:\n${concernsList.join('\n')}' : 'No significant concerns detected'}
''';
  }
} 