import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'services/supabase_init_service.dart';
import 'services/supabase_service.dart';
// Database verification functionality now integrated into enhanced_safescan_service.dart

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  try {
    await dotenv.load(fileName: ".env");
    print("Environment variables loaded successfully");
  } catch (e) {
    print("Error loading .env file: $e");
    print("Using default fallback values for environment variables");
  }
  
  // Initialize Supabase MCP server
  try {
    final initialized = await SupabaseInitService.initialize();
    if (initialized) {
      print("✅ Supabase MCP server connected successfully");
      
      // Verify database tables
      await _verifyDatabaseTables();
      
      // Test MCP protocol calls
      await _testMCPProtocol();
      
      // Perform database verification
      await _verifyDatabase();
    } else {
      print("❌ Failed to connect to Supabase MCP server");
    }
  } catch (e) {
    print("❌ Error initializing Supabase: $e");
  }
}

/// Verify required database tables
Future<void> _verifyDatabaseTables() async {
  print("\n🔍 VERIFYING DATABASE TABLES...");
  
  try {
    // Check all required tables
    final tablesToCheck = [
      'products',
      'ingredients',
      'product_ingredients',
      'saved_products',
      'user_preferences',
      'allergens',
      'dietary_preferences',
      'avoided_ingredients'
    ];
    
    final tableStatus = await SupabaseInitService.checkTables(tablesToCheck);
    
    print("TABLE STATUS:");
    tableStatus.forEach((table, exists) {
      print("  ${exists ? '✅' : '❌'} $table");
    });
    
    // Check if all required tables exist
    final requiredTables = ['products', 'ingredients', 'product_ingredients', 'saved_products', 'user_preferences'];
    final allRequiredExist = requiredTables.every((table) => tableStatus[table] == true);
    
    if (allRequiredExist) {
      print("✅ All required tables exist. MCP protocol can function properly.");
    } else {
      print("⚠️ Some required tables are missing. MCP protocol may be limited.");
    }
  } catch (e) {
    print("❌ Error verifying database tables: $e");
  }
}

/// Test MCP protocol calls
Future<void> _testMCPProtocol() async {
  print("\n🧪 TESTING MCP PROTOCOL CALLS...");
  
  // Create SupabaseService instance for all tests
  final supabaseService = SupabaseService(SupabaseInitService.client);
  
  try {
    // Test 1: Get ingredients
    print("\n📋 TEST 1: Fetching ingredients...");
    try {
      final ingredients = await supabaseService.getAllIngredients();
      print("  ✅ Successfully fetched ${ingredients.length} ingredients");
      
      if (ingredients.isNotEmpty) {
        print("  📝 Sample ingredient: ${ingredients.first.name} (${ingredients.first.safetyLevel})");
      }
    } catch (e) {
      print("  ❌ Failed to fetch ingredients: $e");
    }
    
    // Test 2: Try user authentication (anonymous mode)
    print("\n🔐 TEST 2: Testing anonymous session...");
    try {
      final currentUser = supabaseService.currentUser;
      if (currentUser != null) {
        print("  ✅ Existing session detected");
        print("  📝 User ID: ${currentUser.id}");
      } else {
        print("  ℹ️ No active session (anonymous mode)");
      }
    } catch (e) {
      print("  ❌ Error checking session: $e");
    }
    
    // Test 3: Test ingredient search
    print("\n🔍 TEST 3: Testing ingredient search...");
    try {
      final searchTerms = ["sugar", "sodium benzoate", "vitamin"];
      
      for (final term in searchTerms) {
        final results = await supabaseService.searchIngredients(term);
        print("  ${results.isNotEmpty ? '✅' : '❌'} Search for '$term': ${results.length} results");
      }
    } catch (e) {
      print("  ❌ Error during ingredient search: $e");
    }
    
    // Test 4: Test table health checks
    print("\n🏥 TEST 4: Testing database health...");
    try {
      final isHealthy = await DatabaseVerificationService.quickHealthCheck();
      print("  ${isHealthy ? '✅' : '❌'} Database health check: ${isHealthy ? 'GOOD' : 'FAILED'}");
    } catch (e) {
      print("  ❌ Error during health check: $e");
    }
  } catch (e) {
    print("❌ Error during MCP protocol tests: $e");
  }
}

/// Verify database state
Future<void> _verifyDatabase() async {
  print("\n📊 PERFORMING DATABASE VERIFICATION...");
  
  try {
    final verificationResult = await DatabaseVerificationService.verifyIngredientDatabase();
    
    print("VERIFICATION RESULT:");
    print("  📌 Status: ${verificationResult['status']}");
    print("  📌 Total ingredients: ${verificationResult['total_ingredients']}");
    print("  📌 Database connected: ${verificationResult['database_connected']}");
    print("  📌 Test searches passed: ${verificationResult['test_searches_passed']}/${verificationResult['test_searches_total']}");
    
    print("\nRECOMMENDATIONS:");
    for (final rec in verificationResult['recommendations'] as List) {
      print("  🔹 $rec");
    }
  } catch (e) {
    print("❌ Error during database verification: $e");
  }
} 