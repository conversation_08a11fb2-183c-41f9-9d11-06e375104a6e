import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

// Simple script to check Supabase connection without Flutter dependencies
Future<void> main() async {
  print('🔍 SafeScan MCP Supabase Connection Test');
  print('======================================\n');
  
  try {
    // Load environment variables
    try {
      await dotenv.load(fileName: ".env");
      print("✅ Environment variables loaded successfully");
    } catch (e) {
      print("⚠️ Using default Supabase configuration values");
    }
    
    // Get Supabase URL and key from environment variables or use defaults
    final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 'https://lwnfzmvqahraputvfkos.supabase.co';
    final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao';
    
    print('🌐 Supabase URL: $supabaseUrl');
    
    // Test connection
    print('\n🔄 Testing connection to Supabase...');
    final connectionResult = await testConnection(supabaseUrl, supabaseKey);
    if (!connectionResult) {
      print('❌ Connection to Supabase failed. Check your credentials.');
      exit(1);
    }
    print('✅ Connection successful');
    
    // Check required tables
    final requiredTables = [
      'products',
      'ingredients', 
      'product_ingredients',
      'saved_products',
      'user_preferences',
      'ai_analysis_cache',
      'scan_events',
      'product_analysis_fallback',
      'pending_ingredients',
      'user_feedback'
    ];
    
    print('\n📋 Checking tables:');
    for (final table in requiredTables) {
      final exists = await checkTableExists(supabaseUrl, supabaseKey, table);
      print('  ${exists ? '✅' : '❌'} $table');
    }
    
    // Check if the MCP tables exist (which would be from our SQL migration)
    print('\n📋 Checking MCP-specific tables:');
    final mcpTables = ['ai_analysis_cache', 'scan_events', 'product_analysis_fallback'];
    int mcpTableCount = 0;
    
    for (final table in mcpTables) {
      final exists = await checkTableExists(supabaseUrl, supabaseKey, table);
      if (exists) mcpTableCount++;
    }
    
    print('\n🧮 MCP tables status: $mcpTableCount/${mcpTables.length} exist');
    
    if (mcpTableCount < mcpTables.length) {
      print('\n⚠️ Not all MCP tables exist. Run the SQL migration to create them:');
      print('1. Go to the Supabase dashboard');
      print('2. Open the SQL editor');
      print('3. Run the SQL from supabase_migration.sql');
    } else {
      print('\n✅ All MCP tables are properly set up!');
    }
    
    // Test data retrieval
    print('\n🔍 Testing data retrieval from ingredients table...');
    final ingredientsData = await getIngredients(supabaseUrl, supabaseKey);
    print('Found ${ingredientsData.length} ingredients in the database');
    
    if (ingredientsData.isNotEmpty) {
      print('\nSample ingredient data:');
      print(ingredientsData[0]);
    }
    
    print('\n✅ MCP Supabase test completed successfully!');
    
  } catch (e) {
    print('❌ Error during MCP test: $e');
    exit(1);
  }
}

/// Test connection to Supabase
Future<bool> testConnection(String supabaseUrl, String supabaseKey) async {
  try {
    final url = Uri.parse('$supabaseUrl/rest/v1/');
    final response = await http.get(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
      },
    );
    
    return response.statusCode == 200;
  } catch (e) {
    print('Error testing connection: $e');
    return false;
  }
}

/// Check if a table exists
Future<bool> checkTableExists(String supabaseUrl, String supabaseKey, String tableName) async {
  try {
    final url = Uri.parse('$supabaseUrl/rest/v1/$tableName?limit=0');
    final response = await http.get(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
      },
    );
    
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}

/// Get ingredients data
Future<List<Map<String, dynamic>>> getIngredients(String supabaseUrl, String supabaseKey) async {
  try {
    final url = Uri.parse('$supabaseUrl/rest/v1/ingredients?limit=5');
    final response = await http.get(
      url,
      headers: {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
        'Content-Type': 'application/json',
      },
    );
    
    if (response.statusCode == 200) {
      final List<dynamic> data = Uri.decodeFull(response.body) == '[]' 
          ? [] 
          : List<dynamic>.from(Uri.decodeFull(response.body));
      
      return data.map((item) => Map<String, dynamic>.from(item)).toList();
    }
    return [];
  } catch (e) {
    print('Error retrieving ingredients: $e');
    return [];
  }
} 