import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/supabase_init_service.dart';
import '../services/supabase_service.dart';

// Enhanced provider with initialization check
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  if (!SupabaseInitService.isInitialized) {
    throw Exception('Supabase not initialized. Call SupabaseInitService.initialize() first.');
  }
  return SupabaseInitService.client;
});

final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return SupabaseService(client);
});

// Add initialization status provider
final supabaseInitializationProvider = FutureProvider<bool>((ref) async {
  return await SupabaseInitService.initialize();
});

// Add MCP tables status provider
final mcpTablesProvider = FutureProvider<bool>((ref) async {
  if (!SupabaseInitService.isInitialized) {
    await SupabaseInitService.initialize();
  }
  return await SupabaseInitService.checkMcpTables();
});
