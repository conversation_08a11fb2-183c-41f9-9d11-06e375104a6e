import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/enhanced_safescan_service.dart';
import 'supabase_provider.dart';

/// Provider for the enhanced SafeScan service
final enhancedSafeScanServiceProvider = Provider<EnhancedSafeScanService>((ref) {
  final supabaseService = ref.read(supabaseServiceProvider);
  return EnhancedSafeScanService(supabaseService);
});

/// Provider for product analysis using the enhanced service
final enhancedProductAnalysisProvider = FutureProvider.family<Map<String, dynamic>?, String>((ref, barcode) async {
  final service = ref.read(enhancedSafeScanServiceProvider);
  
  try {
    final product = await service.analyzeProductByBarcode(barcode);
    
    if (product != null) {
      return {
        'success': true,
        'product': product,
        'recommendation': product.recommendation,
        'safety_score': product.safetyScore,
      };
    } else {
      return {
        'success': false,
        'error': 'Product analysis failed - insufficient data',
        'requires_user_input': true,
      };
    }
  } catch (e) {
    return {
      'success': false,
      'error': e.toString(),
      'requires_user_input': true,
    };
  }
}); 