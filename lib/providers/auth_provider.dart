import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user.dart' as app_user;
import '../services/supabase_service.dart';
import 'supabase_provider.dart';

// Auth state provider
final authStateProvider = StreamProvider<AuthState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return supabaseService.authStateChanges;
});

// Current user provider
final currentUserProvider = Provider<app_user.User?>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => supabaseService.currentUser,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Auth controller
class AuthController extends StateNotifier<AsyncValue<void>> {
  final SupabaseService supabaseService;
  AuthController(this.supabaseService) : super(const AsyncValue.data(null));

  Future<void> signUp({
    required String email,
    required String password,
    String? name,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await supabaseService.signUp(
        email: email,
        password: password,
        name: name,
      );
      
      if (response.user != null) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error(
          response.session?.user?.emailConfirmedAt == null
              ? 'Please check your email to confirm your account'
              : 'Sign up failed',
          StackTrace.current,
        );
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    state = const AsyncValue.loading();
    try {
      final response = await supabaseService.signIn(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error('Sign in failed', StackTrace.current);
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> signOut() async {
    state = const AsyncValue.loading();
    try {
      await supabaseService.signOut();
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> resetPassword(String email) async {
    state = const AsyncValue.loading();
    try {
      await Supabase.instance.client.auth.resetPasswordForEmail(email);
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Auth controller provider
final authControllerProvider = StateNotifierProvider<AuthController, AsyncValue<void>>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return AuthController(supabaseService);
});

// Helper provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});

// Helper provider to get user ID
final userIdProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.id;
}); 