import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Import our services
import 'services/gemini_service.dart';
import 'services/supabase_service.dart';
import 'services/supabase_init_service.dart';
// import 'services/safety_analysis_service.dart'; // Commented out - using enhanced service instead
import 'models/product.dart';
// import 'lib/services/logger_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Load environment variables
  try {
    await dotenv.load(fileName: ".env");
    // LoggerService.info("Environment variables loaded successfully");
  } catch (e) {
    // LoggerService.warning("Error loading .env file: $e");
    // LoggerService.warning("Using default fallback values");
  }
  
  // LoggerService.info("\n===== SAFESCAN API DIAGNOSTIC TOOL =====");
  // LoggerService.info("Testing critical services for product safety analysis");
  // LoggerService.info("===========================================\n");
  
  // Initialize Supabase
  try {
    // LoggerService.info("1. INITIALIZING SUPABASE CONNECTION");
    await SupabaseInitService.initialize();
    // LoggerService.info("✅ Supabase initialized successfully");
  } catch (e) {
    // LoggerService.error("❌ Supabase initialization failed: $e");
    return; // Exit if Supabase fails to initialize
  }
  
  // Define a test barcode (Beyond Good 70% Dark Chocolate)
  const testBarcode = "898575001337";
  // LoggerService.info("\n2. TESTING OPENFOODFACTS API");
  // LoggerService.info("Using test barcode: $testBarcode");
  
  // Test OpenFoodFacts API
  try {
    final openFoodFactsBaseUrl = dotenv.env['OPENFOODFACTS_API_URL'] ?? 'https://world.openfoodfacts.org/api/v0/product';
    final openFoodFactsUserAgent = dotenv.env['OPENFOODFACTS_USER_AGENT'] ?? 'SafeScan - Product Safety App';
    
    // LoggerService.info("Connecting to OpenFoodFacts API: $openFoodFactsBaseUrl");
    final apiUrl = '$openFoodFactsBaseUrl/$testBarcode.json';
    
    final response = await http.get(
      Uri.parse(apiUrl),
      headers: {'User-Agent': openFoodFactsUserAgent},
    ).timeout(const Duration(seconds: 10));
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['status'] == 1) {
        // LoggerService.info("✅ OpenFoodFacts API connection successful");
        // LoggerService.info("Product found: ${data['product']['product_name']}");
        // LoggerService.info("Brand: ${data['product']['brands']}");
        
        // Display ingredients if available
        if (data['product']['ingredients_text'] != null) {
          // LoggerService.info("Ingredients: ${data['product']['ingredients_text']}");
        } else {
          // LoggerService.info("No ingredients information available");
        }
      } else {
        // LoggerService.warning("⚠️ OpenFoodFacts API returned status ${data['status']} - product not found");
      }
    } else {
      // LoggerService.error("❌ OpenFoodFacts API returned error: ${response.statusCode}");
    }
  } catch (e) {
    // LoggerService.error("❌ OpenFoodFacts API test failed: $e");
  }
  
  // Test Gemini AI API
  // LoggerService.info("\n3. TESTING GEMINI AI API");
  try {
    final geminiApiKey = dotenv.env['GEMINI_API_KEY'];
    if (geminiApiKey == null || geminiApiKey.isEmpty) {
      // LoggerService.warning("⚠️ Gemini API key not found in environment variables");
    } else {
      // LoggerService.info("Gemini API key found in environment variables");
    }
    
    // Initialize Gemini service
    final geminiInitialized = await GeminiService.initialize();
    if (geminiInitialized) {
      // LoggerService.info("✅ Gemini service initialized successfully");
      
      // Test a simple request to verify the API is working
      try {
        final testIngredient = "Organic cocoa beans";
        // LoggerService.info("Testing ingredient analysis with: '$testIngredient'");
        
        final ingredientAnalysis = await GeminiService.assessUnknownIngredient(testIngredient);
        // LoggerService.info("✅ Gemini AI API response received");
        // LoggerService.info("Safety level: ${ingredientAnalysis['safety_level']}");
        // LoggerService.info("Description: ${ingredientAnalysis['description']}");
      } catch (e) {
        // LoggerService.error("❌ Gemini AI API test failed: $e");
      }
    } else {
      // LoggerService.error("❌ Gemini service initialization failed");
    }
  } catch (e) {
    // LoggerService.error("❌ Gemini AI API test failed: $e");
  }
  
  // Test Supabase for Safety Analysis
  // LoggerService.info("\n4. TESTING SUPABASE FOR SAFETY ANALYSIS");
  try {
    // Check if the required tables exist for safety analysis
    // Updated to match existing tables in the database schema
    final tableNames = [
      'ingredients',
      'products',
      'scan_results',      // Instead of ai_analysis_cache
      'pending_ingredients',
      'ingredients_backup', // Additional backup table
      'assisted_ingredients' // Instead of user_feedback
    ];
    
    // LoggerService.info("Checking required tables for safety analysis:");
    
    final missingTables = <String>[];
    
    for (final tableName in tableNames) {
      try {
        final supabase = Supabase.instance.client;
        await supabase.from(tableName).select().limit(1);
        // LoggerService.info(" - ✅ $tableName (exists)");
      } catch (e) {
        // LoggerService.error(" - ❌ $tableName (error: ${e.toString().split('\n')[0]})");
        missingTables.add(tableName);
      }
    }
    
    // Create missing tables if any
    if (missingTables.isNotEmpty) {
      // LoggerService.info("\nAttempting to create missing tables:");
      await _createMissingTables(missingTables);
    }
    
    // Test ingredient database query
    try {
      // LoggerService.info("\nTesting ingredient database lookup:");
      final testIngredient = "cocoa";
      
      // Create SupabaseService instance
      final supabaseService = SupabaseService(SupabaseInitService.client);
      final ingredients = await supabaseService.searchIngredients(testIngredient);
      
      if (ingredients.isNotEmpty) {
        // LoggerService.info("✅ Found ${ingredients.length} ingredients matching '$testIngredient'");
        // LoggerService.info("First match: ${ingredients[0].name} (Safety level: ${ingredients[0].safetyLevel})");
      } else {
        // LoggerService.warning("⚠️ No ingredients found matching '$testIngredient'");
      }
    } catch (e) {
      // LoggerService.error("❌ Ingredient database test failed: $e");
    }
  } catch (e) {
    // LoggerService.error("❌ Supabase safety analysis test failed: $e");
  }
  
  // Test End-to-End Safety Analysis
  // LoggerService.info("\n5. TESTING END-TO-END SAFETY ANALYSIS");
  try {
    // Create a test product for safety analysis
    final testProduct = Product(
      id: 'test-id-12345',  // Changed to a clearer test ID format
      name: 'Organic Dark Chocolate',
      brand: 'Test Brand',
      barcode: testBarcode,
      safetyScore: 0,
      safetyRating: 'Unknown',
      scannedAt: DateTime.now(),
      userId: 'test-user',
      ingredientsJson: {
        'ingredients': [
          {'name': 'Organic cocoa beans'},
          {'name': 'Organic cane sugar'},
          {'name': 'Organic cocoa butter'},
          {'name': 'Sea salt'}
        ]
      },
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    // LoggerService.info("Running safety analysis on test product: ${testProduct.name}");
    // final analysisResult = await SafetyAnalysisService.analyzeProduct(testProduct); // Commented out - using enhanced service instead
    
    // LoggerService.info("✅ Safety analysis completed successfully");
    // LoggerService.info("Safety Score: ${analysisResult.overallScore}/100");
    // LoggerService.info("Safety Rating: ${analysisResult.safetyRating}");
    
    // Commented out since analysisResult is not available when analysis is commented out
    // if (analysisResult.violatingIngredients.isNotEmpty) {
    //   LoggerService.info("Violating ingredients found: ${analysisResult.violatingIngredients}");
    // } else {
    //   LoggerService.info("No violating ingredients found");
    // }
  } catch (e) {
    // LoggerService.error("❌ End-to-end safety analysis test failed: $e");
  }
  
  // LoggerService.info("\n===== DIAGNOSTIC TEST SUMMARY =====");
  // LoggerService.info("1. Supabase Connection: TESTED");
  // LoggerService.info("2. OpenFoodFacts API: TESTED");
  // LoggerService.info("3. Gemini AI API: TESTED");
  // LoggerService.info("4. Supabase for Safety Analysis: TESTED");
  // LoggerService.info("5. End-to-End Safety Analysis: TESTED");
  // LoggerService.info("===================================");
  
  // LoggerService.info("\nDiagnostic tests completed. You can review the results above to identify any issues.");
  // LoggerService.info("Exiting test utility...");
}

/// Create missing tables required for safety analysis
Future<void> _createMissingTables(List<String> missingTables) async {
  final supabase = Supabase.instance.client;
  
  try {
    // Create missing tables one by one
    for (final tableName in missingTables) {
      try {
        // LoggerService.info(" - Creating table: $tableName");
        
        switch (tableName) {
          case 'ai_analysis_cache':
            // SQL to create ai_analysis_cache table
            await supabase.rpc('create_ai_analysis_cache_table', params: {});
            // LoggerService.info(" - ✅ $tableName (created)");
            break;
            
          case 'scan_logs':
            // SQL to create scan_logs table
            await supabase.rpc('create_scan_logs_table', params: {});
            // LoggerService.info(" - ✅ $tableName (created)");
            break;
            
          case 'product_analysis_fallback':
            // SQL to create product_analysis_fallback table
            await supabase.rpc('create_product_analysis_fallback_table', params: {});
            // LoggerService.info(" - ✅ $tableName (created)");
            break;
            
          case 'unknown_barcodes':
            // SQL to create unknown_barcodes table
            await supabase.rpc('create_unknown_barcodes_table', params: {});
            // LoggerService.info(" - ✅ $tableName (created)");
            break;
            
          case 'pending_ingredients':
            // SQL to create pending_ingredients table
            await supabase.rpc('create_pending_ingredients_table', params: {});
            // LoggerService.info(" - ✅ $tableName (created)");
            break;
            
          default:
            // LoggerService.warning(" - ⚠️ $tableName (no creation script available)");
        }
      } catch (e) {
        // LoggerService.error(" - ❌ Failed to create $tableName: ${e.toString().split('\n')[0]}");
        // LoggerService.warning("   Note: You may need to manually create this table in the Supabase dashboard");
        
        // Output SQL that can be used to manually create tables
        final createTableSql = _getCreateTableSql(tableName);
        if (createTableSql.isNotEmpty) {
          // LoggerService.info("\n   SQL to create table $tableName:");
          // LoggerService.info("   $createTableSql");
          // LoggerService.info("");
        }
      }
    }
  } catch (e) {
    // LoggerService.error("❌ Error creating tables: $e");
  }
}

/// Get SQL for creating tables
String _getCreateTableSql(String tableName) {
  switch (tableName) {
    case 'ai_analysis_cache':
      return '''
CREATE TABLE ai_analysis_cache (
  id SERIAL PRIMARY KEY,
  product_id TEXT NOT NULL,
  analysis_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX idx_ai_analysis_cache_product_id ON ai_analysis_cache(product_id);
''';
      
    case 'scan_logs':
      return '''
CREATE TABLE scan_logs (
  id SERIAL PRIMARY KEY,
  barcode TEXT NOT NULL,
  request_id TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT NOT NULL
);
CREATE INDEX idx_scan_logs_barcode ON scan_logs(barcode);
CREATE INDEX idx_scan_logs_user_id ON scan_logs(user_id);
''';
      
    case 'product_analysis_fallback':
      return '''
CREATE TABLE product_analysis_fallback (
  id SERIAL PRIMARY KEY,
  barcode TEXT,
  product_name TEXT,
  safety_score INTEGER,
  safety_rating TEXT,
  user_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
''';
      
    case 'unknown_barcodes':
      return '''
CREATE TABLE unknown_barcodes (
  id SERIAL PRIMARY KEY,
  barcode TEXT NOT NULL UNIQUE,
  count INTEGER DEFAULT 1,
  first_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT,
  status TEXT DEFAULT 'pending'
);
''';
      
    case 'pending_ingredients':
      return '''
CREATE TABLE pending_ingredients (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  normalized_name TEXT,
  safety_level TEXT,
  description TEXT,
  health_risks TEXT[] DEFAULT '{}',
  pending_review BOOLEAN DEFAULT TRUE,
  ai_generated BOOLEAN DEFAULT FALSE,
  request_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
''';
      
    default:
      return '';
  }
} 