import 'package:flutter/material.dart';

class AnalysisLoadingOverlay {
  static OverlayEntry? _overlayEntry;
  static ValueNotifier<String> _stageNotifier = ValueNotifier('');
  static ValueNotifier<String> _detailNotifier = ValueNotifier('');

  // Stage constants matching the mermaid flow
  static const String stageCheckingDatabase = 'checking_database';
  static const String stageRetrievingDetails = 'retrieving_details';
  static const String stageAnalyzingImage = 'analyzing_image';
  static const String stageSearchingOnline = 'searching_online';
  static const String stageAnalyzingIngredients = 'analyzing_ingredients';

  // Detail messages
  static const String detailCheckingDatabase = 'Checking database...';
  static const String detailRetrievingDetails = 'Retrieving product details...';
  static const String detailAnalyzingImage = 'Analyzing product image...';
  static const String detailSearchingOnline = 'Searching online databases...';
  static const String detailAnalyzingIngredients = 'Analyzing ingredients...';

  static void show({required BuildContext context}) {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black54,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                ValueListenableBuilder<String>(
                  valueListenable: _detailNotifier,
                  builder: (context, detail, child) {
                    return Text(
                      detail.isNotEmpty ? detail : 'Analyzing product...',
                      style: const TextStyle(fontSize: 16),
                      textAlign: TextAlign.center,
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  static void updateStage({required String stage, String? detail}) {
    _stageNotifier.value = stage;
    
    final stageDetail = detail ?? _getDetailForStage(stage);
    _detailNotifier.value = stageDetail;
  }

  static String _getDetailForStage(String stage) {
    switch (stage) {
      case stageCheckingDatabase:
        return detailCheckingDatabase;
      case stageRetrievingDetails:
        return detailRetrievingDetails;
      case stageAnalyzingImage:
        return detailAnalyzingImage;
      case stageSearchingOnline:
        return detailSearchingOnline;
      case stageAnalyzingIngredients:
        return detailAnalyzingIngredients;
      default:
        return 'Processing...';
    }
  }

  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _stageNotifier.value = '';
    _detailNotifier.value = '';
  }
} 
