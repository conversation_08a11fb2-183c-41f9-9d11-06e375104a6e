import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

class SafetyTipsWidget extends StatelessWidget {
  const SafetyTipsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: AppTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Safety Tips',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.highContrastText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Daily tip with modern design
            _buildTipCard(
              icon: Icons.visibility_outlined,
              title: 'Read ingredient lists carefully',
              description: 'Look for additives, preservatives, and allergens that may affect your health.',
              color: const Color(0xFF4CAF50),
            ),
            const SizedBox(height: 12),
            
            _buildTipCard(
              icon: Icons.eco_outlined,
              title: 'Choose organic when possible',
              description: 'Organic products contain fewer pesticides and artificial additives.',
              color: const Color(0xFF2196F3),
            ),
            const SizedBox(height: 12),
            
            _buildTipCard(
              icon: Icons.balance_outlined,
              title: 'Check nutrition labels',
              description: 'Pay attention to sugar, sodium, and trans fat content for better health choices.',
              color: const Color(0xFFFF9800),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.highContrastText,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 