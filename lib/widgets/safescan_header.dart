import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SafeScanHeader extends StatelessWidget {
  final String pageTitle;
  final bool showAppName;
  const SafeScanHeader({
    Key? key,
    required this.pageTitle,
    this.showAppName = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(top: 56, left: 20, right: 20, bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/SafeScan-logo.png',
            height: 48,
            width: 48,
            fit: BoxFit.contain,
          ),
          const SizedBox(width: 8),
          if (showAppName)
            Text(
              'SAFESCAN',
              style: GoogleFonts.nunito(
                fontSize: 26,
                fontWeight: FontWeight.w800,
                letterSpacing: 0.5,
                color: Colors.black,
              ),
            )
          else
            Text(
              pageTitle,
              style: GoogleFonts.nunito(
                fontSize: 26,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: 0.2,
              ),
            ),
        ],
      ),
    );
  }
} 