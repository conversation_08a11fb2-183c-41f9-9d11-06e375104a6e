import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../constants/app_theme.dart';

class LoadingOverlay {
  static OverlayEntry? _overlayEntry;
  
  static void show({
    required BuildContext context,
    required String message,
    String? detailMessage,
    double animationSize = 140,
    bool isDarkMode = true,
    bool isFullScreen = false,
    String? processingStage,
  }) {
    if (_overlayEntry != null) {
      // Already showing, update the message
      hide();
    }
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = colorScheme.brightness == Brightness.dark;
    final backgroundColor = isDark ? colorScheme.background : colorScheme.surface;
    final textColor = colorScheme.onBackground;
    final detailColor = colorScheme.onBackground.withOpacity(0.7);
    final accentColor = AppTheme.primaryBlue;
    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        color: isFullScreen ? backgroundColor : colorScheme.background.withOpacity(0.7),
        child: Center(
          child: isFullScreen 
              ? _buildFullScreenLoader(message, detailMessage, animationSize, textColor, detailColor, processingStage)
              : _buildCompactLoader(message, detailMessage, animationSize, backgroundColor, textColor, detailColor, processingStage),
        ),
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  static Widget _buildFullScreenLoader(
    String message, 
    String? detailMessage, 
    double animationSize,
    Color textColor,
    Color detailColor,
    String? processingStage,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Only use Cat animation for scanning to results flow (when processingStage is not null)
        SizedBox(
          width: animationSize * 1.5,
          height: animationSize * 1.2, // Maintain aspect ratio
          child: processingStage != null 
              ? Lottie.asset(
                  'assets/animations/Cat-Loading-screen.json',
                  fit: BoxFit.contain,
                  frameRate: FrameRate.max,
                )
              : Lottie.asset(
                  'assets/animations/SafeScan-blue.json',
                  fit: BoxFit.contain,
                  frameRate: FrameRate.max,
                ),
        ),
        const SizedBox(height: 32),
        Text(
          message,
          style: TextStyle(
            color: textColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            shadows: textColor == Colors.white ? [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 2,
                color: Colors.black.withOpacity(0.3),
              ),
            ] : null, // Add shadow for white text on light backgrounds
          ),
          textAlign: TextAlign.center,
        ),
        if (detailMessage != null) ...[
          const SizedBox(height: 16),
          Text(
            detailMessage,
            style: TextStyle(
              color: detailColor,
              fontSize: 18,
              fontWeight: FontWeight.w500, // Added font weight for better readability
              shadows: detailColor == Colors.white ? [
                Shadow(
                  offset: const Offset(0, 1),
                  blurRadius: 2,
                  color: Colors.black.withOpacity(0.3),
                ),
              ] : null,
            ),
            textAlign: TextAlign.center,
          ),
        ],
        
        if (processingStage != null) ...[
          const SizedBox(height: 60),
          _buildProcessingStageIndicator(processingStage),
        ],
      ],
    );
  }
  
  static Widget _buildCompactLoader(
    String message, 
    String? detailMessage, 
    double animationSize,
    Color backgroundColor,
    Color textColor,
    Color detailColor,
    String? processingStage,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Only use Cat animation for scanning to results flow (when processingStage is not null)
          SizedBox(
            width: animationSize,
            height: animationSize * 0.75, // Maintain aspect ratio
            child: processingStage != null 
                ? Lottie.asset(
                    'assets/animations/Cat-Loading-screen.json',
                    fit: BoxFit.contain,
                    frameRate: FrameRate.max,
                  )
                : Lottie.asset(
                    'assets/animations/SafeScan-blue.json',
                    fit: BoxFit.contain,
                    frameRate: FrameRate.max,
                  ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (detailMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              detailMessage,
              style: TextStyle(
                color: detailColor,
                fontSize: 14,
                fontWeight: FontWeight.w500, // Added font weight for better readability
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
  
  static Widget _buildProcessingStageIndicator(String stage) {
    final List<String> stages = [
      "Data Collection",
      "Ingredient Analysis",
      "Safety Verification",
      "Report Generation"
    ];
    
    // Find current stage index
    int currentIndex = 0;
    for (int i = 0; i < stages.length; i++) {
      if (stages[i].toLowerCase().contains(stage.toLowerCase()) || 
          stage.toLowerCase().contains(stages[i].toLowerCase())) {
        currentIndex = i;
        break;
      }
    }
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(stages.length, (index) {
            return Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: index <= currentIndex ? AppTheme.primaryBlue : AppTheme.lightGrey, // Use lightGrey for better contrast
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: index <= currentIndex ? AppTheme.primaryBlue : AppTheme.lightGrey,
                      width: 2,
                    ),
                  ),
                  child: index <= currentIndex
                      ? const Icon(Icons.check, size: 12, color: Colors.white)
                      : null,
                ),
                if (index < stages.length - 1)
                  Container(
                    width: 40,
                    height: 2,
                    color: index < currentIndex ? AppTheme.primaryBlue : AppTheme.lightGrey, // Use lightGrey for better contrast
                  ),
              ],
            );
          }),
        ),
        const SizedBox(height: 12),
        Text(
          stages[currentIndex],
          style: TextStyle(
            color: AppTheme.primaryBlue,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 2,
                color: Colors.black.withOpacity(0.2),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
} 