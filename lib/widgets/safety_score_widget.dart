import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import 'dart:math' as math;

class SafetyScoreWidget extends StatelessWidget {
  final int score;
  final double size;
  final bool showText;

  const SafetyScoreWidget({
    super.key,
    required this.score,
    this.size = 80,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final scoreColor = score >= 80
        ? AppTheme.primaryBlue
        : score >= 50
            ? AppTheme.warningOrange
            : AppTheme.dangerRed;
    return Container(
      decoration: BoxDecoration(
        color: scoreColor.withOpacity(0.13),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: scoreColor.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: scoreColor.withOpacity(0.3)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.verified, color: scoreColor, size: size / 2.2),
          const SizedBox(width: 8),
          Text(
            '$score',
            style: TextStyle(
              fontSize: size / 2.1,
              fontWeight: FontWeight.bold,
              color: scoreColor,
            ),
          ),
          Text(
            '/100',
            style: TextStyle(
              fontSize: size / 3.2,
              color: scoreColor.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(int score) {
    if (score >= 80) {
      return AppTheme.primaryBlue;
    } else if (score >= 50) {
      return AppTheme.warningOrange; // Use warningOrange for better contrast
    } else {
      return AppTheme.dangerRed; // Use dangerRed for better contrast
    }
  }
}

class SafetyScorePainter extends CustomPainter {
  final int score;
  final Color color;
  final double strokeWidth;

  SafetyScorePainter({
    required this.score,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Calculate sweep angle based on score (0-100 maps to 0-2π)
    final sweepAngle = (score / 100) * 2 * math.pi;
    
    // Draw arc starting from top (-π/2)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(SafetyScorePainter oldDelegate) {
    return oldDelegate.score != score ||
           oldDelegate.color != color ||
           oldDelegate.strokeWidth != strokeWidth;
  }
} 