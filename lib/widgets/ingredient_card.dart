import 'package:flutter/material.dart';
import '../models/ingredient.dart';
import '../constants/app_theme.dart';

class IngredientCard extends StatefulWidget {
  final AnalyzedIngredient ingredient;

  const IngredientCard({
    super.key,
    required this.ingredient,
  });

  @override
  State<IngredientCard> createState() => _IngredientCardState();
}

class _IngredientCardState extends State<IngredientCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.08),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.ingredient.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                if (widget.ingredient.description != null && widget.ingredient.description!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      widget.ingredient.description!,
                      style: TextStyle(
                        fontSize: 13,
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (widget.ingredient.status != null)
            _IngredientStatusBadge(status: widget.ingredient.status!),
        ],
      ),
    );
  }

  Color _getSafetyColor(String safetyLevel) {
    switch (safetyLevel) {
      case 'green':
        return AppTheme.primaryGreen;
      case 'yellow':
        return Colors.orange;
      case 'red':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getSafetyIcon(String safetyLevel) {
    switch (safetyLevel) {
      case 'green':
        return Icons.check_circle;
      case 'yellow':
        return Icons.warning;
      case 'red':
        return Icons.dangerous;
      default:
        return Icons.help;
    }
  }

  String _getSafetyText(String safetyLevel) {
    switch (safetyLevel) {
      case 'green':
        return 'Safe';
      case 'yellow':
        return 'Caution';
      case 'red':
        return 'Avoid';
      default:
        return 'Unknown';
    }
  }
}

class _IngredientStatusBadge extends StatelessWidget {
  final String status;
  const _IngredientStatusBadge({required this.status});
  @override
  Widget build(BuildContext context) {
    Color color;
    String label;
    switch (status) {
      case 'safe':
        color = AppTheme.primaryBlue;
        label = 'Safe';
        break;
      case 'caution':
        color = AppTheme.warningOrange;
        label = 'Caution';
        break;
      case 'danger':
        color = AppTheme.dangerRed;
        label = 'Danger';
        break;
      default:
        color = AppTheme.lightGrey;
        label = status;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.13),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.4)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 13,
        ),
      ),
    );
  }
} 