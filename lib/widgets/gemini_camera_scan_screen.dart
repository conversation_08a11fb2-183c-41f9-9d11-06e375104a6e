import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import '../constants/app_theme.dart';
import '../services/gemini_service.dart';
import '../services/logger_service.dart';

class GeminiCameraScanScreen extends StatefulWidget {
  final Function(String)? onBarcodeDetected;

  const GeminiCameraScanScreen({super.key, this.onBarcodeDetected});

  @override
  State<GeminiCameraScanScreen> createState() => _GeminiCameraScanScreenState();
}

class _GeminiCameraScanScreenState extends State<GeminiCameraScanScreen> {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isProcessing = false;
  String? _error;
  String? _statusMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      // Request camera permission
      final status = await Permission.camera.request();
      if (status != PermissionStatus.granted) {
        if (mounted) {
          _showPermissionDialog();
        }
        return;
      }

      // Get available cameras
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        if (mounted) {
          setState(() => _error = 'No cameras available');
        }
        return;
      }

      // Use back camera for better barcode scanning
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      // Initialize camera controller
      _controller = CameraController(
        camera,
        ResolutionPreset.high, // High resolution for better barcode recognition
        enableAudio: false,
      );

      await _controller!.initialize();

      if (mounted) {
        setState(() => _isInitialized = true);
      }
    } catch (e) {
      LoggerService.error('[GeminiCameraScreen] Error initializing camera: $e');
      if (mounted) {
        setState(() => _error = 'Failed to initialize camera: ${e.toString()}');
      }
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Camera Permission Required'),
        content: const Text(
          'This app needs camera access to scan product barcodes. Please grant camera permission in settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _captureAndAnalyzeImage() async {
    if (_controller == null || !_controller!.value.isInitialized || _isProcessing) {
      return;
    }

    setState(() {
      _isProcessing = true;
      _statusMessage = 'Capturing image...';
    });

    try {
      // Capture image
      final image = await _controller!.takePicture();
      
      setState(() {
        _statusMessage = 'Analyzing image with AI...';
      });

      // Extract barcode using Gemini AI
      final barcode = await GeminiService.extractBarcodeFromImage(image.path);
      
      if (barcode != null && barcode.isNotEmpty) {
        LoggerService.info('[GeminiCameraScreen] Detected barcode: $barcode');
        
        // Clean up the temporary image file
        try {
          await File(image.path).delete();
        } catch (e) {
          LoggerService.info('Failed to delete temporary image: $e');
        }

        // Call the callback or return via Navigator
        if (widget.onBarcodeDetected != null) {
          widget.onBarcodeDetected!(barcode);
        } else {
          if (mounted) {
            Navigator.of(context).pop(barcode);
          }
        }
      } else {
        // No barcode found
        setState(() {
          _statusMessage = 'No barcode found. Try again with better lighting or angle.';
        });
        
        // Clear status message after 3 seconds
        Timer(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _statusMessage = null;
            });
          }
        });
      }
    } catch (e) {
      LoggerService.error('[GeminiCameraScreen] Error capturing/analyzing image: $e');
      setState(() {
        _statusMessage = 'Error analyzing image. Please try again.';
      });
      
      // Clear error message after 3 seconds
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _statusMessage = null;
          });
        }
      });
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return _buildErrorWidget();
    }

    if (!_isInitialized) {
      return _buildLoadingWidget();
    }

    return _buildCameraWidget();
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _error = null;
                });
                _initializeCamera();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Initializing camera...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraWidget() {
    return Stack(
      children: [
        // Camera preview
        CameraPreview(_controller!),
        
        // Overlay with scanning frame
        _buildScanningOverlay(),
        
        // Status message
        if (_statusMessage != null) _buildStatusMessage(),
        
        // Capture button
        _buildCaptureButton(),
        
        // Instructions
        _buildInstructions(),
      ],
    );
  }

  Widget _buildScanningOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
      ),
      child: Center(
        child: Container(
          width: 250,
          height: 250,
          decoration: BoxDecoration(
            border: Border.all(
              color: _isProcessing ? Colors.orange : Colors.white,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          _statusMessage!,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildCaptureButton() {
    return Positioned(
      bottom: 80,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: _isProcessing ? null : _captureAndAnalyzeImage,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _isProcessing ? Colors.grey : Colors.white,
              border: Border.all(
                color: _isProcessing ? Colors.grey : AppTheme.primaryBlue,
                width: 4,
              ),
            ),
            child: _isProcessing
                ? const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  )
                : const Icon(
                    Icons.camera_alt,
                    color: AppTheme.primaryBlue,
                    size: 32,
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Text(
        'Position the barcode within the frame and tap the capture button',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
