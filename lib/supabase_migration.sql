-- SafeScan Database Migration Script
-- This script creates views or tables that map existing database schema to the ones needed by the app

-- Create a view for ai_analysis_cache based on scan_results
CREATE OR REPLACE VIEW ai_analysis_cache AS
SELECT 
  id::text AS id,
  product_id::text AS product_id,
  jsonb_build_object(
    'product_name', product_name,
    'confidence_score', confidence_score,
    'source', source,
    'timestamp', scan_date
  ) AS analysis_data,
  created_at
FROM scan_results;

-- Create a view for scan_logs based on scan_results
CREATE OR REPLACE VIEW scan_logs AS
SELECT 
  id::text AS id,
  barcodes AS barcode,
  product_id::text AS request_id,
  scan_date AS timestamp,
  'anonymous' AS user_id
FROM scan_results;

-- Create a view for product_analysis_fallback based on products
CREATE OR REPLACE VIEW product_analysis_fallback AS
SELECT 
  id::text AS id,
  barcodes AS barcode,
  brand_id::text AS product_name,
  safety_score::integer AS safety_score,
  safety_rating,
  NULL AS user_id,
  created_at
FROM products;

-- Create a view for user_feedback based on assisted_ingredients
CREATE OR REPLACE VIEW user_feedback AS
SELECT 
  id::text AS id,
  user_id::text AS user_id,
  NULL AS product_id,
  'ingredient_feedback' AS feedback_type,
  jsonb_build_object(
    'ingredient_name', name,
    'reason', reason,
    'timestamp', created_at
  ) AS feedback_data,
  created_at
FROM assisted_ingredients;

-- Create fallback user_feedback table if assisted_ingredients doesn't exist
CREATE TABLE IF NOT EXISTS user_feedback (
  id SERIAL PRIMARY KEY,
  user_id TEXT,
  product_id TEXT,
  feedback_type TEXT DEFAULT 'product_feedback',
  feedback_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a view for unknown_barcodes (no direct mapping, creating a basic structure)
CREATE TABLE IF NOT EXISTS unknown_barcodes (
  id SERIAL PRIMARY KEY,
  barcode TEXT NOT NULL UNIQUE,
  count INTEGER DEFAULT 1,
  first_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id TEXT,
  status TEXT DEFAULT 'pending'
);

-- Create functions that allow the app to write to these views
-- by redirecting writes to the underlying tables

-- Function to handle inserts to ai_analysis_cache
CREATE OR REPLACE FUNCTION insert_ai_analysis_cache()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO scan_results (
    product_id, 
    barcodes, 
    product_name, 
    confidence_score, 
    source, 
    scan_date
  ) VALUES (
    NEW.product_id,
    NEW.product_id, -- using product_id as barcode
    (NEW.analysis_data->>'product_name')::text,
    COALESCE((NEW.analysis_data->>'confidence_score')::float, 0.9),
    COALESCE((NEW.analysis_data->>'source')::text, 'app_analysis'),
    COALESCE((NEW.analysis_data->>'timestamp')::timestamp with time zone, NOW())
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for ai_analysis_cache inserts
DROP TRIGGER IF EXISTS ai_analysis_cache_insert_trigger ON ai_analysis_cache;
CREATE TRIGGER ai_analysis_cache_insert_trigger
INSTEAD OF INSERT ON ai_analysis_cache
FOR EACH ROW EXECUTE FUNCTION insert_ai_analysis_cache();

-- Function to handle inserts to scan_logs
CREATE OR REPLACE FUNCTION insert_scan_logs()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO scan_results (
    barcodes,
    product_id,
    scan_date,
    source,
    confidence_score
  ) VALUES (
    NEW.barcode,
    NEW.request_id,
    NEW.timestamp,
    'app_scan',
    1.0
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for scan_logs inserts
DROP TRIGGER IF EXISTS scan_logs_insert_trigger ON scan_logs;
CREATE TRIGGER scan_logs_insert_trigger
INSTEAD OF INSERT ON scan_logs
FOR EACH ROW EXECUTE FUNCTION insert_scan_logs();

-- Create stored procedures for creating tables that can be called from app
CREATE OR REPLACE PROCEDURE create_ai_analysis_cache_table()
LANGUAGE plpgsql
AS $$
BEGIN
  -- This procedure will be called from the app but we're using views instead
  RAISE NOTICE 'Using scan_results table with view mapping instead of creating new table';
END;
$$;

CREATE OR REPLACE PROCEDURE create_scan_logs_table()
LANGUAGE plpgsql
AS $$
BEGIN
  -- This procedure will be called from the app but we're using views instead
  RAISE NOTICE 'Using scan_results table with view mapping instead of creating new table';
END;
$$;

CREATE OR REPLACE PROCEDURE create_product_analysis_fallback_table()
LANGUAGE plpgsql
AS $$
BEGIN
  -- This procedure will be called from the app but we're using views instead
  RAISE NOTICE 'Using products table with view mapping instead of creating new table';
END;
$$;

CREATE OR REPLACE PROCEDURE create_unknown_barcodes_table()
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create the table if it doesn't exist
  CREATE TABLE IF NOT EXISTS unknown_barcodes (
    id SERIAL PRIMARY KEY,
    barcode TEXT NOT NULL UNIQUE,
    count INTEGER DEFAULT 1,
    first_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_scan TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id TEXT,
    status TEXT DEFAULT 'pending'
  );
  RAISE NOTICE 'Created unknown_barcodes table';
END;
$$;

CREATE OR REPLACE PROCEDURE create_user_feedback_table()
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if assisted_ingredients exists
  DECLARE
    table_exists BOOLEAN;
  BEGIN
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'assisted_ingredients'
    ) INTO table_exists;
    
    IF table_exists THEN
      -- Create view if the table exists
      EXECUTE 'CREATE OR REPLACE VIEW user_feedback AS
        SELECT 
          id::text AS id,
          user_id::text AS user_id,
          NULL AS product_id,
          ''ingredient_feedback'' AS feedback_type,
          jsonb_build_object(
            ''ingredient_name'', name,
            ''reason'', reason,
            ''timestamp'', created_at
          ) AS feedback_data,
          created_at
        FROM assisted_ingredients';
      RAISE NOTICE 'Created user_feedback view based on assisted_ingredients table';
    ELSE
      -- Create table if assisted_ingredients doesn't exist
      CREATE TABLE IF NOT EXISTS user_feedback (
        id SERIAL PRIMARY KEY,
        user_id TEXT,
        product_id TEXT,
        feedback_type TEXT DEFAULT 'product_feedback',
        feedback_data JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      RAISE NOTICE 'Created user_feedback table';
    END IF;
  END;
END;
$$; 