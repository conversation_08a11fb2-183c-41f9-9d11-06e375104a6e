import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/gemini_camera_scan_screen.dart';
import '../providers/product_provider.dart';

class ScanScreen extends ConsumerStatefulWidget {
  const ScanScreen({super.key});

  @override
  ConsumerState<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends ConsumerState<ScanScreen> {
  bool _isScanning = false;
  String? _lastScannedBarcode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Product'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Stack(
        children: [
          // Camera view with Gemini AI barcode extraction
          GeminiCameraScanScreen(
            onBarcodeDetected: _handleBarcodeDetected,
          ),
          

          
          // Scanning indicator
          if (_isScanning)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Scanning product...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _handleBarcodeDetected(String barcode) async {
    // Prevent multiple scans of the same barcode
    if (_isScanning || barcode == _lastScannedBarcode) return;

    setState(() {
      _isScanning = true;
      _lastScannedBarcode = barcode;
    });

    try {
      // Use the product provider to analyze the product by barcode
      final productProvider = ref.read(productControllerProvider.notifier);

      // Start the product analysis workflow
      await productProvider.scanProductByBarcode(barcode);

      // Listen to the product state to handle the result
      final productState = ref.read(productControllerProvider);

      if (mounted) {
        productState.when(
          data: (product) {
            if (product != null) {
              // Navigate to product details screen
              context.go('/product/${product.id}');
            } else {
              // Product analysis failed, show error
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Product not found. Please try again or enter details manually.'),
                  backgroundColor: Colors.orange,
                ),
              );
              context.pop(); // Go back to home screen
            }
          },
          loading: () {
            // Keep showing loading indicator
          },
          error: (error, stackTrace) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error analyzing product: ${error.toString()}'),
                backgroundColor: Colors.red,
              ),
            );
            context.pop(); // Go back to home screen
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error scanning product: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        context.pop(); // Go back to home screen
      }
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }
}
