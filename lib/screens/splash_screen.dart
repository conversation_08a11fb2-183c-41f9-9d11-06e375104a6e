import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:lottie/lottie.dart';
import '../constants/app_theme.dart';
import '../providers/supabase_provider.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Add a minimum splash duration
      await Future.delayed(const Duration(seconds: 2));
      
      // Check authentication
      await _checkAuth();
    } catch (e) {
      print('[SplashScreen] Error during initialization: $e');
      // Navigate to login on error
      if (mounted) {
        context.go('/login');
      }
    }
  }

  Future<void> _checkAuth() async {
    try {
      print('[SplashScreen] _checkAuth called');
      
      final session = Supabase.instance.client.auth.currentSession;
      print('[SplashScreen] session: ${session?.user?.id ?? 'NULL'}');
      
      if (mounted) {
        if (session != null) {
          print('[SplashScreen] User authenticated, navigating to /home');
          context.go('/home');
        } else {
          print('[SplashScreen] No session, navigating to /login');
          context.go('/login');
        }
      }
    } catch (e) {
      print('[SplashScreen] Auth check error: $e');
      if (mounted) {
        context.go('/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.security,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 24),
            
            // App Name
            const Text(
              'SafeScan',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            
            // Tagline
            const Text(
              'Scan. Analyze. Stay Safe.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 48),
            
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
          ],
        ),
      ),
    );
  }
} 
