import 'dart:convert';
import 'package:flutter/foundation.dart';

class SafeJsonParser {
  /// Safely parse JSON string to Map
  static Map<String, dynamic>? parseJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return null;
    
    try {
      // First try direct parsing
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Initial JSON parsing failed: $e');
      
      try {
        // Try cleaning the string
        final cleanedString = _cleanJsonString(jsonString);
        return json.decode(cleanedString) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('Cleaned JSON parsing failed: $e');
        return null;
      }
    }
  }

  /// Safely parse JSON string to List
  static List<dynamic>? parseJsonList(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return null;
    
    try {
      return json.decode(jsonString) as List<dynamic>;
    } catch (e) {
      debugPrint('JSON list parsing failed: $e');
      return null;
    }
  }

  /// Safely extract nested JSON from a Map
  static Map<String, dynamic>? extractNestedJson(Map<String, dynamic>? data, String key) {
    if (data == null || !data.containsKey(key)) return null;
    
    final value = data[key];
    if (value is Map<String, dynamic>) return value;
    if (value is String) return parseJson(value);
    return null;
  }

  /// Safely extract nested list from a Map
  static List<dynamic>? extractNestedList(Map<String, dynamic>? data, String key) {
    if (data == null || !data.containsKey(key)) return null;
    
    final value = data[key];
    if (value is List) return value;
    if (value is String) return parseJsonList(value);
    return null;
  }

  /// Safely get a value from a Map with type checking
  static T? getValue<T>(Map<String, dynamic>? data, String key, {T? defaultValue}) {
    if (data == null || !data.containsKey(key)) return defaultValue;
    
    final value = data[key];
    if (value is T) return value;
    
    // Handle string to type conversion
    if (value is String) {
      if (T == int) return int.tryParse(value) as T?;
      if (T == double) return double.tryParse(value) as T?;
      if (T == bool) return (value.toLowerCase() == 'true') as T?;
    }
    
    return defaultValue;
  }

  /// Clean and normalize JSON string
  static String _cleanJsonString(String input) {
    // Remove BOM if present
    String cleaned = input.replaceAll('\uFEFF', '');
    
    // Remove any non-JSON content before the first {
    final firstBrace = cleaned.indexOf('{');
    if (firstBrace > 0) {
      cleaned = cleaned.substring(firstBrace);
    }
    
    // Remove any content after the last }
    final lastBrace = cleaned.lastIndexOf('}');
    if (lastBrace > 0 && lastBrace < cleaned.length - 1) {
      cleaned = cleaned.substring(0, lastBrace + 1);
    }
    
    // Fix common JSON formatting issues
    cleaned = cleaned
        .replaceAll('\n', '')
        .replaceAll('\r', '')
        .replaceAll('\t', '')
        .replaceAll('\\"', '"')
        .replaceAll('"{', '{')
        .replaceAll('}"', '}')
        .replaceAll('"[', '[')
        .replaceAll(']"', ']');
    
    return cleaned;
  }

  /// Merge multiple JSON objects
  static Map<String, dynamic> mergeJsonObjects(List<Map<String, dynamic>?> objects) {
    final result = <String, dynamic>{};
    
    for (final obj in objects) {
      if (obj != null) {
        obj.forEach((key, value) {
          if (value is Map<String, dynamic> && result[key] is Map<String, dynamic>) {
            // Recursively merge nested objects
            result[key] = mergeJsonObjects([result[key] as Map<String, dynamic>, value]);
          } else {
            result[key] = value;
          }
        });
      }
    }
    
    return result;
  }

  /// Validate JSON structure against expected schema
  static bool validateJsonStructure(Map<String, dynamic> data, Map<String, dynamic> schema) {
    try {
      for (final key in schema.keys) {
        if (!data.containsKey(key)) return false;
        
        final expectedType = schema[key];
        final actualValue = data[key];
        
        if (expectedType is Map<String, dynamic>) {
          if (actualValue is! Map<String, dynamic>) return false;
          if (!validateJsonStructure(actualValue, expectedType)) return false;
        } else if (expectedType is List) {
          if (actualValue is! List) return false;
          if (expectedType.isNotEmpty && actualValue.isNotEmpty) {
            final firstExpectedType = expectedType.first;
            if (firstExpectedType is Map<String, dynamic>) {
              for (final item in actualValue) {
                if (item is! Map<String, dynamic>) return false;
                if (!validateJsonStructure(item, firstExpectedType)) return false;
              }
            }
          }
        }
      }
      return true;
    } catch (e) {
      debugPrint('JSON validation error: $e');
      return false;
    }
  }
} 