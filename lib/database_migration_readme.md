# SafeScan Database Migration Guide

## Overview

This guide explains how to set up the database tables needed for the SafeScan app to function properly.

The app requires several tables for storing product information, analysis results, and user feedback. Some of these tables may already exist in your database with different names or structures.

## Missing Tables

The app looks for the following tables:
- `ai_analysis_cache` - Stores cached analysis results from AI processing
- `scan_events` - Logs scanning events for analytics
- `product_analysis_fallback` - Fallback table for product analysis data
- `user_feedback` - Stores user feedback about products and ingredients
- `pending_ingredients` - Stores ingredients pending review
- `unknown_barcodes` - Tracks barcodes that weren't found in the database

## Migration Script

We've created a SQL migration script that maps existing tables to the ones needed by the app. The script creates views and stored procedures that allow the app to work with your existing database schema.

### How to Run the Migration

1. Open the Supabase dashboard for your project
2. Go to the SQL Editor
3. Copy and paste the contents of `lib/supabase_migration.sql` into the editor
4. Run the script

### What the Migration Does

The script:
1. Creates views that map existing tables to the ones needed by the app
2. Sets up triggers to handle write operations to these views
3. Creates stored procedures that the app can call to verify or create required tables

### Testing the Migration

After running the migration, you can test it by:
1. Running the diagnostic app from the command line:
   ```
   flutter run -d <your-device> lib/diagnostic_app.dart
   ```
2. Clicking the "Run Diagnostic Tests" button
3. Checking the results in the diagnostic log

## Manual Setup

If you prefer to set up the tables manually:

1. Create the following tables:
   - `ai_analysis_cache` - For storing AI analysis results
   - `scan_logs` - For logging scan events
   - `user_feedback` - For storing user feedback
   - `unknown_barcodes` - For tracking unknown barcodes
   - `pending_ingredients` - For ingredients pending review

2. Ensure each table has the correct schema as defined in the migration script

## Troubleshooting

If you encounter issues:

1. Check the Supabase SQL Editor console for any error messages
2. Verify that all required tables exist with the correct structure
3. Run the diagnostic app to identify specific missing tables
4. Try running the individual CREATE TABLE statements one by one 