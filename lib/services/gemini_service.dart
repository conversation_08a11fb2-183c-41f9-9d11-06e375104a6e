import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/ingredient.dart';
import '../models/product.dart';
import 'package:flutter/foundation.dart';
import 'logger_service.dart';

/// Service for interacting with Google's Gemini AI API for enhanced product analysis
class GeminiService {
  // Use environment variable for API key
  static String? _apiKey;
  
  // Gemini model configurations
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1';
  
  // Primary models as per requirements
  static const String _gemini2FlashModel = 'gemini-2.0-flash'; // For fast, versatile tasks
  static const String _gemini15ProModel = 'gemini-1.5-pro'; // For complex reasoning tasks
  
  static const int _maxRetries = 3;
  static const Duration _initialBackoff = Duration(seconds: 2);
  
  // Track rate limiting for each model with reset timers
  static bool _isFlashModelRateLimited = false;
  static bool _isProModelRateLimited = false;
  static DateTime _flashModelRateLimitResetTime = DateTime.now();
  static DateTime _proModelRateLimitResetTime = DateTime.now();
  
  // Get text model with fallback chain
  static String get _textModel {
    if (!_isFlashModelRateLimited) {
      return _gemini2FlashModel; // Default to Flash for most operations
    } else if (!_isProModelRateLimited) {
      return _gemini15ProModel; // Fall back to Pro if Flash is rate limited
    } else {
      // Last resort - use Pro model even if potentially rate limited
      return _gemini15ProModel;
    }
  }
  
  // Get vision model
  static String get _visionModel {
    // For vision tasks, always use Flash model as it supports vision
    return _gemini2FlashModel;
  }
  
  // Get complex reasoning model
  static String get _complexModel {
    if (!_isFlashModelRateLimited) {
      return _gemini2FlashModel; // Use Flash model for complex reasoning too
    } else if (!_isProModelRateLimited) {
      return _gemini15ProModel; // Fall back to Pro if Flash is rate limited
    } else {
      // Last resort
      return _gemini2FlashModel; 
    }
  }

  /// Initialize the service with API key
  static Future<bool> initialize() async {
    try {
      // Load environment variables if not already loaded
      try {
        await dotenv.load(fileName: ".env");
      } catch (e) {
        LoggerService.info("Environment variables not loaded: $e");
      }
      
      // Get API key from environment variables
      _apiKey = dotenv.env['GEMINI_API_KEY'];
      
      if (_apiKey == null || _apiKey!.isEmpty) {
        LoggerService.info('Gemini API key not found in environment variables');
        // Still return true to let the app function with fallbacks
        return true;
      }
      
      // Test the API key with a simple request
      try {
        final testPrompt = "Echo back 'API key is valid'";
        await _callGeminiAPI(testPrompt, model: _gemini2FlashModel, useRetries: false);
        LoggerService.info('Gemini API key verified successfully');
        return true;
      } catch (apiError) {
        LoggerService.info('Gemini API key verification failed: $apiError');
        // Clear the API key so the app uses fallbacks
        _apiKey = null;
        // Still return true to let the app function with fallbacks
        return true;
      }
    } catch (e) {
      LoggerService.info('Error initializing GeminiService: $e');
      // Still return true to let the app function with fallbacks
      return true;
    }
  }

  /// Extract JSON from potentially malformed Gemini response
  static Map<String, dynamic> _extractJsonFromResponse(String response) {
    try {
      // First try to parse the entire response as JSON
      return json.decode(response.trim());
    } catch (e) {
      // If that fails, try to extract JSON within markdown code blocks
      final codeBlockRegex = RegExp(r'```(?:json)?\s*([\s\S]*?)\s*```');
      final match = codeBlockRegex.firstMatch(response);
      
      if (match != null && match.groupCount >= 1) {
        try {
          final jsonStr = match.group(1)!.trim();
          return json.decode(jsonStr);
        } catch (e) {
          LoggerService.info('Error parsing JSON from code block: $e');
        }
      }
      
      // If still not successful, try to extract anything that looks like JSON
      final jsonRegex = RegExp(r'{[\s\S]*}');
      final jsonMatch = jsonRegex.firstMatch(response);
      
      if (jsonMatch != null) {
        try {
          final jsonStr = jsonMatch.group(0)!.trim();
          // Check if JSON is potentially truncated
          if (!jsonStr.endsWith('}')) {
            LoggerService.info('Potentially truncated JSON detected, attempting repair');
            // Add closing brace if missing
            return json.decode(jsonStr + '}');
          }
          return json.decode(jsonStr);
        } catch (e) {
          LoggerService.info('Error parsing JSON from regex match: $e');
          // Try one more approach for handling truncated/malformed JSON
          try {
            // Try to parse with more lenient approach for malformed JSON
            // Make sure we're using the jsonStr from this scope
            final jsonStrToClean = jsonMatch.group(0)!.trim();
            final cleanedJson = _sanitizePartialJson(jsonStrToClean);
            return Map<String, dynamic>.from(json.decode(cleanedJson));
          } catch (innerE) {
            LoggerService.info('Failed to repair malformed JSON: $innerE');
          }
        }
      }
      
      // Additional attempt to extract JSON using a more comprehensive regex
      try {
        final betterJsonRegex = RegExp(r'({[\s\S]*?})(?:\s*$|\n)');
        final betterMatch = betterJsonRegex.firstMatch(response);
        if (betterMatch != null) {
          final jsonStr = betterMatch.group(1)!.trim();
          return Map<String, dynamic>.from(json.decode(_sanitizePartialJson(jsonStr)));
        }
      } catch (e) {
        LoggerService.info('Error in comprehensive JSON extraction: $e');
      }
      
      // If all parsing attempts fail, extract text information
      LoggerService.info('JSON parsing failed, falling back to text extraction: $e');
      return _extractTextualInformation(response);
    }
  }
  
  /// Attempt to sanitize potentially malformed JSON
  static String _sanitizePartialJson(String input) {
    // Make sure all open braces have matching closing braces
    int openBraces = 0;
    int closeBraces = 0;
    int openBrackets = 0;
    int closeBrackets = 0;
    
    for (int i = 0; i < input.length; i++) {
      if (input[i] == '{') openBraces++;
      if (input[i] == '}') closeBraces++;
      if (input[i] == '[') openBrackets++;
      if (input[i] == ']') closeBrackets++;
    }
    
    String result = input;
    
    // Add missing closing brackets
    while (closeBrackets < openBrackets) {
      result += ']';
      closeBrackets++;
    }
    
    // Add missing closing braces
    while (closeBraces < openBraces) {
      result += '}';
      closeBraces++;
    }
    
    // Fix common JSON formatting issues
    result = result
      .replaceAll("'", '"')  // Replace single quotes with double quotes
      .replaceAll(RegExp(r',(\s*[}\]])'), r'$1')  // Remove trailing commas before closing brackets/braces
      .replaceAll(RegExp(r'([{,])\s*(\w+)\s*:'), r'$1"$2":'); // Ensure property names are quoted
    
    // Ensure the result is valid JSON format
    if (!result.trim().endsWith('}') && !result.trim().endsWith(']')) {
      result = result.trim() + '}';
    }
    
    return result;
  }
  
  /// Extract textual information when JSON parsing fails
  static Map<String, dynamic> _extractTextualInformation(String response) {
    // Create a basic structure with extracted information
    final result = <String, dynamic>{
      'safety_level': 'Caution',
      'description': 'Information extracted from text response',
      'health_risks': <String>[],
      'source': 'Unknown',
      'common_uses': <String>[],
      'ingredients': <String>[],
      'ingredients_text': '',
      'nutrition_facts': <String, dynamic>{},
      'certifications': <String>[],
      'health_claims': <String>[],
    };
    
    // Try to extract safety level
    final safetyLevelRegex = RegExp(r'(?:safety_level|Safety Level)[:\s]+"?(Safe|Generally Safe|Moderate Concern|Caution|Avoid|Unsafe)"?', caseSensitive: false);
    final safetyMatch = safetyLevelRegex.firstMatch(response);
    if (safetyMatch != null && safetyMatch.groupCount >= 1) {
      result['safety_level'] = safetyMatch.group(1);
    }
    
    // Try to extract description
    final descriptionRegex = RegExp(r'(?:description|Description)[:\s]+"([^"]+)"', caseSensitive: false);
    final descMatch = descriptionRegex.firstMatch(response);
    if (descMatch != null && descMatch.groupCount >= 1) {
      result['description'] = descMatch.group(1);
    } else {
      // If no description found, use the first paragraph that's not JSON-like
      final paragraphs = response.split('\n\n');
      for (final paragraph in paragraphs) {
        if (!paragraph.trim().startsWith('{') && paragraph.length > 10) {
          result['description'] = paragraph.trim();
          break;
        }
      }
    }
    
    // Try to extract ingredients
    final ingredientsRegex = RegExp(r'(?:ingredients)[:\s]+"([^"]+)"', caseSensitive: false);
    final ingredientsMatch = ingredientsRegex.firstMatch(response);
    if (ingredientsMatch != null && ingredientsMatch.groupCount >= 1) {
      final ingredientsText = ingredientsMatch.group(1) ?? '';
      result['ingredients_text'] = ingredientsText;
      
      // Split ingredients by commas
      final ingredients = ingredientsText.split(',').map((i) => i.trim()).toList();
      result['ingredients'] = ingredients;
    }
    
    return result;
  }

  /// Analyze unknown ingredient using Gemini AI
  static Future<Map<String, dynamic>> assessUnknownIngredient(String ingredientName) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        LoggerService.info('Gemini API key not configured, using fallback.');
        return _generateOfflineIngredientResponse(ingredientName);
      }
    }

    try {
      final prompt = '''
Analyze the safety of the ingredient: $ingredientName
Consider:
1. Common uses
2. Safety concerns
3. Regulatory status
4. Health impacts
5. Alternatives

Provide a structured response with:
- Safety level (safe, caution, avoid)
- Health risks
- Usage recommendations
- Scientific evidence
''';

      final response = await _callGeminiAPI(prompt, model: _gemini2FlashModel);
      return _extractJsonFromResponse(response);
    } catch (e) {
      LoggerService.info('Error assessing ingredient: $e');
      return {
        'safety_level': 'unknown',
        'health_risks': [],
        'recommendations': 'Unable to assess ingredient safety',
        'evidence': 'No data available',
      };
    }
  }

  /// Analyze ingredient interactions using Gemini AI
  static Future<Map<String, dynamic>> analyzeIngredientInteractions(List<String> ingredientNames) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    if (ingredientNames.isEmpty) {
      return {
        'interactions': [],
        'warnings': [],
      };
    }

    try {
      final prompt = '''
You are a food safety expert. Analyze the following list of ingredients for potential interactions or combined effects.

INGREDIENTS:
${ingredientNames.map((i) => '- $i').join('\n')}

Provide analysis in this exact JSON format:
{
  "interactions": ["description of interaction 1", "description of interaction 2"],
  "warnings": ["specific warning 1", "specific warning 2"]
}

Focus only on scientifically validated interactions and effects. If no significant interactions exist, return empty arrays.
''';

      final response = await _callGeminiAPI(prompt);
      final jsonData = _extractJsonFromResponse(response);
      
      // Ensure proper typing for interactions and warnings fields
      List<String> interactions = [];
      List<String> warnings = [];
      
      if (jsonData.containsKey('interactions')) {
        final rawInteractions = jsonData['interactions'];
        if (rawInteractions is List) {
          interactions = rawInteractions.map((item) => item.toString()).toList();
        } else if (rawInteractions is String) {
          interactions = [rawInteractions];
        }
      }
      
      if (jsonData.containsKey('warnings')) {
        final rawWarnings = jsonData['warnings'];
        if (rawWarnings is List) {
          warnings = rawWarnings.map((item) => item.toString()).toList();
        } else if (rawWarnings is String) {
          warnings = [rawWarnings];
        }
      }
      
      return {
        'interactions': interactions,
        'warnings': warnings,
      };
    } catch (e) {
      LoggerService.info('Error in Gemini interaction analysis: $e');
      return {
        'interactions': [],
        'warnings': [],
      };
    }
  }

  /// Perform final verification of product analysis using Gemini AI
  static Future<Map<String, dynamic>> performFinalProductVerification(Map<String, dynamic> productInfo) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = '''
Verify the following product information:
${json.encode(productInfo)}

Check for:
1. Data completeness
2. Accuracy
3. Consistency
4. Safety compliance
5. Regulatory compliance

Provide a structured response with:
- Verification status
- Data quality
- Compliance status
- Recommendations
- Final assessment
''';

      final response = await _callGeminiAPI(prompt);
      return _extractJsonFromResponse(response);
    } catch (e) {
      LoggerService.info('Error in final product verification: $e');
      return {
        'verification_status': 'failed',
        'data_quality': 'poor',
        'compliance_status': 'unknown',
        'recommendations': [],
        'final_assessment': 'Unable to verify product information',
      };
    }
  }

  /// Analyze ingredients for safety using Gemini AI
  static Future<List<AnalyzedIngredient>> analyzeIngredientsWithAI(
    List<String> ingredients,
    Map<String, dynamic>? userPreferences,
  ) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = _buildIngredientAnalysisPrompt(ingredients, userPreferences);
      final response = await _callGeminiAPI(prompt);
      
      return _parseIngredientAnalysisResponse(response, ingredients);
    } catch (e) {
      LoggerService.info('Error in Gemini ingredient analysis: $e');
      rethrow;
    }
  }

  /// Generate comprehensive product safety assessment
  static Future<Map<String, dynamic>> generateProductSafetyAssessment(
    Product product,
    List<AnalyzedIngredient> analyzedIngredients,
    Map<String, dynamic>? userPreferences,
  ) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = _buildSafetyAssessmentPrompt(
        product, 
        analyzedIngredients, 
        userPreferences,
      );
      // Using Flash model for safety assessment
      final response = await _callGeminiAPI(prompt, model: _gemini2FlashModel);
      
      return _parseSafetyAssessmentResponse(response);
    } catch (e) {
      LoggerService.info('Error in Gemini safety assessment: $e');
      rethrow;
    }
  }

  /// Generate personalized recommendations
  static Future<Map<String, dynamic>> generatePersonalizedRecommendations(
    Product product,
    List<AnalyzedIngredient> ingredients,
    Map<String, dynamic>? userPreferences,
    List<String>? allergens,
  ) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = _buildRecommendationsPrompt(
        product,
        ingredients,
        userPreferences,
        allergens,
      );
      final response = await _callGeminiAPI(prompt);

      return _parseRecommendationsResponse(response);
    } catch (e) {
      LoggerService.info('Error in Gemini recommendations: $e');
      rethrow;
    }
  }

  /// Extract barcode from product image using Gemini Vision API
  static Future<String?> extractBarcodeFromImage(String imagePath) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final imageBytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(imageBytes);

      final prompt = _buildBarcodeExtractionPrompt();
      final response = await _callGeminiVisionAPI(prompt, base64Image);

      return _parseBarcodeExtractionResponse(response);
    } catch (e) {
      LoggerService.info('Error extracting barcode from image: $e');
      rethrow;
    }
  }

  /// Find product alternatives using AI
  static Future<List<Map<String, dynamic>>> findProductAlternatives(
    Product product,
    List<AnalyzedIngredient> ingredients,
    Map<String, dynamic>? userPreferences,
  ) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = _buildAlternativesPrompt(product, ingredients, userPreferences);
      final response = await _callGeminiAPI(prompt);
      
      return _parseAlternativesResponse(response);
    } catch (e) {
      LoggerService.info('Error in Gemini alternatives search: $e');
      rethrow;
    }
  }

  /// Search for product information using Gemini API
  static Future<Map<String, dynamic>?> searchProductInformation(String productName, String barcode) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    try {
      final prompt = '''
Search for information about:
Product: $productName
Barcode: $barcode

Focus on:
1. Product details
2. Ingredients
3. Safety information
4. Certifications
5. Brand information

Provide a structured response with:
- Product details
- Ingredients list
- Safety information
- Certifications
- Brand details
''';

      final response = await _callGeminiAPI(prompt);
      
      try {
        // Extract the JSON part of the response
        final jsonData = _extractJsonFromResponse(response);
        return jsonData;
      } catch (e) {
        LoggerService.info('Error parsing Gemini product search response: $e');
        return null;
      }
    } catch (e) {
      LoggerService.info('Error in Gemini product search: $e');
      return null;
    }
  }

  /// Quick safety check for ingredients when coverage is high
  static Future<Map<String, dynamic>> quickIngredientSafetyCheck(List<String> ingredientNames) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    if (ingredientNames.isEmpty) {
      return {
        'ingredients': {},
      };
    }

    try {
      final prompt = '''
Perform a quick safety check for these ingredients:
${ingredientNames.join(', ')}

Focus on:
1. Immediate safety concerns
2. Common allergens
3. High-risk ingredients
4. Regulatory status

Provide a structured response with:
- High-risk ingredients
- Allergens
- Safety warnings
- Quick recommendations
''';

      final response = await _callGeminiAPI(prompt);
      final jsonData = _extractJsonFromResponse(response);
      
      // Ensure the ingredients field exists
      if (!jsonData.containsKey('ingredients')) {
        return {'ingredients': {}};
      }
      
      return jsonData;
    } catch (e) {
      LoggerService.info('Error in quick ingredient safety check: $e');
      return {'ingredients': {}};
    }
  }

  /// Detailed research on ingredients when coverage is partial
  static Future<Map<String, dynamic>> researchIngredientSafety(List<String> ingredientNames) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    if (ingredientNames.isEmpty) {
      return {
        'ingredients': {},
      };
    }

    try {
      final prompt = '''
Research safety information for these ingredients:
${ingredientNames.join(', ')}

Analyze:
1. Scientific studies
2. Regulatory status
3. Health impacts
4. Usage guidelines
5. Safety thresholds

Provide a structured response with:
- Scientific findings
- Regulatory status
- Health impacts
- Usage guidelines
- Safety thresholds
''';

      final response = await _callGeminiAPI(prompt, model: _gemini2FlashModel);
      final jsonData = _extractJsonFromResponse(response);
      
      // Ensure the ingredients field exists
      if (!jsonData.containsKey('ingredients')) {
        return {'ingredients': {}};
      }
      
      return jsonData;
    } catch (e) {
      LoggerService.info('Error researching ingredient safety: $e');
      return {
        'scientific_findings': [],
        'regulatory_status': {},
        'health_impacts': [],
        'usage_guidelines': {},
        'safety_thresholds': {},
      };
    }
  }

  /// Comprehensive deep analysis for ingredients when coverage is minimal
  static Future<Map<String, dynamic>> deepIngredientAnalysis(List<String> ingredientNames) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    if (ingredientNames.isEmpty) {
      return {
        'ingredients': {},
      };
    }

    try {
      final prompt = '''
Perform deep analysis of these ingredients:
${ingredientNames.join(', ')}

Analyze:
1. Chemical composition
2. Processing methods
3. Health impacts
4. Environmental impact
5. Sustainability
6. Alternatives

Provide a structured response with:
- Chemical analysis
- Processing details
- Health assessment
- Environmental impact
- Sustainability score
- Alternative options
''';

      final response = await _callGeminiAPI(prompt, model: _gemini2FlashModel);
      final jsonData = _extractJsonFromResponse(response);
      
      // Process into standard format, preserving additional deep analysis fields
      final Map<String, dynamic> processedData = {'ingredients': {}};
      
      if (jsonData.containsKey('ingredients')) {
        final ingredients = jsonData['ingredients'] as Map<String, dynamic>;
        
        for (final ingredient in ingredients.entries) {
          final name = ingredient.key;
          final data = ingredient.value as Map<String, dynamic>;
          
          // Extract standard fields and add to processed data
          processedData['ingredients'][name] = {
            'safety_level': data['safety_level'] ?? 'yellow',
            'description': data['description'] ?? '',
            'health_risks': data['health_risks'] ?? [],
            'category': data['category'] ?? '',
            'aliases': data['aliases'] ?? [],
            'confidence': data['confidence'] ?? 0.8,
            // Preserve additional deep analysis fields
            'regulatory_status': data['regulatory_status'],
            'population_concerns': data['population_concerns'],
          };
        }
      }
      
      return processedData;
    } catch (e) {
      LoggerService.info('Error in deep ingredient analysis: $e');
      return {'ingredients': {}};
    }
  }

  /// Final AI verification of safety analysis
  static Future<Map<String, dynamic>> verifySafetyAnalysis({
    required List<Map<String, dynamic>> ingredientAnalysis,
    required Map<String, dynamic> criteriaResults,
    required String proposedRating,
    required int proposedScore,
  }) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        return {
          'verified_rating': proposedRating,
          'verified_score': proposedScore,
          'verification_notes': 'Offline mode: Using proposed values',
          'confidence_score': 0.7,
        };
      }
    }

    try {
      final prompt = '''
Verify the following safety analysis:
${json.encode(ingredientAnalysis)}

Check for:
1. Completeness
2. Accuracy
3. Consistency
4. Missing information
5. Potential errors

Provide a structured response with:
- Verification status
- Confidence level
- Missing information
- Suggested improvements
- Final assessment
''';

      final response = await _callGeminiAPI(prompt);
      final jsonData = _extractJsonFromResponse(response);
      
      // Ensure minimum fields exist
      if (!jsonData.containsKey('verified_rating')) {
        jsonData['verified_rating'] = proposedRating;
      }
      
      if (!jsonData.containsKey('verified_score')) {
        jsonData['verified_score'] = proposedScore;
      }
      
      if (!jsonData.containsKey('verification_notes')) {
        jsonData['verification_notes'] = 'Verification completed';
      }
      
      if (!jsonData.containsKey('confidence_score')) {
        jsonData['confidence_score'] = 0.8;
      }
      
      return jsonData;
    } catch (e) {
      LoggerService.info('Error verifying safety analysis: $e');
      return {
        'verified_rating': proposedRating,
        'verified_score': proposedScore,
        'verification_notes': 'Verification failed: ${e.toString()}',
        'confidence_score': 0.5,
      };
    }
  }

  /// Research product information
  static Future<Map<String, dynamic>> researchProduct(String productName) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        return {
          'ingredients': <String>[],
          'ingredients_text': '',
          'nutrition_facts': {},
          'certifications': <String>[],
          'health_claims': <String>[],
        };
      }
    }

    try {
      final prompt = '''
Research the product: $productName
Focus on:
1. Ingredients
2. Safety concerns
3. Certifications
4. Manufacturing process
5. Brand reputation

Provide a structured response with:
- Product details
- Safety assessment
- Certifications
- Manufacturing info
- Brand information
''';

      final response = await _callGeminiAPI(prompt, model: _gemini2FlashModel);
      final jsonData = _extractJsonFromResponse(response);
      
      // Ensure all required fields exist with proper types
      final Map<String, dynamic> typeSafeData = {
        'ingredients': jsonData.containsKey('ingredients') 
            ? List<String>.from(jsonData['ingredients'] ?? []) 
            : <String>[],
        'ingredients_text': jsonData['ingredients_text'] ?? '',
        'nutrition_facts': jsonData['nutrition_facts'] ?? {},
        'certifications': jsonData.containsKey('certifications')
            ? List<String>.from(jsonData['certifications'] ?? [])
            : <String>[],
        'health_claims': jsonData.containsKey('health_claims')
            ? List<String>.from(jsonData['health_claims'] ?? [])
            : <String>[]
      };
      
      return typeSafeData;
    } catch (e) {
      LoggerService.info('Error researching product: $e');
      return {
        'product_details': {},
        'safety_assessment': 'Unable to assess product safety',
        'certifications': [],
        'manufacturing_info': {},
        'brand_info': {},
      };
    }
  }

  /// Analyze nutritional impact of a product
  static Future<Map<String, dynamic>> analyzeNutritionalImpact(Map<String, dynamic> nutritionFacts) async {
    try {
      final prompt = '''
        Analyze the nutritional impact of this product based on its nutrition facts:
        ${json.encode(nutritionFacts)}
        
        Please provide:
        1. Overall nutritional quality assessment
        2. Key nutritional concerns
        3. Health impact analysis
      ''';

      final response = await _callGeminiAPI(prompt);
      final jsonResponse = _extractJsonFromResponse(response);
      
      return {
        'nutritional_quality': jsonResponse['nutritional_quality']?.toString() ?? 'unknown',
        'key_concerns': (jsonResponse['key_concerns'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
        'health_impact': jsonResponse['health_impact']?.toString() ?? 'unknown',
        'confidence': (jsonResponse['confidence'] as num?)?.toDouble() ?? 0.7,
      };
    } catch (e) {
      LoggerService.info('Error analyzing nutritional impact: $e');
      return {
        'nutritional_quality': 'unknown',
        'key_concerns': [],
        'health_impact': 'unknown',
        'confidence': 0.3,
        'error': e.toString(),
      };
    }
  }

  /// Analyze processing methods of ingredients
  static Future<Map<String, dynamic>> analyzeProcessingMethods(List<String> ingredients) async {
    try {
      final prompt = '''
        Analyze the processing methods used for these ingredients:
        ${ingredients.join(', ')}
        
        Please provide:
        1. Processing method assessment
        2. Potential processing concerns
        3. Impact on nutritional value
      ''';

      final response = await _callGeminiAPI(prompt);
      final jsonResponse = _extractJsonFromResponse(response);
      
      return {
        'processing_assessment': jsonResponse['processing_assessment']?.toString() ?? 'unknown',
        'processing_concerns': (jsonResponse['processing_concerns'] as List<dynamic>?)?.map((e) => e.toString()).toList() ?? [],
        'nutritional_impact': jsonResponse['nutritional_impact']?.toString() ?? 'unknown',
        'confidence': (jsonResponse['confidence'] as num?)?.toDouble() ?? 0.7,
      };
    } catch (e) {
      LoggerService.info('Error analyzing processing methods: $e');
      return {
        'processing_assessment': 'unknown',
        'processing_concerns': [],
        'nutritional_impact': 'unknown',
        'confidence': 0.3,
        'error': e.toString(),
      };
    }
  }

  // Private helper methods

  /// Call Gemini API with error handling and rate limiting
  static Future<String> _callGeminiAPI(String prompt, {int retryCount = 0, String? model, bool useRetries = true}) async {
    if (_apiKey == null) {
      await initialize();
      if (_apiKey == null) {
        throw Exception('Gemini API key not configured');
      }
    }

    // Maximum retry attempts
    const maxRetries = 2;
    
    // Check if rate limited models have reset
    final now = DateTime.now();
    if (_isFlashModelRateLimited && now.isAfter(_flashModelRateLimitResetTime)) {
      LoggerService.info('Flash model rate limit timer reset');
      _isFlashModelRateLimited = false;
    }
    
    if (_isProModelRateLimited && now.isAfter(_proModelRateLimitResetTime)) {
      LoggerService.info('Pro model rate limit timer reset');
      _isProModelRateLimited = false;
    }
    
    // Use requested model or default text model
    final requestedModel = model ?? _textModel;

    try {
      final url = Uri.parse('$_baseUrl/models/$requestedModel:generateContent?key=$_apiKey');
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'role': 'user',
              'parts': [{'text': prompt}],
            },
          ],
          'generationConfig': {
            'temperature': 0.2,
            'topP': 0.8,
            'topK': 40,
            'maxOutputTokens': 2048,
          },
        }),
      ).timeout(const Duration(seconds: 20)); // Increased timeout to 20 seconds

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final candidates = data['candidates'] as List<dynamic>;
        
        if (candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          final parts = content['parts'] as List<dynamic>;
          
          if (parts.isNotEmpty) {
            // Request succeeded - after some time, we could reset rate limit flags
            Future.delayed(const Duration(minutes: 5), () {
              // Clear rate limit flags periodically
              _isFlashModelRateLimited = false;
              _isProModelRateLimited = false;
            });
            
            return parts[0]['text'] as String;
          }
        }
        
        throw Exception('No text content in response');
      } else if (response.statusCode == 429) {
        // Rate limit exceeded
        LoggerService.info('Gemini API rate limit exceeded (429) for model $requestedModel. Response: ${response.body}');
        
        // Mark the appropriate model as rate limited for 120 seconds (increased time)
        if (requestedModel == _gemini2FlashModel) {
          _isFlashModelRateLimited = true;
          _flashModelRateLimitResetTime = DateTime.now().add(const Duration(seconds: 120));
          LoggerService.info('Flash model rate limited until ${_flashModelRateLimitResetTime}');
        } else if (requestedModel == _gemini15ProModel) {
          _isProModelRateLimited = true;
          _proModelRateLimitResetTime = DateTime.now().add(const Duration(seconds: 120));
          LoggerService.info('Pro model rate limited until ${_proModelRateLimitResetTime}');
        }
        
        // Try a different model based on what was rate limited
        String? fallbackModel;
        if (_isFlashModelRateLimited && !_isProModelRateLimited) {
          fallbackModel = _gemini15ProModel;
          LoggerService.info('Switching to fallback model: $fallbackModel');
        } else if (_isProModelRateLimited && !_isFlashModelRateLimited) {
          fallbackModel = _gemini2FlashModel;
          LoggerService.info('Switching to fallback model: $fallbackModel');
        } else {
          // If we're out of models, retry with exponential backoff
          if (useRetries && retryCount < maxRetries) {
            final backoffSeconds = (1 << retryCount) * 5; // Increased backoff time (5, 10, 20 seconds)
            LoggerService.info('All models rate limited. Retrying in $backoffSeconds seconds...');
            
            await Future.delayed(Duration(seconds: backoffSeconds));
            return _callGeminiAPI(prompt, retryCount: retryCount + 1);
          }
          
          // If still rate limited after retries, use fallback response
          LoggerService.info('Rate limit exceeded on all models, using fallback response');
          return _generateFallbackResponse(prompt);
        }
        
        // Try with the fallback model if available
        if (fallbackModel != null) {
          // Add a small delay before trying fallback
          await Future.delayed(const Duration(seconds: 2));
          return _callGeminiAPI(prompt, model: fallbackModel);
        } else {
          return _generateFallbackResponse(prompt);
        }
      } else {
        throw Exception('Gemini API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      // Check if it's a rate limit error that we've already retried the maximum times
      if (e.toString().contains('429') && retryCount >= maxRetries) {
        LoggerService.info('Gemini API rate limit error, using fallback');
        return _generateFallbackResponse(prompt);
      }
      
      // Retry on network errors
      if (e is http.ClientException || e is TimeoutException) {
        if (useRetries && retryCount < maxRetries) {
          final backoffSeconds = (1 << retryCount) * 5; // Increased backoff (5, 10, 20 seconds)
          LoggerService.info('Network error, retrying in $backoffSeconds seconds...');
          
          await Future.delayed(Duration(seconds: backoffSeconds));
          return _callGeminiAPI(prompt, retryCount: retryCount + 1, model: model);
        }
      }
      
      // If all retries fail or for other errors, use fallback
      LoggerService.info('Error calling Gemini API: $e');
      return _generateFallbackResponse(prompt);
    }
  }
  
  /// Generate a fallback response when API calls fail
  static String _generateFallbackResponse(String prompt) {
    // Basic response pattern detection
    if (prompt.contains('safety_level') && prompt.contains('health_risks')) {
      // Ingredient assessment fallback
      return '''
{
  "safety_level": "Moderate Concern",
  "description": "Unable to analyze with AI - standard precautionary rating applied",
  "health_risks": ["Unknown - consult product labeling"],
  "source": "Unknown",
  "common_uses": ["Food products"]
}
''';
    } else if (prompt.contains('personalized_recommendations') || prompt.contains('safety_analysis')) {
      // Product assessment fallback
      return '''
{
  "safety_rating": "Moderate Concern",
  "explanation": "AI analysis unavailable - using basic assessment",
  "recommendations": ["Review ingredients manually", "Check for allergens on label"],
  "potential_concerns": ["Unable to analyze ingredients fully"]
}
''';
    } else {
      // Generic fallback
      return '''
{
  "status": "limited_service",
  "message": "AI analysis temporarily unavailable - using simplified assessment"
}
''';
    }
  }
  
  /// Generate offline ingredient response
  static Map<String, dynamic> _generateOfflineIngredientResponse(String ingredientName) {
    return {
      "safety_level": "Moderate Concern",
      "description": "Analysis not available - please check again later",
      "health_risks": ["Unable to assess - service unavailable"],
      "source": "Unknown",
      "common_uses": ["Food products"]
    };
  }

  static String _buildIngredientAnalysisPrompt(
    List<String> ingredients, 
    Map<String, dynamic>? userPreferences,
  ) {
    final allergens = userPreferences?['allergens'] as List<String>? ?? [];
    final avoidedIngredients = userPreferences?['avoided_ingredients'] as List<String>? ?? [];
    
    return '''
You are a food safety expert and nutritionist. Analyze the following ingredients for their safety and health implications.

INGREDIENTS TO ANALYZE:
${ingredients.map((i) => '- $i').join('\n')}

USER PREFERENCES:
- Allergens to avoid: ${allergens.isEmpty ? 'None specified' : allergens.join(', ')}
- Ingredients to avoid: ${avoidedIngredients.isEmpty ? 'None specified' : avoidedIngredients.join(', ')}

For each ingredient, provide analysis in this exact JSON format:
{
  "ingredients": [
    {
      "name": "ingredient_name",
      "safety_level": "green|yellow|red",
      "safety_score": 0-100,
      "description": "Brief description of the ingredient",
      "health_risks": ["list", "of", "specific", "health", "risks"],
      "benefits": ["list", "of", "health", "benefits"],
      "category": "preservative|colorant|flavoring|nutrient|emulsifier|sweetener|other",
      "allergen_risk": "none|low|medium|high",
      "user_specific_concerns": ["concerns", "based", "on", "user", "preferences"],
      "confidence": 0.0-1.0
    }
  ]
}

SAFETY LEVELS:
- GREEN (80-100): Generally safe, minimal health concerns
- YELLOW (50-79): Use with caution, moderate concerns or limited research
- RED (0-49): High health risks, recommend avoiding

Consider:
1. Scientific research on health effects
2. FDA/regulatory status
3. User's specific allergens and preferences
4. Dosage and typical consumption levels
5. Vulnerable populations (children, pregnant women)
6. Processing methods and additives

Provide ONLY the JSON response, no additional text.
''';
  }

  static String _buildSafetyAssessmentPrompt(
    Product product,
    List<AnalyzedIngredient> analyzedIngredients,
    Map<String, dynamic>? userPreferences,
  ) {
    final ingredientsSummary = analyzedIngredients.map((i) => 
      '${i.name}: ${i.safetyLevel} (${i.safetyScore}/100)'
    ).join('\n');

    return '''
You are a food safety expert. Provide a comprehensive safety assessment for this product.

PRODUCT: ${product.name}
BRAND: ${product.brand ?? 'Unknown'}

ANALYZED INGREDIENTS:
$ingredientsSummary

USER PREFERENCES:
${userPreferences != null ? json.encode(userPreferences) : 'No specific preferences'}

Provide assessment in this exact JSON format:
{
  "overall_safety_score": 0-100,
  "safety_rating": "safe|caution|unsafe",
  "safety_summary": "Brief overall assessment",
  "main_concerns": [
    {
      "concern": "Primary concern description",
      "severity": "low|medium|high",
      "affected_groups": ["general|children|pregnant|elderly|etc"]
    }
  ],
  "health_benefits": ["positive aspects"],
  "recommendations": [
    {
      "type": "usage|alternative|medical",
      "recommendation": "Specific actionable advice"
    }
  ],
  "consumption_advice": {
    "frequency": "daily|weekly|occasional|avoid",
    "portion_guidance": "serving size recommendations",
    "special_considerations": "additional notes"
  },
  "regulatory_status": "FDA approved|Generally recognized as safe|Limited approval|etc",
  "research_confidence": 0.0-1.0
}

Consider:
1. Synergistic effects between ingredients
2. Product category and typical consumption
3. Cumulative exposure from multiple products
4. Individual user risk factors
5. Latest scientific research
6. Regulatory guidelines

Provide ONLY the JSON response.
''';
  }

  static String _buildRecommendationsPrompt(
    Product product,
    List<AnalyzedIngredient> ingredients,
    Map<String, dynamic>? userPreferences,
    List<String>? allergens,
  ) {
    return '''
Generate personalized recommendations for this product based on ingredient analysis and user profile.

PRODUCT: ${product.name}
SAFETY SCORE: ${product.safetyScore}/100

HIGH-RISK INGREDIENTS:
${ingredients.where((i) => i.safetyLevel == 'red').map((i) => i.name).join(', ')}

USER ALLERGENS: ${allergens?.join(', ') ?? 'None'}
USER PREFERENCES: ${userPreferences != null ? json.encode(userPreferences) : 'None'}

Provide recommendations in JSON format:
{
  "should_consume": true|false,
  "consumption_frequency": "daily|weekly|occasionally|never",
  "portion_recommendations": "specific guidance",
  "alternatives": [
    {
      "type": "Similar product with better safety profile",
      "suggestion": "Specific alternative",
      "why_better": "Explanation"
    }
  ],
  "precautions": ["specific precautions to take"],
  "monitor_for": ["symptoms or effects to watch"],
  "consult_doctor_if": ["conditions requiring medical advice"],
  "safer_preparation": ["cooking or preparation tips"],
  "ingredient_swaps": [
    {
      "avoid": "problematic ingredient",
      "use_instead": "safer alternative"
    }
  ]
}

Provide ONLY the JSON response.
''';
  }

  static String _buildAlternativesPrompt(
    Product product,
    List<AnalyzedIngredient> ingredients,
    Map<String, dynamic>? userPreferences,
  ) {
    return '''
Find safer alternative products similar to: ${product.name}

CURRENT PRODUCT ISSUES:
${ingredients.where((i) => i.safetyLevel != 'green').map((i) => '- ${i.name}: ${i.safetyLevel}').join('\n')}

Suggest alternatives in JSON format:
{
  "alternatives": [
    {
      "name": "Alternative product name",
      "brand": "Brand name",
      "safety_score": 80-100,
      "why_better": "Explanation of improvements",
      "similar_benefits": "How it maintains product benefits",
      "availability": "common|specialty|online",
      "price_comparison": "similar|higher|lower",
      "key_improvements": ["specific ingredient improvements"]
    }
  ]
}

Focus on:
1. Products with cleaner ingredient lists
2. Organic or natural alternatives
3. Similar functionality/taste
4. Widely available options
5. Reasonable price points

Provide ONLY the JSON response with realistic product suggestions.
''';
  }

  // Response parsing methods

  static List<AnalyzedIngredient> _parseIngredientAnalysisResponse(
    String response, 
    List<String> originalIngredients,
  ) {
    try {
      final jsonResponse = json.decode(response.trim());
      final ingredientsData = jsonResponse['ingredients'] as List;
      
      return ingredientsData.map((data) => AnalyzedIngredient(
        name: data['name'] as String,
        description: data['description'] as String?,
        safetyLevel: data['safety_level'] as String,
        healthRisks: List<String>.from(data['health_risks'] ?? []),
        aliases: List<String>.from(data['benefits'] ?? []), // Using benefits as aliases for display
        category: data['category'] as String?,
        isMatched: true, // AI analyzed
        confidence: (data['confidence'] as num?)?.toDouble() ?? 0.9,
      )).toList();
    } catch (e) {
      LoggerService.info('Error parsing ingredient analysis: $e');
      // Fallback to original ingredients with basic analysis
      return originalIngredients.map((ingredient) => AnalyzedIngredient(
        name: ingredient,
        description: 'AI analysis failed, using basic assessment',
        safetyLevel: 'yellow',
        healthRisks: ['Analysis incomplete'],
        isMatched: false,
        confidence: 0.3,
      )).toList();
    }
  }

  static Map<String, dynamic> _parseSafetyAssessmentResponse(String response) {
    try {
      return json.decode(response.trim()) as Map<String, dynamic>;
    } catch (e) {
      LoggerService.info('Error parsing safety assessment: $e');
      return {
        'overall_safety_score': 50,
        'safety_rating': 'caution',
        'safety_summary': 'AI analysis failed, manual review recommended',
        'main_concerns': [],
        'recommendations': [],
      };
    }
  }

  static Map<String, dynamic> _parseRecommendationsResponse(String response) {
    try {
      return json.decode(response.trim()) as Map<String, dynamic>;
    } catch (e) {
      LoggerService.info('Error parsing recommendations: $e');
      return {
        'should_consume': null,
        'consumption_frequency': 'consult_expert',
        'alternatives': [],
        'precautions': ['AI analysis failed, consult healthcare provider'],
      };
    }
  }

  static List<Map<String, dynamic>> _parseAlternativesResponse(String response) {
    try {
      final jsonResponse = json.decode(response.trim());
      return List<Map<String, dynamic>>.from(jsonResponse['alternatives'] ?? []);
    } catch (e) {
      LoggerService.info('Error parsing alternatives: $e');
      return [];
    }
  }

  /// Build prompt for barcode extraction from image
  static String _buildBarcodeExtractionPrompt() {
    return '''
Analyze this product image and extract any barcode numbers visible in the image.

Look for:
1. UPC barcodes (12-13 digits)
2. EAN barcodes (8 or 13 digits)
3. Code 128 barcodes
4. QR codes containing product information
5. Any other product identification numbers

Return ONLY the barcode number as a plain string. If multiple barcodes are found, return the longest/most complete one.
If no barcode is found, return "NO_BARCODE_FOUND".

Examples of valid responses:
- 123456789012
- 1234567890123
- 12345678
- NO_BARCODE_FOUND

Do not include any explanatory text, just the barcode number or "NO_BARCODE_FOUND".
''';
  }

  /// Parse barcode extraction response
  static String? _parseBarcodeExtractionResponse(String response) {
    try {
      final cleanResponse = response.trim().replaceAll(RegExp(r'[^\d]'), '');

      // Check if response indicates no barcode found
      if (response.toUpperCase().contains('NO_BARCODE_FOUND') ||
          response.toUpperCase().contains('NOT FOUND') ||
          response.toUpperCase().contains('NO BARCODE') ||
          cleanResponse.isEmpty) {
        return null;
      }

      // Validate barcode length (common barcode formats)
      if (cleanResponse.length >= 8 && cleanResponse.length <= 14) {
        LoggerService.info('Extracted barcode from image: $cleanResponse');
        return cleanResponse;
      }

      LoggerService.info('Invalid barcode length: ${cleanResponse.length} for barcode: $cleanResponse');
      return null;
    } catch (e) {
      LoggerService.info('Error parsing barcode extraction response: $e');
      return null;
    }
  }

  /// Call Gemini Vision API for image analysis
  static Future<String> _callGeminiVisionAPI(String prompt, String base64Image, {int retryCount = 0}) async {
    final url = Uri.parse('$_baseUrl/models/$_visionModel:generateContent?key=$_apiKey');

    final payload = json.encode({
      'contents': [
        {
          'role': 'user',
          'parts': [
            {'text': prompt},
            {
              'inline_data': {
                'mime_type': 'image/jpeg',
                'data': base64Image
              }
            }
          ]
        }
      ],
      'generationConfig': {
        'temperature': 0.1, // Low temperature for precise barcode extraction
        'topP': 0.8,
        'topK': 40,
        'maxOutputTokens': 100, // Short response expected
      }
    });

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: payload,
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final candidates = data['candidates'] as List<dynamic>;

        if (candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          final parts = content['parts'] as List<dynamic>;

          if (parts.isNotEmpty) {
            return parts[0]['text'] as String;
          }
        }

        throw Exception('No text content in vision API response');
      } else if (response.statusCode == 429) {
        // Rate limit - retry once after delay
        if (retryCount < 1) {
          await Future.delayed(const Duration(seconds: 5));
          return _callGeminiVisionAPI(prompt, base64Image, retryCount: retryCount + 1);
        }
        throw Exception('Gemini Vision API rate limit exceeded');
      } else {
        throw Exception('Gemini Vision API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      LoggerService.error('Error calling Gemini Vision API: $e');

      // Retry once on network errors
      if (retryCount < 1 && (e is http.ClientException || e is TimeoutException)) {
        await Future.delayed(const Duration(seconds: 2));
        return _callGeminiVisionAPI(prompt, base64Image, retryCount: retryCount + 1);
      }

      rethrow;
    }
  }
}