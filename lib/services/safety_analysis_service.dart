import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:synchronized/synchronized.dart';
import '../models/product.dart';
import '../models/safety_analysis_result.dart';
import '../constants/safety_criteria.dart';
import '../utils/json_parser.dart';
import 'supabase_init_service.dart';
import 'gemini_service.dart';
import 'package:http/http.dart' as http;
import 'supabase_service.dart';
import 'logger_service.dart';

/// Service to handle product safety analysis with the current database structure
/// but also leveraging MCP tables when available for enhanced functionality
class SafetyAnalysisService {
  final SupabaseService supabaseService;
  SafetyAnalysisService(this.supabaseService);

  // Use a lock for MCP tables check
  static final _mcpCheckLock = Lock();
  static bool _mcpTablesAvailable = false;
  static bool _checkedMcpTables = false;
  
  /// Check if MCP tables are available with proper synchronization
  static Future<bool> _checkMcpTables() async {
    return await _mcpCheckLock.synchronized(() async {
      if (!_checkedMcpTables) {
        _mcpTablesAvailable = await SupabaseInitService.checkMcpTables();
        _checkedMcpTables = true;
      }
      return _mcpTablesAvailable;
    });
  }
  
  /// Analyze a product's ingredients for safety concerns
  /// Returns a SafetyAnalysisResult object with detailed analysis
  static Future<SafetyAnalysisResult> analyzeProduct(Product product, {String? userId}) async {
    // Check for MCP tables if we haven't already
    final mcpAvailable = await _checkMcpTables();
    
    try {
      // Generate a request ID for tracking
      final requestId = const Uuid().v4();
      LoggerService.info('Starting safety analysis for ${product.name} (RequestID: $requestId)');
      
      // Enhanced validation of input product
      if (!_validateProductInput(product)) {
        throw Exception('Invalid product data for analysis');
      }
      
      // Check if this is a test product
      final bool isTestProduct = _isTestProduct(product.id);
      if (isTestProduct) {
        LoggerService.info('Analyzing test product: ${product.id} - Using local analysis only');
      }
      
      // Step 1: Check Database Cache with enhanced validation
      SafetyAnalysisResult? cachedAnalysis;
      if (mcpAvailable && product.barcode != null && !isTestProduct) {
        cachedAnalysis = await _checkAnalysisCache(product.barcode!);
        
        if (cachedAnalysis != null) {
          // Enhanced ingredient validation
          final ingredientsMatch = await _verifyIngredientsMatch(cachedAnalysis);
          final safetyScoreValid = await _validateSafetyScore(cachedAnalysis);
          
          if (ingredientsMatch && safetyScoreValid) {
            LoggerService.info('Using validated cached analysis for ${product.barcode}');
            return cachedAnalysis;
          }
          LoggerService.info('Cached analysis found but requires fresh analysis due to validation failure');
        }
      }
      
      // Step 2: Enhanced Parallel Data Gathering with retry mechanism
      final futures = await Future.wait([
        _getOpenFoodFactsData(product).timeout(
          const Duration(seconds: 10),
          onTimeout: () => _getFallbackProductData(product),
        ),
        _getGeminiData(product).timeout(
          const Duration(seconds: 15),
          onTimeout: () => _getFallbackAIAnalysis(product),
        ),
        _getWebResearchData(product).timeout(
          const Duration(seconds: 20),
          onTimeout: () => _getFallbackWebData(product),
        ),
      ]);
      
      final openFoodFactsData = futures[0] as Map<String, dynamic>;
      final geminiData = futures[1] as Map<String, dynamic>;
      final webResearchData = futures[2] as Map<String, dynamic>;
      
      // Step 3: Enhanced Data Convergence & Analysis
      final analysisResult = await _convergeAndAnalyzeData(
        product: product,
        openFoodFactsData: openFoodFactsData,
        geminiData: geminiData,
        webResearchData: webResearchData,
      );
      
      // Step 4: Create SafetyAnalysisResult with enhanced validation
      final result = SafetyAnalysisResult(
        productId: product.id,
        barcode: product.barcode,
        productName: product.name,
        brand: product.brand,
        overallScore: _calculateValidatedSafetyScore(analysisResult),
        safetyRating: _getSafetyRating(_calculateValidatedSafetyScore(analysisResult)),
        criteriaResults: _validateAndCleanCriteriaResults(analysisResult['safety_analysis']['criteria_results'] as Map),
        violatingIngredients: _validateAndCleanViolatingIngredients(
          analysisResult['safety_analysis']['violating_ingredients'] as Map
        ),
        analysis: analysisResult,
        requestId: requestId,
        userId: userId,
      );
      
      // Step 5: Store Results with enhanced error handling
      if (mcpAvailable && product.barcode != null && !isTestProduct) {
        await _storeAnalysisInCache(result);
        await _logScanEvent(product.barcode!, requestId, userId ?? 'anonymous');
      }
      
      // Step 6: Update Product with enhanced validation
      if (!isTestProduct) {
        await _updateProductSafetyScore(product, result);
      }
      
      return result;
    } catch (e) {
      LoggerService.error('Error in safety analysis: $e');
      return _createFallbackResult(product, userId, e.toString());
    }
  }
  
  /// Enhanced validation of product input
  static bool _validateProductInput(Product product) {
    if (product.name.isEmpty) return false;
    if (product.id.isEmpty) return false;
    if (product.ingredientsJson == null || product.ingredientsJson!.isEmpty) return false;
    return true;
  }
  
  /// Enhanced safety score validation
  static Future<bool> _validateSafetyScore(SafetyAnalysisResult result) async {
    if (result.overallScore < 0 || result.overallScore > 100) return false;
    if (!['excellent', 'good', 'fair', 'poor', 'unsafe'].contains(result.safetyRating)) return false;
    return true;
  }
  
  /// Calculate validated safety score
  static int _calculateValidatedSafetyScore(Map<String, dynamic> analysisResult) {
    final rawScore = analysisResult['safety_analysis']['overall_score'] as int? ?? 50;
    return rawScore.clamp(0, 100);
  }
  
  /// Validate and clean criteria results
  static Map<String, dynamic> _validateAndCleanCriteriaResults(Map criteriaResults) {
    final cleanedResults = <String, dynamic>{};
    for (final entry in criteriaResults.entries) {
      if (entry.value is bool || entry.value is int || entry.value is String) {
        cleanedResults[entry.key] = entry.value;
      }
    }
    return cleanedResults;
  }
  
  /// Validate and clean violating ingredients
  static Map<String, List<String>> _validateAndCleanViolatingIngredients(Map violatingIngredients) {
    final cleanedIngredients = <String, List<String>>{};
    for (final entry in violatingIngredients.entries) {
      if (entry.value is List) {
        cleanedIngredients[entry.key] = (entry.value as List)
            .where((item) => item is String)
            .map((item) => item.toString())
            .toList();
      }
    }
    return cleanedIngredients;
  }
  
  /// Create fallback result when analysis fails
  static SafetyAnalysisResult _createFallbackResult(Product product, String? userId, String error) {
    return SafetyAnalysisResult(
      productId: product.id,
      barcode: product.barcode,
      productName: product.name,
      brand: product.brand,
      overallScore: 50, // Neutral score for unknown
      safetyRating: 'unknown',
      criteriaResults: {},
      violatingIngredients: {},
      analysis: {'error': error},
      requestId: const Uuid().v4(),
      userId: userId,
    );
  }
  
  /// Verify if cached ingredients match current database
  static Future<bool> _verifyIngredientsMatch(SafetyAnalysisResult cachedAnalysis) async {
    try {
      final supabase = SupabaseInitService.client;
      
      // Get current ingredients from database
      final response = await supabase
          .from('products')
          .select('ingredients_json')
          .eq('id', cachedAnalysis.productId)
          .maybeSingle();
      
      if (response == null) return false;
      
      final currentIngredients = response['ingredients_json'] as Map<String, dynamic>?;
      if (currentIngredients == null) return false;
      
      // Compare with cached ingredients
      final cachedIngredients = cachedAnalysis.analysis['ingredients'] as List<dynamic>?;
      if (cachedIngredients == null) return false;
      
      // Simple comparison - can be enhanced for better matching
      return currentIngredients['ingredients'].length == cachedIngredients.length;
    } catch (e) {
      LoggerService.error('Error verifying ingredients match: $e');
      return false;
    }
  }
  
  /// Get detailed insights about a product using Gemini models
  static Future<Map<String, dynamic>> getAIInsights(Product product) async {
    try {
      // Generate prompt for analysis
      String ingredientsText = "Not available";
      if (product.ingredients != null) {
        ingredientsText = product.ingredients!.map((i) => i.name).join(', ');
      }
      
      final String prompt = '''
        Analyze the safety of this product for human consumption:
        Name: ${product.name}
        Ingredients: $ingredientsText
        
        Please provide:
        1. An overall safety assessment
        2. Any concerning ingredients and why they might be problematic
        3. Healthier alternatives if any
      ''';
      
      // Use Gemini-1.5-pro for complex reasoning
      final response = await GeminiService.researchProduct(product.name);
      
      // Store AI analysis in cache if MCP tables are available
      final mcpAvailable = await _checkMcpTables();
      if (mcpAvailable && product.barcode != null) {
        await _storeAIInsightsInCache(product.barcode!, response);
      }
      
      return response;
    } catch (e) {
      LoggerService.error('Error getting AI insights: $e');
      return {
        'error': 'Failed to get AI insights: ${e.toString()}',
        'safety_assessment': 'Analysis not available',
      };
    }
  }
  
  /// Update the product's safety score in the database
  static Future<void> _updateProductSafetyScore(Product product, SafetyAnalysisResult result) async {
    try {
      // Skip database update for test products or invalid UUIDs
      if (_isTestProduct(product.id)) {
        LoggerService.info('Skipping database update for test product: ${product.id}');
        return;
      }
      
      final supabase = SupabaseInitService.client;
      
      // Calculate concerns count
      final concernsCount = result.violatingIngredients.length;
      
      // Prepare update data
      final updateData = {
        'safety_score': result.overallScore,
        'safety_rating': result.safetyRating,
        'concerns_count': concernsCount,
        'match_stats_json': {
          'confidence_score': 0.9,
          'data_sources': ['app_analysis'],
          'last_updated': DateTime.now().toIso8601String(),
        },
        'nutrition_concerns': json.encode({
          'violating_ingredients': result.violatingIngredients,
          'criteria_results': result.criteriaResults,
          'timestamp': DateTime.now().toIso8601String(),
        }),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      // Update the product
      await supabase
          .from('products')
          .update(updateData)
          .eq('id', product.id);
      
      LoggerService.info('Updated product safety score to ${result.overallScore} with $concernsCount concerns');
      
      // Also update scan_results for consistency
      try {
        await supabase
            .from('scan_results')
            .insert({
              'product_id': product.id,
              'barcode': product.barcode,
              'product_name': product.name,
              'brand': product.brand,
              'scan_data_json': {
                'safety_score': result.overallScore,
                'safety_rating': result.safetyRating,
                'criteria_results': result.criteriaResults,
                'violating_ingredients': result.violatingIngredients,
                'timestamp': DateTime.now().toIso8601String(),
              },
              'scan_date': DateTime.now().toIso8601String(),
              'source': 'app_analysis',
              'confidence_score': 0.9,
              'user_id': product.userId,
              'created_at': DateTime.now().toIso8601String(),
              'updated_at': DateTime.now().toIso8601String(),
            });
      } catch (scanError) {
        LoggerService.error('Error updating scan_results: $scanError');
      }
    } catch (e) {
      LoggerService.error('Error updating product safety score: $e');
    }
  }
  
  /// Check if a product ID indicates a test product
  static bool _isTestProduct(String id) {
    // Test product patterns
    if (id.startsWith('test-') || 
        id == 'test-id' || 
        id.contains('test') || 
        id.length < 10 ||
        !_isValidUuid(id)) {
      return true;
    }
    return false;
  }
  
  /// Check if a string is a valid UUID
  static bool _isValidUuid(String str) {
    try {
      // Use UUID validation pattern
      final uuidPattern = RegExp(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        caseSensitive: false
      );
      return uuidPattern.hasMatch(str);
    } catch (e) {
      return false;
    }
  }
  
  /// Check for cached analysis in MCP tables
  static Future<SafetyAnalysisResult?> _checkAnalysisCache(String barcode) async {
    final mcpAvailable = await _checkMcpTables();
    
    if (!mcpAvailable) return null;
    
    try {
      final supabase = SupabaseInitService.client;
      
      // Try to fetch from scan_results
      final response = await supabase
          .from('scan_results')
          .select()
          .eq('barcode', barcode)
          .order('scan_date', ascending: false)
          .limit(1)
          .maybeSingle();
      
      if (response != null) {
        // Extract data from scan_data_json
        final scanData = response['scan_data_json'] as Map<String, dynamic>;
        
        return SafetyAnalysisResult(
          productId: response['product_id'],
          barcode: barcode,
          productName: response['product_name'] ?? 'Unknown Product',
          brand: response['brand'],
          overallScore: scanData['safety_score'] as int? ?? 50,
          safetyRating: scanData['safety_rating'] as String? ?? 'unknown',
          criteriaResults: Map<String, dynamic>.from(scanData['criteria_results'] as Map? ?? {}),
          violatingIngredients: Map<String, List<String>>.from(
            scanData['violating_ingredients'] as Map? ?? {}
          ),
          analysis: scanData,
          requestId: response['id'],
        );
      }
      
      // If no scan_results, try product_analysis_fallback
      final fallbackResponse = await supabase
          .from('product_analysis_fallback')
          .select()
          .eq('barcode', barcode)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();
      
      if (fallbackResponse != null) {
        final analysisData = fallbackResponse['analysis_data'] as Map<String, dynamic>;
        
        return SafetyAnalysisResult(
          productId: fallbackResponse['id'],
          barcode: barcode,
          productName: fallbackResponse['product_name'] ?? 'Unknown Product',
          overallScore: analysisData['safety_score'] as int? ?? 50,
          safetyRating: analysisData['safety_rating'] as String? ?? 'unknown',
          criteriaResults: Map<String, dynamic>.from(analysisData['criteria_results'] as Map? ?? {}),
          violatingIngredients: Map<String, List<String>>.from(
            analysisData['violating_ingredients'] as Map? ?? {}
          ),
          analysis: analysisData,
          requestId: fallbackResponse['id'],
        );
      }
    } catch (e) {
      LoggerService.error('Error checking analysis cache: $e');
    }
    
    return null;
  }
  
  /// Store analysis in MCP cache with proper JSON handling
  static Future<void> _storeAnalysisInCache(SafetyAnalysisResult result) async {
    final mcpAvailable = await _checkMcpTables();
    
    if (!mcpAvailable || result.barcode == null) return;
    
    try {
      final supabase = SupabaseInitService.client;
      
      // Step 1: Store in scan_results table
      final scanData = {
        'product_id': result.productId,
        'barcode': result.barcode!,
        'product_name': result.productName,
        'brand': result.brand,
        'scan_data_json': {
          'safety_score': result.overallScore,
          'safety_rating': result.safetyRating,
          'criteria_results': result.criteriaResults,
          'violating_ingredients': result.violatingIngredients,
          'ingredients': result.analysis['ingredients'] ?? [],
          'certifications': result.analysis['certifications'] ?? [],
          'timestamp': DateTime.now().toIso8601String(),
        },
        'scan_date': DateTime.now().toIso8601String(),
        'source': 'app_analysis',
        'confidence_score': result.analysis['confidence_score'] ?? 0.9,
        'user_id': result.userId,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      // Validate data structure before inserting
      final schema = {
        'product_id': String,
        'barcode': String,
        'product_name': String,
        'brand': String,
        'scan_data_json': Map,
        'scan_date': String,
        'source': String,
        'confidence_score': double,
        'user_id': String,
        'created_at': String,
        'updated_at': String,
      };
      
      if (SafeJsonParser.validateJsonStructure(scanData, schema)) {
        await supabase
            .from('scan_results')
            .insert(scanData);
        
        LoggerService.info('Stored analysis in scan_results for ${result.barcode}');
      } else {
        throw Exception('Invalid scan data structure');
      }
    } catch (e) {
      LoggerService.error('Error storing analysis in scan_results: $e');
      
      // Step 2: Fallback to product_analysis_fallback table
      try {
        final supabase = SupabaseInitService.client;
        
        if (result.barcode != null) {
          final fallbackData = {
            'barcode': result.barcode!,
            'product_name': result.productName,
            'analysis_data': {
              'safety_score': result.overallScore,
              'safety_rating': result.safetyRating,
              'criteria_results': result.criteriaResults,
              'violating_ingredients': result.violatingIngredients,
              'ingredients': result.analysis['ingredients'] ?? [],
              'certifications': result.analysis['certifications'] ?? [],
              'timestamp': DateTime.now().toIso8601String(),
            },
            'source': 'app_analysis_fallback',
            'user_id': result.userId,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          };
          
          if (SafeJsonParser.validateJsonStructure(fallbackData, {
            'barcode': String,
            'product_name': String,
            'analysis_data': Map,
            'source': String,
            'user_id': String,
            'created_at': String,
            'updated_at': String,
          })) {
            await supabase
                .from('product_analysis_fallback')
                .insert(fallbackData);
            LoggerService.info('Stored analysis in product_analysis_fallback table');
          }
        }
      } catch (fallbackError) {
        LoggerService.error('Error storing analysis in product_analysis_fallback: $fallbackError');
        
        // Step 3: Final fallback to products table
        try {
          final supabase = SupabaseInitService.client;
          
          if (result.barcode != null) {
            final updateData = {
              'safety_score': result.overallScore,
              'safety_rating': result.safetyRating,
              'ingredients_json': {
                'ingredients': result.analysis['ingredients'] ?? [],
                'violating_ingredients': result.violatingIngredients,
                'criteria_results': result.criteriaResults,
                'timestamp': DateTime.now().toIso8601String(),
              },
              'updated_at': DateTime.now().toIso8601String(),
            };
            
            if (SafeJsonParser.validateJsonStructure(updateData, {
              'safety_score': int,
              'safety_rating': String,
              'ingredients_json': Map,
              'updated_at': String,
            })) {
              await supabase
                  .from('products')
                  .update(updateData)
                  .eq('barcode', result.barcode!);
              LoggerService.info('Stored analysis in products table as final fallback');
            }
          }
        } catch (finalFallbackError) {
          LoggerService.error('Error storing analysis in products final fallback: $finalFallbackError');
        }
      }
    }
  }
  
  /// Store AI insights in MCP cache
  static Future<void> _storeAIInsightsInCache(String barcode, Map<String, dynamic> insights) async {
    final mcpAvailable = await _checkMcpTables();
    
    if (!mcpAvailable) return;
    
    try {
      final supabase = SupabaseInitService.client;
      
      // Use products table to store insights in description field
      await supabase
          .from('products')
          .update({
            'description': json.encode({
              'ai_insights': insights,
              'timestamp': DateTime.now().toIso8601String(),
            }),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('barcodes', barcode);
      
      LoggerService.info('Stored AI insights in products.description for $barcode');
    } catch (e) {
      LoggerService.error('Error storing AI insights: $e');
    }
  }
  
  /// Log scan event in MCP tables
  static Future<void> _logScanEvent(String barcode, String requestId, String userId) async {
    final mcpAvailable = await _checkMcpTables();
    
    if (!mcpAvailable) return;
    
    try {
      final supabase = SupabaseInitService.client;
      
      // Step 1: Log in scan_results table
      final scanData = {
        'barcode': barcode,
        'product_id': requestId,
        'source': 'app_scan',
        'scan_date': DateTime.now().toIso8601String(),
        'confidence_score': 1.0,
        'user_id': userId,
        'scan_data_json': {
          'scan_type': 'barcode',
          'timestamp': DateTime.now().toIso8601String(),
          'device_info': 'mobile_app',
          'request_id': requestId,
        },
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      await supabase
          .from('scan_results')
          .insert(scanData);
      
      LoggerService.info('Logged scan event for $barcode in scan_results');
    } catch (e) {
      LoggerService.error('Error logging scan event: $e');
      
      // Step 2: Fallback to unknown_barcodes table
      try {
        final supabase = SupabaseInitService.client;
        
        await supabase
            .from('unknown_barcodes')
            .upsert({
              'barcode': barcode,
              'count': 1,
              'first_scan': DateTime.now().toIso8601String(),
              'last_scan': DateTime.now().toIso8601String(),
              'user_id': userId,
              'status': 'pending',
              'request_id': requestId,
            }, onConflict: 'barcode');
        
        LoggerService.info('Logged unknown barcode $barcode');
      } catch (fallbackError) {
        LoggerService.error('Error logging unknown barcode: $fallbackError');
      }
    }
  }
  
  /// Get safety rating based on score
  static String _getSafetyRating(int score) {
    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    if (score >= 30) return 'poor';
    return 'unsafe';
  }
  
  /// Create missing tables required for the service to function
  static Future<void> createRequiredTables() async {
    try {
      final supabase = SupabaseInitService.client;
      
      // Create user_feedback table if it doesn't exist
      try {
        await supabase.rpc('create_user_feedback_table');
        LoggerService.info('Called procedure to create user_feedback table');
      } catch (e) {
        LoggerService.info('Error calling procedure: $e');
        
        // Fallback: Try to create the table directly
        try {
          final query = '''
          CREATE TABLE IF NOT EXISTS user_feedback (
            id SERIAL PRIMARY KEY,
            user_id TEXT,
            product_id TEXT,
            feedback_type TEXT DEFAULT 'product_feedback',
            feedback_data JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          )
          ''';
          
          await supabase.rpc('exec', params: {'query': query});
          LoggerService.info('Created user_feedback table directly');
        } catch (directError) {
          LoggerService.error('Failed to create user_feedback table: $directError');
        }
      }
    } catch (e) {
      LoggerService.error('Error creating required tables: $e');
    }
  }

  /// Merge and analyze data from multiple sources with retry logic
  static Future<Map<String, dynamic>> _convergeAndAnalyzeData({
    required Product product,
    required Map<String, dynamic> openFoodFactsData,
    required Map<String, dynamic> geminiData,
    required Map<String, dynamic> webResearchData,
  }) async {
    int retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        // Merge all data sources
        final mergedData = SafeJsonParser.mergeJsonObjects([
          openFoodFactsData,
          geminiData,
          webResearchData,
          {'product_id': product.id},
          {'barcode': product.barcode},
          {'name': product.name},
        ]);

        // Extract and normalize ingredients
        final normalizedIngredients = await _normalizeIngredients(mergedData);
        
        // If no ingredients found, try alternative sources
        if (normalizedIngredients.isEmpty) {
          LoggerService.info('No ingredients found in primary sources, trying alternatives...');
          final alternativeIngredients = await _extractIngredientsFromAlternativeSources(product);
          if (alternativeIngredients.isNotEmpty) {
            normalizedIngredients.addAll(alternativeIngredients);
          }
        }
        
        // Match against Supabase ingredients table
        final matchedIngredients = await _matchIngredients(normalizedIngredients);
        
        // Analyze ingredient coverage
        final coverage = await _analyzeIngredientCoverage(matchedIngredients);
        
        // Research missing ingredients if needed
        if (coverage['coverage_percentage'] < 80) {
          final missingIngredients = coverage['missing_ingredients'] as List<String>;
          final researchedIngredients = await _researchMissingIngredients(missingIngredients);
          matchedIngredients.addAll(researchedIngredients);
        }
        
        // Apply safety criteria
        final safetyAnalysis = await _applySafetyCriteria(matchedIngredients);
        
        // Validate the analysis results
        if (!_validateAnalysisResults(safetyAnalysis)) {
          throw Exception('Invalid safety analysis results');
        }
        
        // Generate final report
        return {
          'safety_analysis': safetyAnalysis,
          'ingredient_coverage': coverage,
          'matched_ingredients': matchedIngredients,
          'source_data': {
            'open_food_facts': openFoodFactsData,
            'gemini': geminiData,
            'web_research': webResearchData,
          },
        };
      } catch (e) {
        LoggerService.error('Error in data convergence (attempt ${retryCount + 1}): $e');
        retryCount++;
        
        if (retryCount >= maxRetries) {
          throw Exception('Failed to converge and analyze data after $maxRetries attempts: $e');
        }
        
        // Wait before retrying
        await Future.delayed(Duration(seconds: 1 * retryCount));
      }
    }
    
    throw Exception('Failed to converge and analyze data');
  }
  
  /// Normalize ingredient names
  static Future<List<String>> _normalizeIngredients(Map<String, dynamic> data) async {
    final ingredients = <String>[];
    
    try {
      // Extract ingredients from Open Food Facts data
      if (data['source'] == 'open_food_facts' && data['data'] != null) {
        final productData = data['data'] as Map<String, dynamic>;
        if (productData['ingredients_text'] != null) {
          final ingredientsText = productData['ingredients_text'] as String;
          ingredients.addAll(_parseIngredientsList(ingredientsText));
        }
      }
      
      // Extract ingredients from Gemini data
      if (data['source'] == 'gemini' && data['data'] != null) {
        final geminiData = data['data'] as Map<String, dynamic>;
        if (geminiData['ingredients'] != null) {
          if (geminiData['ingredients'] is List) {
            ingredients.addAll((geminiData['ingredients'] as List).map((i) => i.toString()));
          } else if (geminiData['ingredients'] is String) {
            ingredients.addAll(_parseIngredientsList(geminiData['ingredients'] as String));
          }
        }
      }
      
      // Extract ingredients from web research data
      if (data['source'] == 'web_research' && data['data'] != null) {
        final webData = data['data'] as Map<String, dynamic>;
        if (webData['ingredients'] != null) {
          if (webData['ingredients'] is List) {
            ingredients.addAll((webData['ingredients'] as List).map((i) => i.toString()));
          } else if (webData['ingredients'] is String) {
            ingredients.addAll(_parseIngredientsList(webData['ingredients'] as String));
          }
        }
      }
      
      // Normalize ingredient names
      return ingredients.map((i) => _normalizeIngredientName(i)).toList();
    } catch (e) {
      LoggerService.error('Error normalizing ingredients: $e');
      return [];
    }
  }
  
  /// Parse ingredients list from text
  static List<String> _parseIngredientsList(String text) {
    return text
        .split(',')
        .map((i) => i.trim())
        .where((i) => i.isNotEmpty)
        .toList();
  }
  
  /// Normalize ingredient name
  static String _normalizeIngredientName(String name) {
    return name
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Extract ingredients from alternative sources
  static Future<List<String>> _extractIngredientsFromAlternativeSources(Product product) async {
    final ingredients = <String>[];
    
    try {
      // Try to extract from product description
      if (product.description != null) {
        final descriptionIngredients = _extractIngredientsFromText(product.description!);
        ingredients.addAll(descriptionIngredients);
      }
      
      // Try to extract from product name
      final nameIngredients = _extractIngredientsFromText(product.name);
      ingredients.addAll(nameIngredients);
      
      // Try to extract from brand
      if (product.brand != null) {
        final brandIngredients = _extractIngredientsFromText(product.brand!);
        ingredients.addAll(brandIngredients);
      }
    } catch (e) {
      LoggerService.error('Error extracting ingredients from alternative sources: $e');
    }
    
    return ingredients;
  }

  /// Extract ingredients from text
  static List<String> _extractIngredientsFromText(String text) {
    final ingredients = <String>[];
    
    try {
      // Common ingredient patterns
      final patterns = [
        RegExp(r'contains:?\s*([^\.]+)', caseSensitive: false),
        RegExp(r'ingredients:?\s*([^\.]+)', caseSensitive: false),
        RegExp(r'made with:?\s*([^\.]+)', caseSensitive: false),
      ];
      
      for (final pattern in patterns) {
        final match = pattern.firstMatch(text);
        if (match != null && match.groupCount >= 1) {
          final ingredientsText = match.group(1)!;
          final ingredientsList = ingredientsText
              .split(',')
              .map((i) => i.trim())
              .where((i) => i.isNotEmpty)
              .toList();
          ingredients.addAll(ingredientsList);
        }
      }
    } catch (e) {
      LoggerService.error('Error extracting ingredients from text: $e');
    }
    
    return ingredients;
  }

  /// Validate safety analysis results
  static bool _validateAnalysisResults(Map<String, dynamic> analysis) {
    try {
      // Check required fields
      if (!analysis.containsKey('overall_score') ||
          !analysis.containsKey('safety_rating') ||
          !analysis.containsKey('criteria_results') ||
          !analysis.containsKey('violating_ingredients')) {
        return false;
      }
      
      // Validate score range
      final score = analysis['overall_score'] as int;
      if (score < 0 || score > 100) {
        return false;
      }
      
      // Validate safety rating
      final rating = analysis['safety_rating'] as String;
      if (!['excellent', 'good', 'fair', 'poor', 'unsafe', 'unknown'].contains(rating)) {
        return false;
      }
      
      // Validate criteria results
      final criteriaResults = analysis['criteria_results'] as Map<String, dynamic>;
      if (criteriaResults.isEmpty) {
        return false;
      }
      
      return true;
    } catch (e) {
      LoggerService.error('Error validating analysis results: $e');
      return false;
    }
  }

  /// Match ingredients against database
  static Future<List<Map<String, dynamic>>> _matchIngredients(List<String> ingredients) async {
    final matchedIngredients = <Map<String, dynamic>>[];
    
    try {
      final supabase = SupabaseInitService.client;
      
      for (final ingredient in ingredients) {
        // Try to find exact match
        final response = await supabase
            .from('ingredients')
            .select()
            .ilike('name', ingredient)
            .maybeSingle();
        
        if (response != null) {
          matchedIngredients.add(response);
          continue;
        }
        
        // Try to find in aliases
        final aliasResponse = await supabase
            .from('ingredients')
            .select()
            .contains('common_aliases', [ingredient])
            .maybeSingle();
        
        if (aliasResponse != null) {
          matchedIngredients.add(aliasResponse);
        } else {
          // Add as unknown ingredient
          matchedIngredients.add({
            'name': ingredient,
            'safety_level': 'unknown',
            'status': 'pending_research',
          });
        }
      }
    } catch (e) {
      LoggerService.error('Error matching ingredients: $e');
    }
    
    return matchedIngredients;
  }

  /// Analyze ingredient coverage with improved data structure
  static Future<Map<String, dynamic>> _analyzeIngredientCoverage(List<Map<String, dynamic>> matchedIngredients) async {
    if (matchedIngredients.isEmpty) {
      return {
        'coverage_level': 'none',
        'coverage_percentage': 0.0,
        'total_ingredients': 0,
        'matched_ingredients': 0,
        'missing_ingredients': [],
        'needs_research': false,
      };
    }
    
    final totalIngredients = matchedIngredients.length;
    final matchedCount = matchedIngredients.where((i) => i['matched'] == true).length;
    final coveragePercentage = (matchedCount / totalIngredients) * 100;
    
    String coverageLevel;
    if (coveragePercentage >= 80) {
      coverageLevel = 'high';
    } else if (coveragePercentage >= 50) {
      coverageLevel = 'medium';
    } else if (coveragePercentage > 0) {
      coverageLevel = 'low';
    } else {
      coverageLevel = 'none';
    }
    
    final missingIngredients = matchedIngredients
        .where((i) => i['matched'] != true)
        .map((i) => i['name'] as String)
        .toList();
    
    return {
      'coverage_level': coverageLevel,
      'coverage_percentage': coveragePercentage,
      'total_ingredients': totalIngredients,
      'matched_ingredients': matchedCount,
      'missing_ingredients': missingIngredients,
      'needs_research': coveragePercentage < 80,
    };
  }

  /// Research missing ingredients
  static Future<List<Map<String, dynamic>>> _researchMissingIngredients(List<String> ingredients) async {
    final researchedIngredients = <Map<String, dynamic>>[];
    
    try {
      for (final ingredient in ingredients) {
        // Try Gemini research first
        final geminiData = await GeminiService.researchProduct(ingredient);
        
        if (geminiData != null) {
          researchedIngredients.add({
            'name': ingredient,
            'safety_level': geminiData['safety_level'] ?? 'unknown',
            'description': geminiData['description'],
            'source': 'gemini',
          });
          continue;
        }
        
        // Fallback to web research
        final webData = await _webResearchIngredient(ingredient);
        if (webData != null) {
          researchedIngredients.add({
            'name': ingredient,
            'safety_level': webData['safety_level'] ?? 'unknown',
            'description': webData['description'],
            'source': 'web_research',
          });
        }
      }
    } catch (e) {
      LoggerService.error('Error researching ingredients: $e');
    }
    
    return researchedIngredients;
  }

  /// Web research for an ingredient
  static Future<Map<String, dynamic>?> _webResearchIngredient(String ingredient) async {
    try {
      // Implement web research logic here
      // For now, return a basic structure
      return {
        'safety_level': 'unknown',
        'description': 'No web research data available',
      };
    } catch (e) {
      LoggerService.error('Error in web research: $e');
      return null;
    }
  }

  /// Apply safety criteria to ingredients
  static Future<Map<String, dynamic>> _applySafetyCriteria(List<Map<String, dynamic>> ingredients) async {
    try {
      final criteriaResults = <String, dynamic>{};
      var totalScore = 0;
      var criteriaCount = 0;
      
      // Apply each safety criterion
      for (final entry in SafetyCriteria.criteria.entries) {
        final criterion = entry.value;
        final result = await SafetyCriteria.evaluateCriterion(criterion, ingredients);
        criteriaResults[entry.key] = result;
        
        if (result['score'] != null) {
          totalScore += result['score'] as int;
          criteriaCount++;
        }
      }
      
      // Calculate overall score
      final overallScore = criteriaCount > 0 ? (totalScore / criteriaCount).round() : 0;
      
      return {
        'criteria_results': criteriaResults,
        'overall_score': overallScore,
        'safety_rating': _getSafetyRating(overallScore),
        'violating_ingredients': _getViolatingIngredients(ingredients, criteriaResults),
      };
    } catch (e) {
      LoggerService.error('Error applying safety criteria: $e');
      return {
        'error': e.toString(),
        'overall_score': 0,
        'safety_rating': 'unknown',
      };
    }
  }

  /// Get violating ingredients from criteria results
  static Map<String, List<String>> _getViolatingIngredients(
    List<Map<String, dynamic>> ingredients,
    Map<String, dynamic> criteriaResults,
  ) {
    final violations = <String, List<String>>{};
    
    for (final criterion in criteriaResults.keys) {
      final result = criteriaResults[criterion];
      if (result['violations'] != null && result['violations'] is List) {
        violations[criterion] = List<String>.from(result['violations']);
      }
    }
    
    return violations;
  }

  /// Get data from Open Food Facts
  static Future<Map<String, dynamic>> _getOpenFoodFactsData(Product product) async {
    try {
      if (product.barcode == null) return {};
      
      final response = await http.get(
        Uri.parse('https://world.openfoodfacts.org/api/v0/product/${product.barcode}.json'),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1 && data['product'] != null) {
          return {
            'source': 'open_food_facts',
            'data': data['product'],
          };
        }
      }
    } catch (e) {
      LoggerService.error('Error fetching Open Food Facts data: $e');
    }
    
    return {'source': 'open_food_facts', 'error': 'No data available'};
  }

  /// Get data from Gemini
  static Future<Map<String, dynamic>> _getGeminiData(Product product) async {
    try {
      final response = await GeminiService.researchProduct(product.name);
      return {
        'source': 'gemini',
        'data': response,
      };
    } catch (e) {
      LoggerService.error('Error fetching Gemini data: $e');
      return {'source': 'gemini', 'error': e.toString()};
    }
  }

  /// Get data from web research
  static Future<Map<String, dynamic>> _getWebResearchData(Product product) async {
    try {
      // Implement web research logic here
      return {
        'source': 'web_research',
        'data': {},
      };
    } catch (e) {
      LoggerService.error('Error fetching web research data: $e');
      return {'source': 'web_research', 'error': e.toString()};
    }
  }

  Future<List<Map<String, dynamic>>> _evaluateSafetyCriteria(
    List<Map<String, dynamic>> ingredients,
  ) async {
    final results = <Map<String, dynamic>>[];
    
    for (final entry in SafetyCriteria.criteria.entries) {
      final criterion = entry.value;
      final result = await SafetyCriteria.evaluateCriterion(criterion, ingredients);
      results.add(result);
    }
    
    return results;
  }

  /// Get fallback product data when Open Food Facts API fails
  static Map<String, dynamic> _getFallbackProductData(Product product) {
    return {
      'source': 'fallback',
      'data': {
        'product_name': product.name,
        'brands': product.brand,
        'ingredients_text': product.ingredientsJson?['ingredients_text'] ?? '',
        'ingredients': product.ingredientsJson?['ingredients'] ?? [],
      },
      'confidence': 0.3,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get fallback AI analysis when Gemini fails
  static Map<String, dynamic> _getFallbackAIAnalysis(Product product) {
    return {
      'source': 'fallback_ai',
      'data': {
        'safety_assessment': 'Unable to perform AI analysis',
        'ingredients': product.ingredientsJson?['ingredients'] ?? [],
      },
      'confidence': 0.3,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get fallback web data when web research fails
  static Map<String, dynamic> _getFallbackWebData(Product product) {
    return {
      'source': 'fallback_web',
      'data': {
        'ingredients': product.ingredientsJson?['ingredients'] ?? [],
        'certifications': [],
      },
      'confidence': 0.3,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// After presenting results to user, collect feedback and use it for ML/database improvement
  Future<void> handleUserFeedback({
    required String userId,
    required String productId,
    required String feedbackType,
    required Map<String, dynamic> feedbackData,
  }) async {
    await supabaseService.storeUserFeedback(
      userId: userId,
      productId: productId,
      feedbackType: feedbackType,
      feedbackData: feedbackData,
    );
    // TODO: Add logic to trigger ML model/database update based on feedback
    LoggerService.info('User feedback stored and ready for ML/database update.');
  }

  /// Perform comprehensive safety analysis
  static Future<Map<String, dynamic>> performSafetyAnalysis({
    required Product product,
    Map<String, dynamic>? openFoodFactsData,
    Map<String, dynamic>? geminiData,
    Map<String, dynamic>? webResearchData,
    String? requestId,
  }) async {
    int retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        LoggerService.info('Starting safety analysis (RequestID: $requestId)', context: 'performSafetyAnalysis');
        
        // Merge all data sources
        final mergedData = SafeJsonParser.mergeJsonObjects([
          openFoodFactsData ?? {},
          geminiData ?? {},
          webResearchData ?? {},
          {'product_id': product.id},
          {'barcode': product.barcode},
          {'name': product.name},
        ]);

        // Extract and normalize ingredients
        final normalizedIngredients = await _normalizeIngredients(mergedData);
        
        // If no ingredients found, try alternative sources
        if (normalizedIngredients.isEmpty) {
          LoggerService.warning('No ingredients found in primary sources, trying alternatives...', context: 'performSafetyAnalysis');
          final alternativeIngredients = await _extractIngredientsFromAlternativeSources(product);
          if (alternativeIngredients.isNotEmpty) {
            normalizedIngredients.addAll(alternativeIngredients);
          }
        }
        
        // Match against Supabase ingredients table
        final matchedIngredients = await _matchIngredients(normalizedIngredients);
        
        // Analyze ingredient coverage
        final coverage = await _analyzeIngredientCoverage(matchedIngredients);
        
        // Research missing ingredients if needed and coverage is low
        if (coverage['coverage_percentage'] < 80 && normalizedIngredients.isNotEmpty) {
          final missingIngredients = coverage['missing_ingredients'] as List<String>;
          if (missingIngredients.isNotEmpty) {
            LoggerService.info('Researching ${missingIngredients.length} missing ingredients', context: 'performSafetyAnalysis');
            final researchedIngredients = await _researchMissingIngredients(missingIngredients);
            matchedIngredients.addAll(researchedIngredients);
          }
        }
        
        // Apply safety criteria
        final safetyAnalysis = await _applySafetyCriteria(matchedIngredients);
        
        // Validate the analysis results
        if (!_validateAnalysisResults(safetyAnalysis)) {
          throw Exception('Invalid safety analysis results');
        }
        
        // Generate final report
        final finalReport = await _generateFinalReport(
          safetyAnalysis,
          coverage,
          matchedIngredients,
          product,
          requestId ?? '',
        );
        
        LoggerService.info('Safety analysis completed successfully (RequestID: $requestId)', context: 'performSafetyAnalysis');
        return finalReport;
        
      } catch (e) {
        retryCount++;
        LoggerService.error('Safety analysis attempt $retryCount failed: $e (RequestID: $requestId)', context: 'performSafetyAnalysis');
        
        if (retryCount >= maxRetries) {
          LoggerService.warning('All safety analysis attempts failed, generating fallback report (RequestID: $requestId)', context: 'performSafetyAnalysis');
          return _generateFallbackSafetyReport(product, requestId);
        }
        
        // Wait before retry
        await Future.delayed(Duration(seconds: retryCount));
      }
    }
    
    // This should not be reached, but provide fallback
    return _generateFallbackSafetyReport(product, requestId);
  }

  /// Generate fallback safety report when full analysis fails
  static Map<String, dynamic> _generateFallbackSafetyReport(Product product, String? requestId) {
    LoggerService.info('Generating fallback safety report (RequestID: $requestId)', context: '_generateFallbackSafetyReport');
    
    // Provide basic safety assessment based on available product information
    int estimatedScore = 50; // Default neutral score
    String estimatedRating = 'CAUTION';
    List<String> fallbackReasons = ['Limited ingredient data available'];
    
    // Try to extract basic safety indicators from product name/brand
    final productName = product.name.toLowerCase();
    final brand = product.brand?.toLowerCase() ?? '';
    
    // Positive indicators
    if (productName.contains('organic') || brand.contains('organic')) {
      estimatedScore += 10;
      fallbackReasons.add('Product claims to be organic');
    }
    if (productName.contains('natural') || brand.contains('natural')) {
      estimatedScore += 5;
      fallbackReasons.add('Product claims to be natural');
    }
    
    // Negative indicators
    if (productName.contains('artificial') || productName.contains('processed')) {
      estimatedScore -= 10;
      fallbackReasons.add('Product may contain artificial or heavily processed ingredients');
    }
    
    // Determine rating based on score
    if (estimatedScore >= 70) {
      estimatedRating = 'LIKELY_SAFE';
    } else if (estimatedScore >= 40) {
      estimatedRating = 'CAUTION';
    } else {
      estimatedRating = 'REVIEW_NEEDED';
    }
    
    return {
      'safety_score': estimatedScore,
      'safety_rating': estimatedRating,
      'criteria_analysis': {
        'overall_score': estimatedScore,
        'criteria_results': {},
        'violating_ingredients': {},
      },
      'ingredient_analysis': [],
      'ai_verification': 'Fallback analysis - limited data available',
      'data_confidence': 0.3,
      'analysis_timestamp': DateTime.now().toIso8601String(),
      'request_id': requestId ?? '',
      'is_fallback': true,
      'fallback_reasons': fallbackReasons,
      'recommendations': [
        'Manual ingredient review recommended',
        'Consider capturing product ingredient label image',
        'Check manufacturer website for complete ingredient list'
      ],
    };
  }

  /// Generate final safety report
  static Future<Map<String, dynamic>> _generateFinalReport(
    Map<String, dynamic> safetyAnalysis,
    Map<String, dynamic> coverage,
    List<Map<String, dynamic>> matchedIngredients,
    Product product,
    String requestId,
  ) async {
    LoggerService.info('Generating final safety report (RequestID: $requestId)', context: '_generateFinalReport');
    
    // Calculate overall safety score based on ingredients and coverage
    int overallScore = safetyAnalysis['overall_safety_score'] ?? 50;
    
    // Adjust score based on coverage
    final coveragePercentage = coverage['coverage_percentage'] as double;
    if (coveragePercentage < 50) {
      overallScore = (overallScore * 0.8).round(); // Reduce score for low coverage
    }
    
    // Determine safety rating
    String safetyRating;
    if (overallScore >= 80) {
      safetyRating = 'SAFE';
    } else if (overallScore >= 60) {
      safetyRating = 'LIKELY_SAFE';
    } else if (overallScore >= 40) {
      safetyRating = 'CAUTION';
    } else {
      safetyRating = 'UNSAFE';
    }
    
    // Generate recommendations
    final recommendations = <String>[];
    if (coveragePercentage < 80) {
      recommendations.add('Ingredient analysis incomplete - manual review recommended');
    }
    if (overallScore < 60) {
      recommendations.add('Consider alternative products with better safety profiles');
    }
    
    return {
      'safety_score': overallScore,
      'safety_rating': safetyRating,
      'coverage_analysis': coverage,
      'ingredient_analysis': matchedIngredients,
      'criteria_analysis': safetyAnalysis,
      'recommendations': recommendations,
      'data_confidence': _calculateConfidence(coverage, matchedIngredients),
      'analysis_timestamp': DateTime.now().toIso8601String(),
      'request_id': requestId,
      'is_fallback': false,
    };
  }

  /// Calculate analysis confidence based on coverage and matched ingredients
  static double _calculateConfidence(Map<String, dynamic> coverage, List<Map<String, dynamic>> ingredients) {
    final coveragePercentage = coverage['coverage_percentage'] as double;
    final totalIngredients = ingredients.length;
    
    // Base confidence on coverage
    double confidence = coveragePercentage / 100.0;
    
    // Adjust for ingredient count (more ingredients = higher confidence)
    if (totalIngredients >= 5) {
      confidence += 0.1;
    } else if (totalIngredients < 3) {
      confidence -= 0.2;
    }
    
    // Ensure confidence is between 0 and 1
    return confidence.clamp(0.0, 1.0);
  }
} 