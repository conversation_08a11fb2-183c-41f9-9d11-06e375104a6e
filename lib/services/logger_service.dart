import 'package:logger/logger.dart';

class LoggerService {
  static final Logger _logger = Logger(
    printer: Pretty<PERSON>rinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 80,
      colors: true,
      printEmojis: true,
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );

  static void info(String message, {String? requestId, String? context}) {
    _logger.i(_format(message, requestId, context));
  }

  static void warning(String message, {String? requestId, String? context}) {
    _logger.w(_format(message, requestId, context));
  }

  static void error(String message, {String? requestId, String? context, dynamic error, StackTrace? stackTrace}) {
    _logger.e(
      _format(message, requestId, context),
      error: error,
      stackTrace: stackTrace,
    );
  }

  static void debug(String message, {String? requestId, String? context}) {
    _logger.d(_format(message, requestId, context));
  }

  static String _format(String message, String? requestId, String? context) {
    final parts = <String>[];
    if (requestId != null) parts.add('[Req:$requestId]');
    if (context != null) parts.add('[$context]');
    parts.add(message);
    return parts.join(' ');
  }
} 