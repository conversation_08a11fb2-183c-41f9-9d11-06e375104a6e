import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'gemini_service.dart';
import 'logger_service.dart';

class WebScrapingService {
  static String get _googleApiKey => dotenv.env['GOOGLE_CUSTOM_SEARCH_KEY'] ?? '';
  static String get _searchEngineId => dotenv.env['SEARCH_ENGINE_ID'] ?? '';
  static const String _customSearchUrl = 'https://www.googleapis.com/customsearch/v1';

  /// This service uses LoggerService for all logging. All print/debugPrint statements are replaced with LoggerService.info/warning/error/debug for structured, context-rich logs.

  /// Scrape product data using Google Custom Search with 5-second timeout
  static Future<Map<String, dynamic>?> scrapeProductData(String barcode) async {
    if (_googleApiKey.isEmpty || _searchEngineId.isEmpty) {
      LoggerService.warning('Google API credentials not configured for web scraping');
      return null;
    }

    try {
      // Search for product using barcode across multiple sources
      final searchQueries = [
        'barcode $barcode product ingredients',
        'UPC $barcode food product nutrition',
        '$barcode product label ingredients list',
      ];

      for (final query in searchQueries) {
        final result = await _performSearch(query);
        if (result != null && result.isNotEmpty) {
          return result;
        }
      }

      return null;
    } catch (e) {
      LoggerService.error('Error in web scraping: $e');
      return null;
    }
  }

  /// Search for product by name when barcode fails
  static Future<Map<String, dynamic>?> searchProductByName(String productName) async {
    if (_googleApiKey.isEmpty || _searchEngineId.isEmpty) {
      LoggerService.warning('Google API credentials not configured for web scraping');
      return null;
    }

    try {
      final query = '$productName ingredients nutrition facts';
      return await _performSearch(query);
    } catch (e) {
      LoggerService.error('Error searching product by name: $e');
      return null;
    }
  }

  /// Perform Google Custom Search with timeout
  static Future<Map<String, dynamic>?> _performSearch(String query) async {
    try {
      final url = Uri.parse(_customSearchUrl).replace(queryParameters: {
        'key': _googleApiKey,
        'cx': _searchEngineId,
        'q': query,
        'num': '5', // Search top 5 results
        'safe': 'active',
      });

      final response = await http.get(url).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _extractProductInfo(data);
      }

      return null;
    } catch (e) {
      LoggerService.error('Search request timed out or failed: $e');
      return null;
    }
  }

  /// Extract product information from search results
  static Map<String, dynamic>? _extractProductInfo(Map<String, dynamic> searchData) {
    try {
      final items = searchData['items'] as List?;
      if (items == null || items.isEmpty) return null;

      Map<String, dynamic> extractedData = {};
      
      for (final item in items) {
        final title = item['title'] as String? ?? '';
        final snippet = item['snippet'] as String? ?? '';
        final link = item['link'] as String? ?? '';

        // Extract product name from title
        if (extractedData['product_name'] == null) {
          extractedData['product_name'] = _extractProductName(title);
        }

        // Extract brand information
        if (extractedData['brands'] == null) {
          extractedData['brands'] = _extractBrand(title, snippet);
        }

        // Extract ingredients from snippet
        final ingredients = _extractIngredients(snippet);
        if (ingredients.isNotEmpty) {
          extractedData['ingredients'] = ingredients;
          extractedData['ingredients_text'] = ingredients.join(', ');
        }

        // Extract nutrition information
        final nutrition = _extractNutritionInfo(snippet);
        if (nutrition.isNotEmpty) {
          extractedData['nutriments'] = nutrition;
        }

        // Store source URL
        if (extractedData['source_urls'] == null) {
          extractedData['source_urls'] = <String>[];
        }
        (extractedData['source_urls'] as List<String>).add(link);

        // If we have enough data, break
        if (_hasProductData(extractedData)) {
          break;
        }
      }

      return extractedData.isNotEmpty ? extractedData : null;
    } catch (e) {
      LoggerService.error('Error extracting product info: $e');
      return null;
    }
  }

  /// Extract product name from search title
  static String? _extractProductName(String title) {
    // Remove common suffixes and clean up
    final cleaned = title
        .replaceAll(RegExp(r'\s*-\s*(Amazon|Walmart|Target|Grocery|Store).*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s*\|\s*.*'), '')
        .replaceAll(RegExp(r'\s*\.\.\.*'), '')
        .trim();

    return cleaned.isNotEmpty ? cleaned : null;
  }

  /// Extract brand from title or snippet
  static String? _extractBrand(String title, String snippet) {
    // Common brand patterns
    final brandPatterns = [
      RegExp(r'by\s+([A-Z][a-zA-Z\s&]+)', caseSensitive: false),
      RegExp(r'([A-Z][a-zA-Z\s&]+)\s+brand', caseSensitive: false),
      RegExp(r'^([A-Z][a-zA-Z\s&]+)\s+', caseSensitive: false),
    ];

    final text = '$title $snippet';
    for (final pattern in brandPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final brand = match.group(1)?.trim();
        if (brand != null && brand.length > 1 && brand.length < 50) {
          return brand;
        }
      }
    }

    return null;
  }

  /// Extract ingredients from text
  static List<String> _extractIngredients(String text) {
    final ingredientPatterns = [
      RegExp(r'ingredients?\s*:?\s*([^.]+)', caseSensitive: false),
      RegExp(r'contains?\s*:?\s*([^.]+)', caseSensitive: false),
      RegExp(r'made\s+with\s*:?\s*([^.]+)', caseSensitive: false),
    ];

    for (final pattern in ingredientPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final ingredientsText = match.group(1)?.trim();
        if (ingredientsText != null && ingredientsText.isNotEmpty) {
          return ingredientsText
              .split(RegExp(r'[,;]+'))
              .map((ingredient) => ingredient.trim())
              .where((ingredient) => ingredient.isNotEmpty && ingredient.length > 1)
              .take(20) // Limit to 20 ingredients
              .toList();
        }
      }
    }

    return [];
  }

  /// Extract nutrition information from text
  static Map<String, dynamic> _extractNutritionInfo(String text) {
    final nutrition = <String, dynamic>{};

    // Nutrition patterns
    final nutritionPatterns = {
      'calories': RegExp(r'(\d+)\s*calories?', caseSensitive: false),
      'protein': RegExp(r'protein\s*:?\s*(\d+(?:\.\d+)?)\s*g', caseSensitive: false),
      'fat': RegExp(r'(?:total\s+)?fat\s*:?\s*(\d+(?:\.\d+)?)\s*g', caseSensitive: false),
      'carbohydrates': RegExp(r'carb(?:ohydrate)?s?\s*:?\s*(\d+(?:\.\d+)?)\s*g', caseSensitive: false),
      'sodium': RegExp(r'sodium\s*:?\s*(\d+(?:\.\d+)?)\s*mg', caseSensitive: false),
      'sugar': RegExp(r'sugar\s*:?\s*(\d+(?:\.\d+)?)\s*g', caseSensitive: false),
    };

    for (final entry in nutritionPatterns.entries) {
      final match = entry.value.firstMatch(text);
      if (match != null) {
        final value = match.group(1);
        if (value != null) {
          nutrition[entry.key] = entry.key == 'calories' ? int.tryParse(value) : double.tryParse(value);
        }
      }
    }

    return nutrition;
  }

  /// Check if we have minimum required product data
  static bool _hasProductData(Map<String, dynamic> data) {
    return data['product_name'] != null && 
           (data['ingredients'] != null || data['nutriments'] != null);
  }

  /// Get search suggestions for product alternatives
  static Future<List<Map<String, dynamic>>> getProductAlternatives(
    String productName,
    List<String> avoidIngredients,
  ) async {
    if (_googleApiKey.isEmpty || _searchEngineId.isEmpty) {
      return [];
    }

    try {
      final avoidList = avoidIngredients.take(3).join(' ');
      final query = 'healthy alternative to $productName without $avoidList';
      
      final url = Uri.parse(_customSearchUrl).replace(queryParameters: {
        'key': _googleApiKey,
        'cx': _searchEngineId,
        'q': query,
        'num': '5',
        'safe': 'active',
      });

      final response = await http.get(url).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _extractAlternatives(data);
      }

      return [];
    } catch (e) {
      LoggerService.error('Error getting product alternatives: $e');
      return [];
    }
  }

  /// Extract alternative products from search results
  static List<Map<String, dynamic>> _extractAlternatives(Map<String, dynamic> searchData) {
    try {
      final items = searchData['items'] as List?;
      if (items == null || items.isEmpty) return [];

      final alternatives = <Map<String, dynamic>>[];

      for (final item in items) {
        final title = item['title'] as String? ?? '';
        final snippet = item['snippet'] as String? ?? '';
        final link = item['link'] as String? ?? '';

        final productName = _extractProductName(title);
        if (productName != null) {
          alternatives.add({
            'name': productName,
            'description': snippet.length > 150 ? '${snippet.substring(0, 150)}...' : snippet,
            'source_url': link,
            'confidence': 0.7, // Medium confidence for web-scraped alternatives
          });
        }

        if (alternatives.length >= 5) break;
      }

      return alternatives;
    } catch (e) {
      LoggerService.error('Error extracting alternatives: $e');
      return [];
    }
  }

  /// Use Gemini AI to scrape product info from web
  static Future<Map<String, dynamic>?> scrapeProductInfoWithGemini(String barcode) async {
    try {
      // First check if we can find product information from conventional scraping
      final conventionalData = await scrapeProductData(barcode);
      if (conventionalData != null && conventionalData.isNotEmpty) {
        LoggerService.info('Found product data through conventional web scraping for barcode: $barcode');
        return conventionalData;
      }
      
      // If conventional scraping fails, use Gemini AI to find and extract product information
      LoggerService.info('Conventional scraping failed, using Gemini AI for barcode: $barcode');
      final response = await GeminiService.searchProductInformation('Unknown Product', barcode);
      
      if (response != null) {
        LoggerService.info('Successfully extracted product info using Gemini AI for barcode: $barcode');
        return {
          'product_name': response['product_name'],
          'brand': response['brand'],
          'ingredients_text': response['ingredients_text'],
          'ingredients': response['ingredients'] ?? [],
          'nutrition_facts': response['nutrition_facts'] ?? {},
          'image_url': response['image_url'],
          'source': 'gemini_ai',
        };
      }
      
      LoggerService.info('Gemini AI extraction failed for barcode: $barcode');
      return null;
    } catch (e) {
      LoggerService.error('Error in Gemini web scraping: $e');
      return null;
    }
  }

  /// Research product ingredients comprehensively using search keywords
  static Future<Map<String, dynamic>> researchProductIngredients({
    required String searchQuery,
    required String requestId,
    int resultLimit = 5,
  }) async {
    LoggerService.info('Starting web research for: $searchQuery (RequestID: $requestId)');
    
    if (_googleApiKey.isEmpty || _searchEngineId.isEmpty) {
      LoggerService.warning('Google API credentials not configured for web research (RequestID: $requestId)');
      return _fallbackToGeminiResearch(searchQuery, requestId);
    }
    
    try {
      // Create optimized search queries for ingredient research
      final List<String> searchQueries = [
        '$searchQuery ingredients list',
        '$searchQuery nutrition facts label',
        '$searchQuery product information ingredients',
      ];
      
      // Aggregate results from multiple search queries
      final Map<String, dynamic> aggregatedResults = {
        'ingredients': <String>{},
        'ingredients_text': '',
        'nutrition_facts': <String, dynamic>{},
        'certifications': <String>{},
        'health_claims': <String>{},
      };
      
      int successfulSearches = 0;
      
      // Execute multiple searches in parallel for efficiency
      final searchFutures = searchQueries.map((query) => _executeIngredientSearch(query, resultLimit));
      final searchResults = await Future.wait(searchFutures);
      
      // Process and merge all search results
      for (final result in searchResults) {
        if (result != null && result.isNotEmpty) {
          successfulSearches++;
          
          // Merge ingredients
          if (result['ingredients'] is List) {
            aggregatedResults['ingredients']
                .addAll((result['ingredients'] as List).map((i) => i.toString()));
          }
          
          // Update ingredients text, keeping the longest version
          if (result['ingredients_text'] is String && 
              (result['ingredients_text'] as String).length > aggregatedResults['ingredients_text'].length) {
            aggregatedResults['ingredients_text'] = result['ingredients_text'];
          }
          
          // Merge nutrition facts
          if (result['nutrition_facts'] is Map) {
            aggregatedResults['nutrition_facts'] = {
              ...aggregatedResults['nutrition_facts'] as Map,
              ...(result['nutrition_facts'] as Map),
            };
          }
          
          // Merge certifications
          if (result['certifications'] is List) {
            aggregatedResults['certifications']
                .addAll((result['certifications'] as List).map((c) => c.toString()));
          }
          
          // Merge health claims
          if (result['health_claims'] is List) {
            aggregatedResults['health_claims']
                .addAll((result['health_claims'] as List).map((c) => c.toString()));
          }
        }
      }
      
      // Convert Set to List for JSON serialization
      final Map<String, dynamic> finalResults = {
        'ingredients': (aggregatedResults['ingredients'] as Set<String>).toList(),
        'ingredients_text': aggregatedResults['ingredients_text'],
        'nutrition_facts': aggregatedResults['nutrition_facts'],
        'certifications': (aggregatedResults['certifications'] as Set<String>).toList(),
        'health_claims': (aggregatedResults['health_claims'] as Set<String>).toList(),
      };
      
      // If web research was insufficient, fallback to Gemini AI
      if (successfulSearches == 0 || finalResults['ingredients'].isEmpty) {
        LoggerService.warning('Web research yielded insufficient results, falling back to Gemini (RequestID: $requestId)');
        return _fallbackToGeminiResearch(searchQuery, requestId);
      }
      
      LoggerService.info('Web research complete: Found ${finalResults['ingredients'].length} ingredients (RequestID: $requestId)');
      return finalResults;
      
    } catch (e) {
      LoggerService.error('Error in web research: $e (RequestID: $requestId)');
      return _fallbackToGeminiResearch(searchQuery, requestId);
    }
  }
  
  /// Execute a single search for ingredient information
  static Future<Map<String, dynamic>?> _executeIngredientSearch(String query, int resultLimit) async {
    try {
      final url = Uri.parse(_customSearchUrl).replace(queryParameters: {
        'key': _googleApiKey,
        'cx': _searchEngineId,
        'q': query,
        'num': resultLimit.toString(),
        'safe': 'active',
      });
      
      final response = await http.get(url).timeout(const Duration(seconds: 8));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _extractDetailedProductInfo(data);
      }
      
      return null;
    } catch (e) {
      LoggerService.error('Search request failed: $e');
      return null;
    }
  }
  
  /// Extract detailed product information from search results
  static Map<String, dynamic> _extractDetailedProductInfo(Map<String, dynamic> searchData) {
    final Map<String, dynamic> extractedData = {
      'ingredients': <String>[],
      'ingredients_text': '',
      'nutrition_facts': <String, dynamic>{},
      'certifications': <String>[],
      'health_claims': <String>[],
    };
    
    try {
      final items = searchData['items'] as List?;
      if (items == null || items.isEmpty) return extractedData;
      
      // Extract from search snippets
      for (final item in items) {
        final title = item['title'] as String? ?? '';
        final snippet = item['snippet'] as String? ?? '';
        final link = item['link'] as String? ?? '';
        
        // Extract ingredients list
        final ingredients = _extractIngredients(snippet);
        if (ingredients.isNotEmpty) {
          (extractedData['ingredients'] as List<String>).addAll(ingredients);
          
          // Keep the longest ingredients text
          if (extractedData['ingredients_text'].toString().length < snippet.length) {
            extractedData['ingredients_text'] = snippet;
          }
        }
        
        // Extract nutrition information
        final nutrition = _extractNutritionInfo(snippet);
        if (nutrition.isNotEmpty) {
          extractedData['nutrition_facts'] = {
            ...(extractedData['nutrition_facts'] as Map<String, dynamic>),
            ...nutrition,
          };
        }
        
        // Extract certifications
        final certifications = _extractCertifications(title + ' ' + snippet);
        if (certifications.isNotEmpty) {
          (extractedData['certifications'] as List<String>).addAll(certifications);
        }
        
        // Extract health claims
        final healthClaims = _extractHealthClaims(snippet);
        if (healthClaims.isNotEmpty) {
          (extractedData['health_claims'] as List<String>).addAll(healthClaims);
        }
      }
      
      // Remove duplicates from lists
      extractedData['ingredients'] = _removeDuplicates(extractedData['ingredients'] as List<String>);
      extractedData['certifications'] = _removeDuplicates(extractedData['certifications'] as List<String>);
      extractedData['health_claims'] = _removeDuplicates(extractedData['health_claims'] as List<String>);
      
      return extractedData;
    } catch (e) {
      LoggerService.error('Error extracting detailed product info: $e');
      return extractedData;
    }
  }
  
  /// Extract product certifications from text
  static List<String> _extractCertifications(String text) {
    final certificationPatterns = [
      RegExp(r'(USDA\s+Organic)', caseSensitive: false),
      RegExp(r'(Non-GMO|Non GMO)\s+Project\s+Verified', caseSensitive: false),
      RegExp(r'(Gluten-Free|Gluten Free)\s+Certified', caseSensitive: false),
      RegExp(r'(Vegan|Vegetarian)\s+Certified', caseSensitive: false),
      RegExp(r'(Kosher|Halal)\s+Certified', caseSensitive: false),
      RegExp(r'Fair\s+Trade\s+Certified', caseSensitive: false),
      RegExp(r'Certified\s+(B\s+Corp)', caseSensitive: false),
      RegExp(r'(Rainforest\s+Alliance)\s+Certified', caseSensitive: false),
    ];
    
    final List<String> certifications = [];
    
    for (final pattern in certificationPatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final cert = match.group(0)?.trim();
        if (cert != null && cert.isNotEmpty) {
          certifications.add(cert);
        }
      }
    }
    
    return certifications;
  }
  
  /// Extract health claims from text
  static List<String> _extractHealthClaims(String text) {
    final claimPatterns = [
      RegExp(r'(No\s+artificial\s+(?:flavors|colors|preservatives|sweeteners))', caseSensitive: false),
      RegExp(r'(No\s+added\s+sugars?)', caseSensitive: false),
      RegExp(r'(Low\s+sodium|Sodium\s+free)', caseSensitive: false),
      RegExp(r'(High\s+in\s+(?:protein|fiber|vitamins?))', caseSensitive: false),
      RegExp(r'(Dairy\s+free|Lactose\s+free)', caseSensitive: false),
      RegExp(r'(Gluten\s+free)', caseSensitive: false),
      RegExp(r'(No\s+trans\s+fats?)', caseSensitive: false),
      RegExp(r'(Low\s+calorie|Low\s+carb)', caseSensitive: false),
      RegExp(r'(Organic|All\s+natural)', caseSensitive: false),
    ];
    
    final List<String> claims = [];
    
    for (final pattern in claimPatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final claim = match.group(0)?.trim();
        if (claim != null && claim.isNotEmpty) {
          claims.add(claim);
        }
      }
    }
    
    return claims;
  }
  
  /// Remove duplicates from string list while preserving order
  static List<String> _removeDuplicates(List<String> list) {
    final seen = <String>{};
    final result = <String>[];
    
    for (final item in list) {
      final lowerItem = item.toLowerCase();
      if (!seen.contains(lowerItem)) {
        seen.add(lowerItem);
        result.add(item);
      }
    }
    
    return result;
  }
  
  /// Fallback to Gemini AI when web research fails
  static Future<Map<String, dynamic>> _fallbackToGeminiResearch(
    String searchQuery, 
    String requestId
  ) async {
    LoggerService.info('Using Gemini AI for research fallback (RequestID: $requestId)');
    
    try {
      // Extract product name and main terms from search query
      final List<String> terms = searchQuery.split(' ')
          .where((term) => term.toLowerCase() != 'ingredients' && 
                          term.toLowerCase() != 'nutrition' &&
                          term.toLowerCase() != 'facts')
          .toList();
      
      // Use Gemini to research the product - complex reasoning task using Pro model
      final Map<String, dynamic> geminiResults = await GeminiService.researchProduct(terms.join(' '));
      
      // Transform Gemini results to match web research format and ensure proper types
      return {
        'ingredients': geminiResults.containsKey('ingredients') 
          ? List<String>.from(geminiResults['ingredients'] ?? []) 
          : <String>[],
        'ingredients_text': geminiResults['ingredients_text'] ?? '',
        'nutrition_facts': geminiResults['nutrition_facts'] ?? {},
        'certifications': geminiResults.containsKey('certifications')
          ? List<String>.from(geminiResults['certifications'] ?? [])
          : <String>[],
        'health_claims': geminiResults.containsKey('health_claims')
          ? List<String>.from(geminiResults['health_claims'] ?? [])
          : <String>[],
        'data_sources': ['gemini_ai_pro_research'],
      };
    } catch (e) {
      LoggerService.error('Error in Gemini fallback research: $e (RequestID: $requestId)');
      return {
        'ingredients': <String>[],
        'ingredients_text': '',
        'nutrition_facts': {},
        'certifications': <String>[],
        'health_claims': <String>[],
      };
    }
  }

  /// Search for product information using barcode
  static Future<Map<String, dynamic>> searchProduct(String barcode) async {
    try {
      // First try to get data from Open Food Facts
      final offData = await _getOpenFoodFactsData(barcode);
      if (offData != null && offData['status'] == 1) {
        return {
          'source': 'open_food_facts',
          'data': offData['product'],
          'confidence': 0.9,
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      // If Open Food Facts fails, try web scraping
      final webData = await _scrapeProductInfo(barcode);
      if (webData.isNotEmpty) {
        return {
          'source': 'web_scraping',
          'data': webData,
          'confidence': 0.7,
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      // If both fail, return empty data
      return {
        'source': 'fallback',
        'data': {},
        'confidence': 0.3,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggerService.error('Error in product search: $e');
      return {
        'source': 'error',
        'data': {},
        'confidence': 0.0,
        'timestamp': DateTime.now().toIso8601String(),
        'error': e.toString(),
      };
    }
  }

  /// Get data from Open Food Facts
  static Future<Map<String, dynamic>?> _getOpenFoodFactsData(String barcode) async {
    try {
      final response = await http.get(
        Uri.parse('https://world.openfoodfacts.org/api/v0/product/$barcode.json'),
        headers: {'User-Agent': 'SafeScan - Product Safety App'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      LoggerService.error('Error fetching from Open Food Facts: $e');
      return null;
    }
  }

  /// Scrape product information from web
  static Future<Map<String, dynamic>> _scrapeProductInfo(String barcode) async {
    try {
      // Implement web scraping logic here
      // For now, return empty data
      return {};
    } catch (e) {
      LoggerService.error('Error scraping product info: $e');
      return {};
    }
  }
} 