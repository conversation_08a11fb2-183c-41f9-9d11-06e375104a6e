import 'dart:convert';
import 'dart:io';
import 'dart:async'; // Add TimeoutException import
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'logger_service.dart';

/// This service uses LoggerService for all logging. All print/debugPrint statements are replaced with LoggerService.info/warning/error/debug for structured, context-rich logs.

/// Service for analyzing product images using AI
class ImageAnalysisService {
  static String get _apiKey => dotenv.env['GEMINI_API_KEY'] ?? '';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  
  // Vision model as per requirements
  static const String _visionModel = 'gemini-2.0-flash'; // Fast, versatile with vision capabilities
  
  // Track rate limiting
  static bool _isVisionModelRateLimited = false;
  
  /// Analyze a product image to extract information
  static Future<Map<String, dynamic>?> analyzeProductImage(String imagePath) async {
    // Check if the image file exists
    final imageFile = File(imagePath);
    if (!await imageFile.exists()) {
      LoggerService.info('Image file does not exist: $imagePath');
      return {
        'error': 'Image file not found',
        'product_name': 'Unknown Product',
        'brand': 'Unknown',
        'ingredients': [],
        'nutrition_facts': {},
      };
    }

    try {
      // Check if Gemini API is configured
      if (_apiKey.isEmpty) {
        LoggerService.info('Gemini API key not configured for image analysis');
        return {
          'product_name': 'Product from Image',
          'brand': 'Unknown',
          'ingredients': [],
          'data_sources': ['image_capture_fallback'],
          'error': 'API key not configured',
        };
      }

      // Convert image to base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      // Create the prompt for image analysis
      final prompt = '''
Analyze this food product image and extract the following information:
1. Product name
2. Brand
3. List of ingredients (in separate array format)
4. Nutrition facts

Focus on any text visible on the packaging, especially ingredient lists.
''';

      try {
        // Call the Gemini API with the image
        final response = await _callGeminiVisionAPI(prompt, base64Image);
        
        // Process the response to extract structured data
        final data = _extractProductInfo(response);
        return data;
      } catch (apiError) {
        LoggerService.error('Error in Gemini API call: $apiError');
        // Provide a basic fallback response
        return {
          'product_name': 'Product from Image',
          'brand': 'Unknown',
          'ingredients': [],
          'data_sources': ['image_capture_fallback'],
          'error': apiError.toString(),
        };
      }
    } catch (e) {
      LoggerService.error('Error in image analysis: $e');
      // Return a minimal response to prevent app crash
      return {
        'product_name': 'Scanned Product',
        'brand': 'Unknown',
        'ingredients': [],
        'data_sources': ['image_capture_fallback'],
        'error': e.toString(),
      };
    }
  }

  /// Extract ingredient list from product image
  static Future<List<String>?> extractIngredientsFromImage(String imagePath) async {
    if (_apiKey.isEmpty) {
      throw Exception('Gemini API key not configured');
    }

    try {
      final imageBytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(imageBytes);
      
      final prompt = _buildIngredientExtractionPrompt();
      final response = await _callGeminiVisionAPI(prompt, base64Image);
      
      return _parseIngredientExtractionResponse(response);
    } catch (e) {
      LoggerService.error('Error extracting ingredients from image: $e');
      rethrow;
    }
  }

  /// Extract nutrition facts from product image
  static Future<Map<String, dynamic>?> extractNutritionFacts(String imagePath) async {
    if (_apiKey.isEmpty) {
      throw Exception('Gemini API key not configured');
    }

    try {
      final imageBytes = await File(imagePath).readAsBytes();
      final base64Image = base64Encode(imageBytes);
      
      final prompt = _buildNutritionExtractionPrompt();
      final response = await _callGeminiVisionAPI(prompt, base64Image);
      
      return _parseNutritionFactsResponse(response);
    } catch (e) {
      LoggerService.error('Error extracting nutrition facts from image: $e');
      rethrow;
    }
  }

  /// Call Gemini Vision API
  static Future<String> _callGeminiVisionAPI(String prompt, String base64Image, {int retryCount = 0}) async {
    final url = Uri.parse('$_baseUrl/models/$_visionModel:generateContent?key=$_apiKey');
    
    final payload = json.encode({
      'contents': [
        {
          'role': 'user',
          'parts': [
            {'text': prompt},
            {
              'inline_data': {
                'mime_type': 'image/jpeg',
                'data': base64Image
              }
            }
          ]
        }
      ],
      'generationConfig': {
        'temperature': 0.4,
        'topP': 0.95,
        'topK': 40,
        'maxOutputTokens': 2048,
      }
    });

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: payload,
      ).timeout(const Duration(seconds: 30)); // 30 second timeout for image processing
  
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final candidates = data['candidates'] as List<dynamic>;
        
        if (candidates.isNotEmpty) {
          final content = candidates[0]['content'];
          final parts = content['parts'] as List<dynamic>;
          
          if (parts.isNotEmpty) {
            // If previously rate limited, reset after some time
            if (_isVisionModelRateLimited) {
              Future.delayed(const Duration(minutes: 5), () {
                _isVisionModelRateLimited = false;
              });
            }
            
            return parts[0]['text'] as String;
          }
        }
        
        throw Exception('No text content in response');
      } else if (response.statusCode == 429) {
        // Rate limit exceeded
        LoggerService.warning('Gemini Vision API rate limit exceeded (429). Response: ${response.body}');
        _isVisionModelRateLimited = true;
        
        // If rate limited, use exponential backoff
        if (retryCount < 2) {
          // Exponential backoff: wait longer for each retry
          final backoffSeconds = (1 << retryCount) * 3;
          LoggerService.info('Retrying vision API in $backoffSeconds seconds...');
          
          await Future.delayed(Duration(seconds: backoffSeconds));
          return _callGeminiVisionAPI(prompt, base64Image, retryCount: retryCount + 1);
        }
        
        // If we've exhausted retries, use fallback response
        LoggerService.warning('Rate limit exceeded on vision model, using fallback');
        return _generateFallbackImageResponse();
      } else {
        // Other API errors
        LoggerService.error('Gemini Vision API error: ${response.statusCode} - ${response.body}');
        
        // If error happens once, try again
        if (retryCount < 1) {
          await Future.delayed(const Duration(seconds: 2));
          return _callGeminiVisionAPI(prompt, base64Image, retryCount: retryCount + 1);
        }
        
        // After retry, use fallback
        return _generateFallbackImageResponse();
      }
    } catch (e) {
      // Network/timeout errors
      LoggerService.error('Error calling Gemini Vision API: $e');
      
      // Retry once on network errors
      if (retryCount < 1) {
        await Future.delayed(const Duration(seconds: 2));
        return _callGeminiVisionAPI(prompt, base64Image, retryCount: retryCount + 1);
      }
      
      // After retry, use fallback
      return _generateFallbackImageResponse();
    }
  }

  /// Generate a fallback response when vision API fails
  static String _generateFallbackImageResponse() {
    return '''
{
  "product_name": "Product from Image",
  "brand": "Unknown Brand",
  "ingredients": [],
  "nutrition_facts": {},
  "error": "Image analysis failed - please try scanning the barcode or entering product details manually"
}
''';
  }

  static String _buildImageAnalysisPrompt() {
    return '''
Analyze this food product image and extract all available information.

Focus on identifying:
1. Product name and brand
2. Ingredient list (if visible)
3. Nutrition facts (if visible)
4. Key product claims (organic, gluten-free, etc.)
5. Serving size information
6. Any allergen warnings

Return the information in this exact JSON format:
{
  "product_name": "name if visible",
  "brand": "brand name if visible", 
  "ingredients": ["ingredient1", "ingredient2", "..."],
  "nutrition_facts": {
    "serving_size": "value",
    "calories": "value",
    "total_fat": "value",
    "sodium": "value",
    "total_carbs": "value",
    "protein": "value"
  },
  "claims": ["organic", "gluten-free", "..."],
  "allergens": ["contains milk", "..."],
  "confidence": 0.0-1.0,
  "ocr_quality": "high|medium|low"
}

If information is not clearly visible, use null for that field.
Be very accurate with ingredient names and spelling.
''';
  }

  static String _buildIngredientExtractionPrompt() {
    return '''
Extract ONLY the ingredient list from this food product image.

Look for text that says "Ingredients:", "Contains:", or similar labels.
Extract each ingredient exactly as written on the package.

Return a JSON array of ingredients:
{
  "ingredients": ["ingredient1", "ingredient2", "ingredient3"],
  "confidence": 0.0-1.0,
  "source_text": "raw text found on package"
}

Be extremely accurate with spelling and include everything in the ingredient list.
If no ingredient list is visible, return an empty array.
''';
  }

  static String _buildNutritionExtractionPrompt() {
    return '''
Extract nutrition facts from this food product image.

Look for the standard nutrition facts label or any nutritional information.

Return in this JSON format:
{
  "serving_size": "value with units",
  "servings_per_container": "number",
  "calories": "number",
  "total_fat": "value in grams",
  "saturated_fat": "value in grams",
  "trans_fat": "value in grams", 
  "cholesterol": "value in mg",
  "sodium": "value in mg",
  "total_carbohydrates": "value in grams",
  "dietary_fiber": "value in grams",
  "total_sugars": "value in grams",
  "added_sugars": "value in grams",
  "protein": "value in grams",
  "vitamin_d": "value",
  "calcium": "value",
  "iron": "value",
  "potassium": "value",
  "confidence": 0.0-1.0
}

Use null for any values not clearly visible.
Include units exactly as shown on the label.
''';
  }

  static Map<String, dynamic>? _parseImageAnalysisResponse(String response) {
    try {
      // Clean the response and extract JSON
      final cleanedResponse = _extractJsonFromResponse(response);
      return json.decode(cleanedResponse);
    } catch (e) {
      LoggerService.error('Error parsing image analysis response: $e');
      return null;
    }
  }

  static List<String>? _parseIngredientExtractionResponse(String response) {
    try {
      final cleanedResponse = _extractJsonFromResponse(response);
      final data = json.decode(cleanedResponse);
      return List<String>.from(data['ingredients'] ?? []);
    } catch (e) {
      LoggerService.error('Error parsing ingredient extraction response: $e');
      return null;
    }
  }

  static Map<String, dynamic>? _parseNutritionFactsResponse(String response) {
    try {
      final cleanedResponse = _extractJsonFromResponse(response);
      return json.decode(cleanedResponse);
    } catch (e) {
      LoggerService.error('Error parsing nutrition facts response: $e');
      return null;
    }
  }

  static String _extractJsonFromResponse(String response) {
    // Remove markdown code blocks if present
    String cleaned = response.trim();
    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.substring(7);
    } else if (cleaned.startsWith('```')) {
      cleaned = cleaned.substring(3);
    }
    
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.substring(0, cleaned.length - 3);
    }
    
    return cleaned.trim();
  }

  /// Validate extracted data quality
  static bool validateExtractedData(Map<String, dynamic> data) {
    final confidence = data['confidence'] as double? ?? 0.0;
    final hasProductName = data['product_name'] != null && 
                          (data['product_name'] as String).isNotEmpty;
    final hasIngredients = data['ingredients'] != null && 
                          (data['ingredients'] as List).isNotEmpty;
    
    return confidence > 0.6 && (hasProductName || hasIngredients);
  }

  /// Get confidence score for extracted data
  static double getDataConfidence(Map<String, dynamic> data) {
    double baseConfidence = data['confidence'] as double? ?? 0.0;
    
    // Adjust confidence based on data completeness
    int dataPoints = 0;
    int totalPossiblePoints = 6;
    
    if (data['product_name'] != null) dataPoints++;
    if (data['brand'] != null) dataPoints++;
    if (data['ingredients'] != null && (data['ingredients'] as List).isNotEmpty) dataPoints++;
    if (data['nutrition_facts'] != null) dataPoints++;
    if (data['claims'] != null && (data['claims'] as List).isNotEmpty) dataPoints++;
    if (data['allergens'] != null && (data['allergens'] as List).isNotEmpty) dataPoints++;
    
    double completenessScore = dataPoints / totalPossiblePoints;
    
    // Weight the final confidence
    return (baseConfidence * 0.7) + (completenessScore * 0.3);
  }

  /// Extract structured product info from Gemini's response
  static Map<String, dynamic> _extractProductInfo(String response) {
    final result = <String, dynamic>{};
    
    try {
      // First try to parse as JSON
      try {
        final cleanedResponse = _extractJsonFromResponse(response);
        final jsonData = json.decode(cleanedResponse);
        
        // If it's valid JSON, use it directly
        if (jsonData is Map<String, dynamic>) {
          return jsonData;
        }
      } catch (e) {
        // If JSON parsing failed, continue with text extraction
        LoggerService.info('JSON parsing failed, falling back to text extraction: $e');
      }
      
      // Extract product name
      final productNameMatch = RegExp(r'Product name[:\s]+(.*?)(?:\n|$)', caseSensitive: false).firstMatch(response);
      if (productNameMatch != null) {
        result['product_name'] = productNameMatch.group(1)?.trim();
      }
      
      // Extract brand
      final brandMatch = RegExp(r'Brand[:\s]+(.*?)(?:\n|$)', caseSensitive: false).firstMatch(response);
      if (brandMatch != null) {
        result['brand'] = brandMatch.group(1)?.trim();
      }
      
      // Extract ingredients
      final ingredientsList = <String>[];
      
      // Try to find ingredient list section
      final ingredientsSection = RegExp(r'Ingredients(?:[\s:]+)([\s\S]*?)(?:\n\n|$)', caseSensitive: false).firstMatch(response);
      if (ingredientsSection != null && ingredientsSection.group(1) != null) {
        final ingredientsText = ingredientsSection.group(1)!;
        
        // Check if it's formatted as a list with dashes or numbers
        final listItems = RegExp(r'(?:^|\n)[\s-]*([^-\n]+)').allMatches(ingredientsText);
        
        if (listItems.isNotEmpty) {
          for (final match in listItems) {
            final ingredient = match.group(1)?.trim();
            if (ingredient != null && ingredient.isNotEmpty) {
              ingredientsList.add(ingredient);
            }
          }
        } else {
          // If not a list, split by commas
          ingredientsList.addAll(
            ingredientsText
                .split(',')
                .map((i) => i.trim())
                .where((i) => i.isNotEmpty)
          );
        }
      }
      
      result['ingredients'] = ingredientsList;
      
      // Extract nutrition facts (simplified)
      final nutritionFacts = <String, dynamic>{};
      
      final caloriesMatch = RegExp(r'Calories[:\s]+(\d+)', caseSensitive: false).firstMatch(response);
      if (caloriesMatch != null) {
        nutritionFacts['calories'] = caloriesMatch.group(1);
      }
      
      final fatMatch = RegExp(r'(Total Fat|Fat)[:\s]+([\d\.]+)\s*g', caseSensitive: false).firstMatch(response);
      if (fatMatch != null) {
        nutritionFacts['total_fat'] = fatMatch.group(2);
      }
      
      final carbsMatch = RegExp(r'(Total Carbohydrates|Carbohydrates)[:\s]+([\d\.]+)\s*g', caseSensitive: false).firstMatch(response);
      if (carbsMatch != null) {
        nutritionFacts['total_carbs'] = carbsMatch.group(2);
      }
      
      final proteinMatch = RegExp(r'Protein[:\s]+([\d\.]+)\s*g', caseSensitive: false).firstMatch(response);
      if (proteinMatch != null) {
        nutritionFacts['protein'] = proteinMatch.group(1);
      }
      
      result['nutrition_facts'] = nutritionFacts;
      
      // If we couldn't extract a product name, provide a default
      if (result['product_name'] == null || (result['product_name'] as String).isEmpty) {
        result['product_name'] = 'Unknown Product';
      }
      
      // If we couldn't extract a brand, provide a default
      if (result['brand'] == null || (result['brand'] as String).isEmpty) {
        result['brand'] = 'Unknown Brand';
      }
      
      return result;
    } catch (e) {
      LoggerService.error('Error in _extractProductInfo: $e');
      // Return minimal viable data
      return {
        'product_name': 'Unknown Product',
        'brand': 'Unknown Brand',
        'ingredients': <String>[],
        'nutrition_facts': <String, dynamic>{},
      };
    }
  }
} 