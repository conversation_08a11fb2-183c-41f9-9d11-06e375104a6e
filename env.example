# SafeScan Production Environment Variables

# Supabase Configuration (Required)
SUPABASE_URL=https://lwnfzmvqahraputvfkos.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao

# Gemini API (REQUIRED FOR PRODUCTION)
# Get your API key from: https://ai.google.dev/tutorials/setup
GEMINI_API_KEY=AIzaSyBaANwtl55lqBnOhvqHf5BMYeNee2xKoCY

# Gemini Model Configuration 
# SafeScan uses two specific Gemini models:
# 1. gemini-2.0-flash: Fast, versatile model for most operations
#    - Used for: Standard ingredient analysis, image processing, general tasks
#
# 2. gemini-1.5-pro: Advanced model for complex reasoning
#    - Used for: Complex safety assessments, detailed ingredient research, in-depth analyses
#
# The app automatically selects the appropriate model based on task complexity
# and implements intelligent fallback between models if rate limiting occurs.

# Google Cloud API (REQUIRED FOR PRODUCTION)
GOOGLE_VISION_API_KEY=AIzaSyDp6YCFo7cEBOmjPof0CWvC_rxcqQ_pFms

# Google Custom Search (REQUIRED FOR PRODUCTION)
SEARCH_ENGINE_ID=7154d3defaa9a4c35


# OpenFoodFacts Configuration
OPENFOODFACTS_USER_AGENT=SafeScan - AI-Powered Product Safety Scanner
OPENFOODFACTS_API_URL=https://world.openfoodfacts.org/api/v0/product

# App Environment
APP_ENV=production