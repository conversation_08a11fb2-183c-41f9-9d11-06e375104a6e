["/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/images/icon.png", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/images/SafeScan-logo.png", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/images/favicon.png", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/images/adaptive-icon.png", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/images/splash-icon.png", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/Data-not-found.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/HomeScreen-2.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/Cat-Loading-screen.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/Gradient-VOice.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/HomeScreenAnimation.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/SafeScan-blue.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/RippleLoadingScreen.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/animations/Safescan-Green.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/assets/fonts/SpaceMono-Regular.ttf", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/.env", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/packages/material_design_icons_flutter/lib/fonts/materialdesignicons-webfont.ttf", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/AssetManifest.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/AssetManifest.bin", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/FontManifest.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/NOTICES.Z", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/flutter_assets/NativeAssetsManifest.json", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/x86_64/app.so", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/arm64-v8a/app.so", "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/release/armeabi-v7a/app.so"]