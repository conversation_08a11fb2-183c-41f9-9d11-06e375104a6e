{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/targets/android.dart", "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/098c341a26db0aac411ca8b4f8255913/app.dill", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp"], "outputs": ["/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/098c341a26db0aac411ca8b4f8255913/arm64-v8a/app.so"]}