 /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/icon.png /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/SafeScan-logo.png /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/favicon.png /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/adaptive-icon.png /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/splash-icon.png /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Data-not-found.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/HomeScreen-2.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Cat-Loading-screen.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Gradient-VOice.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/HomeScreenAnimation.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/SafeScan-blue.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/RippleLoadingScreen.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Safescan-Green.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/fonts/SpaceMono-Regular.ttf /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/.env /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/packages/material_design_icons_flutter/lib/fonts/materialdesignicons-webfont.ttf /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z /Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/AndroidStudioProjects/Safescan/pubspec.yaml /Users/<USER>/AndroidStudioProjects/Safescan/assets/images/icon.png /Users/<USER>/AndroidStudioProjects/Safescan/assets/images/SafeScan-logo.png /Users/<USER>/AndroidStudioProjects/Safescan/assets/images/favicon.png /Users/<USER>/AndroidStudioProjects/Safescan/assets/images/adaptive-icon.png /Users/<USER>/AndroidStudioProjects/Safescan/assets/images/splash-icon.png /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Data-not-found.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/HomeScreen-2.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Cat-Loading-screen.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Gradient-VOice.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/HomeScreenAnimation.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/SafeScan-blue.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/RippleLoadingScreen.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Safescan-Green.json /Users/<USER>/AndroidStudioProjects/Safescan/assets/fonts/SpaceMono-Regular.ttf /Users/<USER>/AndroidStudioProjects/Safescan/.env /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/fonts/materialdesignicons-webfont.ttf /opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/24601892b32eefa3e669ab6ab2e21a45/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.14.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.11.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-5.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/LICENSE /opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/pkg/sky_engine/LICENSE /opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/LICENSE /Users/<USER>/AndroidStudioProjects/Safescan/DOES_NOT_EXIST_RERUN_FOR_WILDCARD993755036