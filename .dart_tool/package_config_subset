app_links
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib/
app_links_linux
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/
app_links_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/
app_links_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
cached_network_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
camera
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/lib/
camera_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/
camera_avfoundation
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/
camera_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/
camera_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dio
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/
dio_web_adapter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
fl_chart
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/lib/
flutter_cache_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_dotenv
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/
flutter_lints
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/
flutter_slidable
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/lib/
flutter_spinkit
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.2.1/lib/
functions_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/
go_router
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/
gotrue
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/
gtk
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/
hive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/
hive_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
jwt_decode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/lib/
logger
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
lottie
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
material_design_icons_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
octo_image
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
posix
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/lib/
postgrest
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
realtime_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/
retry
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/lib/
riverpod
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/
shared_preferences
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
shimmer
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
state_notifier
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/
storage_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
supabase
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/
supabase_flutter
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/
synchronized
3.8
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yet_another_json_isolate
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib/
safescan_flutter
3.8
file:///Users/<USER>/AndroidStudioProjects/Safescan/
file:///Users/<USER>/AndroidStudioProjects/Safescan/lib/
sky_engine
3.7
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/pkg/sky_engine/
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/
flutter_test
3.7
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_test/
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_web_plugins/
file:///opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_web_plugins/lib/
2
